// Sample data arrays for generating realistic test data
const arabicFirstNames = [
    'أحمد', 'محمد', 'علي', 'حسن', 'حسين', 'عبدالله', 'عبدالرحمن', 'خالد', 'سعد', 'فهد',
    'عمر', 'يوسف', 'إبراهيم', 'عبدالعزيز', 'سلمان', 'فيصل', 'نايف', 'بندر', 'تركي', 'مشعل',
    'فاطمة', 'عائشة', 'خديجة', 'زينب', 'مريم', 'سارة', 'نورا', 'هند', 'ريم', 'لينا',
    'أمل', 'سعاد', 'منى', 'ليلى', 'سلمى', 'دينا', 'رنا', 'هالة', 'نادية', 'سميرة'
];

const arabicFamilyNames = [
    'العتيبي', 'المطيري', 'الدوسري', 'الشمري', 'القحطاني', 'الغامدي', 'الزهراني', 'الحربي', 'العنزي', 'الرشيد',
    'آل سعود', 'آل الشيخ', 'البراك', 'الفايز', 'السديري', 'الثنيان', 'الخالد', 'المبارك', 'الصالح', 'الفهد',
    'محمد', 'أحمد', 'علي', 'حسن', 'إبراهيم', 'عبدالله', 'الأحمد', 'المحمد', 'العلي', 'الحسن'
];

const frenchFirstNames = [
    'Ahmed', 'Mohamed', 'Ali', 'Hassan', 'Hussein', 'Abdullah', 'Abderrahman', 'Khalid', 'Saad', 'Fahd',
    'Omar', 'Youssef', 'Ibrahim', 'Abdelaziz', 'Salman', 'Faisal', 'Naif', 'Bandar', 'Turki', 'Mishaal',
    'Fatima', 'Aicha', 'Khadija', 'Zeinab', 'Mariam', 'Sara', 'Nora', 'Hind', 'Reem', 'Lina',
    'Amal', 'Souad', 'Mona', 'Laila', 'Salma', 'Dina', 'Rana', 'Hala', 'Nadia', 'Samira'
];

const frenchFamilyNames = [
    'Al-Otaibi', 'Al-Mutairi', 'Al-Dosari', 'Al-Shammari', 'Al-Qahtani', 'Al-Ghamdi', 'Al-Zahrani', 'Al-Harbi', 'Al-Anzi', 'Al-Rashid',
    'Al-Saud', 'Al-Sheikh', 'Al-Barrak', 'Al-Fayez', 'Al-Sudairi', 'Al-Thunayan', 'Al-Khalid', 'Al-Mubarak', 'Al-Saleh', 'Al-Fahd',
    'Mohamed', 'Ahmed', 'Ali', 'Hassan', 'Ibrahim', 'Abdullah', 'Al-Ahmed', 'Al-Mohamed', 'Al-Ali', 'Al-Hassan'
];

const cities = [
    { ar: 'الرياض', fr: 'Riyadh' },
    { ar: 'جدة', fr: 'Jeddah' },
    { ar: 'مكة المكرمة', fr: 'Mecca' },
    { ar: 'المدينة المنورة', fr: 'Medina' },
    { ar: 'الدمام', fr: 'Dammam' },
    { ar: 'الطائف', fr: 'Taif' },
    { ar: 'بريدة', fr: 'Buraidah' },
    { ar: 'تبوك', fr: 'Tabuk' },
    { ar: 'القصيم', fr: 'Qassim' },
    { ar: 'حائل', fr: 'Hail' },
    { ar: 'الأحساء', fr: 'Al-Ahsa' },
    { ar: 'ينبع', fr: 'Yanbu' },
    { ar: 'الخبر', fr: 'Khobar' },
    { ar: 'أبها', fr: 'Abha' },
    { ar: 'نجران', fr: 'Najran' }
];

// Global variables
let citizens = JSON.parse(localStorage.getItem('citizens')) || [];
let isGenerating = false;
let startTime = 0;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateStatistics();
    updateImageRecommendation();
});

// Update image recommendation based on record count
function updateImageRecommendation() {
    const recordCount = parseInt(document.getElementById('recordCount').value);
    const recommendationDiv = document.getElementById('imageRecommendation');

    let recommendation = '';
    let recommendedImagePercent = '0';

    if (recordCount <= 100) {
        recommendation = '💡 يمكنك استخدام 100% صور للاختبار الصغير';
        recommendedImagePercent = '100';
    } else if (recordCount <= 1000) {
        recommendation = '💡 يُنصح بـ 25% صور للاختبار المتوسط';
        recommendedImagePercent = '25';
    } else if (recordCount <= 5000) {
        recommendation = '⚠️ يُنصح بـ 10% صور أو أقل لتجنب امتلاء التخزين';
        recommendedImagePercent = '10';
    } else {
        recommendation = '🚫 يُنصح بعدم استخدام صور للاختبارات الكبيرة';
        recommendedImagePercent = '0';
    }

    recommendationDiv.textContent = recommendation;

    // Auto-select recommended percentage
    document.getElementById('withImages').value = recommendedImagePercent;
}

// Generate random data
function getRandomItem(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomDate(start, end) {
    const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    return date.toISOString().split('T')[0];
}

function getRandomHijriDate() {
    const hijriMonths = ['محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
    const day = Math.floor(Math.random() * 29) + 1;
    const month = getRandomItem(hijriMonths);
    const year = Math.floor(Math.random() * 50) + 1400; // 1400-1450
    return `${day} ${month} ${year}`;
}

function generateActNumber(index, year) {
    return `${index + 1}/${year}`;
}

// Generate a simple test certificate image (very small for testing)
function generateTestCertificateImage() {
    const canvas = document.createElement('canvas');
    canvas.width = 200; // Reduced size
    canvas.height = 150; // Reduced size
    const ctx = canvas.getContext('2d');

    // Create a simple certificate-like image
    const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6'];
    ctx.fillStyle = getRandomItem(colors);
    ctx.fillRect(0, 0, 200, 150);

    ctx.fillStyle = 'white';
    ctx.font = '12px Arial'; // Smaller font
    ctx.textAlign = 'center';
    ctx.fillText('شهادة تجريبية', 100, 70);
    ctx.fillText('Test Certificate', 100, 90);

    return canvas.toDataURL('image/jpeg', 0.3); // Very low quality for smallest size
}

// Generate single citizen record
function generateCitizen(index, year, withImage = false) {
    const gender = Math.random() > 0.5 ? 'ذكر' : 'أنثى';
    const city = getRandomItem(cities);

    const citizen = {
        id: Date.now() + index,
        firstNameAr: getRandomItem(arabicFirstNames),
        firstNameFr: getRandomItem(frenchFirstNames),
        familyNameAr: getRandomItem(arabicFamilyNames),
        familyNameFr: getRandomItem(frenchFamilyNames),
        birthPlaceAr: city.ar,
        birthPlaceFr: city.fr,
        birthDate: getRandomDate(new Date(1950, 0, 1), new Date(2020, 11, 31)),
        hijriDate: getRandomHijriDate(),
        gender: gender,
        fatherNameAr: getRandomItem(arabicFirstNames) + ' ' + getRandomItem(arabicFamilyNames),
        fatherNameFr: getRandomItem(frenchFirstNames) + ' ' + getRandomItem(frenchFamilyNames),
        motherNameAr: getRandomItem(arabicFirstNames) + ' ' + getRandomItem(arabicFamilyNames),
        motherNameFr: getRandomItem(frenchFirstNames) + ' ' + getRandomItem(frenchFamilyNames),
        actNumber: generateActNumber(index, year),
        registrationDate: getRandomDate(new Date(2020, 0, 1), new Date()),
        createdAt: new Date().toISOString()
    };

    // Add certificate image if requested
    if (withImage) {
        citizen.certificateImage = {
            data: generateTestCertificateImage(),
            fileName: `certificate_${citizen.id}.jpg`,
            uploadDate: new Date().toISOString()
        };
    }

    return citizen;
}

// Main generation function
async function generateTestData() {
    if (isGenerating) {
        alert('⚠️ عملية إنشاء البيانات جارية بالفعل!');
        return;
    }

    const recordCount = parseInt(document.getElementById('recordCount').value);
    const imagePercentage = parseInt(document.getElementById('withImages').value);
    const batchSize = parseInt(document.getElementById('batchSize').value);

    const confirmMessage = `هل أنت متأكد من إنشاء ${recordCount.toLocaleString()} سجل؟\n\n` +
                          `• نسبة الشهادات: ${imagePercentage}%\n` +
                          `• حجم الدفعة: ${batchSize}\n` +
                          `• الوقت المتوقع: ${Math.ceil(recordCount / 1000)} دقيقة\n\n` +
                          `⚠️ تحذير: قد يستغرق وقتاً طويلاً!`;

    if (!confirm(confirmMessage)) return;

    isGenerating = true;
    startTime = Date.now();

    // Show progress
    document.getElementById('progressSection').style.display = 'block';

    const currentYear = new Date().getFullYear();
    let generated = 0;

    // Clear existing data if requested
    if (confirm('هل تريد مسح البيانات الموجودة أولاً؟')) {
        citizens = [];
    }

    // Generate in batches to avoid freezing
    for (let batch = 0; batch < Math.ceil(recordCount / batchSize); batch++) {
        const batchStart = batch * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, recordCount);
        const batchRecords = [];

        for (let i = batchStart; i < batchEnd; i++) {
            const withImage = Math.random() * 100 < imagePercentage;
            const citizen = generateCitizen(citizens.length + i, currentYear, withImage);
            batchRecords.push(citizen);
            generated++;

            // Update progress
            const progress = (generated / recordCount) * 100;
            updateProgress(progress, generated, recordCount);
        }

        // Add batch to citizens array
        citizens.push(...batchRecords);

        // Save to localStorage every batch with size check
        try {
            const dataString = JSON.stringify(citizens);
            const dataSizeMB = (dataString.length / 1024 / 1024).toFixed(2);

            // Check if approaching localStorage limit (5MB warning)
            if (dataString.length > 5 * 1024 * 1024) {
                const continueGeneration = confirm(`⚠️ تحذير: حجم البيانات وصل إلى ${dataSizeMB} ميجابايت\nقد تواجه مشاكل في التخزين\n\nهل تريد المتابعة؟`);
                if (!continueGeneration) {
                    break;
                }
            }

            localStorage.setItem('citizens', dataString);

            // Update size display
            document.getElementById('dataSize').textContent = dataSizeMB + ' MB';

        } catch (error) {
            const dataSizeMB = (JSON.stringify(citizens).length / 1024 / 1024).toFixed(2);
            alert(`❌ خطأ في الحفظ: امتلأت ذاكرة التخزين!\n\nتم إنشاء: ${generated.toLocaleString()} سجل\nحجم البيانات: ${dataSizeMB} ميجابايت\n\n💡 نصيحة: قلل نسبة الصور أو استخدم عدد أقل من السجلات`);
            break;
        }

        // Allow UI to update
        await new Promise(resolve => setTimeout(resolve, 10));
    }

    isGenerating = false;
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);

    // Hide progress and update stats
    document.getElementById('progressSection').style.display = 'none';
    updateStatistics();

    alert(`✅ تم إنشاء ${generated.toLocaleString()} سجل بنجاح!\n⏱️ الوقت المستغرق: ${duration} ثانية\n📊 متوسط السرعة: ${(generated / duration).toFixed(0)} سجل/ثانية`);
}

// Update progress bar
function updateProgress(percentage, current, total) {
    document.getElementById('progressFill').style.width = percentage + '%';
    document.getElementById('progressText').textContent = percentage.toFixed(1) + '%';
    document.getElementById('progressDetails').textContent = `${current.toLocaleString()} من ${total.toLocaleString()} سجل`;
}

// Update statistics
function updateStatistics() {
    const total = citizens.length;
    const withCerts = citizens.filter(c => c.certificateImage).length;
    const dataSize = (JSON.stringify(citizens).length / 1024 / 1024).toFixed(2);

    document.getElementById('totalRecords').textContent = total.toLocaleString();
    document.getElementById('withCertificates').textContent = withCerts.toLocaleString();
    document.getElementById('dataSize').textContent = dataSize + ' MB';

    if (startTime > 0) {
        const duration = ((Date.now() - startTime) / 1000).toFixed(1);
        document.getElementById('generationTime').textContent = duration + 's';
    }
}

// Clear all data
function clearAllData() {
    if (confirm('⚠️ هل أنت متأكد من حذف جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه!')) {
        citizens = [];
        localStorage.removeItem('citizens');
        updateStatistics();
        alert('✅ تم حذف جميع البيانات');
    }
}
