// نظام إدارة المستخدمين والمصادقة
class UserManager {
    constructor() {
        this.dbName = 'CitizensDatabase';
        this.version = 3; // زيادة الإصدار لدعم المستخدمين
        this.db = null;
        this.currentUser = null;
        this.sessionTimeout = 8 * 60 * 60 * 1000; // 8 ساعات
    }

    // تهيئة نظام المستخدمين
    async init() {
        try {
            await this._initDatabase();
            await this._createDefaultAdmin();
            await this._checkExistingSession();
            console.log('تم تهيئة نظام المستخدمين بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في تهيئة نظام المستخدمين:', error);
            throw error;
        }
    }

    // تهيئة قاعدة البيانات
    async _initDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                this.db = event.target.result;

                // إنشاء جداول المستخدمين إذا لم تكن موجودة
                if (!this.db.objectStoreNames.contains('users')) {
                    const usersStore = this.db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
                    usersStore.createIndex('username', 'username', { unique: true });
                    usersStore.createIndex('email', 'email', { unique: true });
                    usersStore.createIndex('role', 'role', { unique: false });
                    usersStore.createIndex('isActive', 'isActive', { unique: false });
                }

                if (!this.db.objectStoreNames.contains('sessions')) {
                    const sessionsStore = this.db.createObjectStore('sessions', { keyPath: 'sessionId' });
                    sessionsStore.createIndex('userId', 'userId', { unique: false });
                    sessionsStore.createIndex('expiresAt', 'expiresAt', { unique: false });
                }

                if (!this.db.objectStoreNames.contains('audit_log')) {
                    const auditStore = this.db.createObjectStore('audit_log', { keyPath: 'id', autoIncrement: true });
                    auditStore.createIndex('userId', 'userId', { unique: false });
                    auditStore.createIndex('action', 'action', { unique: false });
                    auditStore.createIndex('timestamp', 'timestamp', { unique: false });
                }
            };
        });
    }

    // إنشاء مدير افتراضي
    async _createDefaultAdmin() {
        try {
            const existingAdmin = await this.getUserByUsername('admin');
            if (!existingAdmin) {
                const adminUser = {
                    username: 'admin',
                    email: '<EMAIL>',
                    password: await this._hashPassword('admin123'),
                    fullName: 'مدير النظام',
                    role: 'admin',
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null,
                    permissions: ['all']
                };

                await this._addUser(adminUser);
                console.log('تم إنشاء حساب المدير الافتراضي');
                console.log('اسم المستخدم: admin');
                console.log('كلمة المرور: admin123');
            }
        } catch (error) {
            console.error('خطأ في إنشاء المدير الافتراضي:', error);
        }
    }

    // تشفير كلمة المرور
    async _hashPassword(password) {
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'civil_registry_salt');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // إضافة مستخدم جديد
    async _addUser(userData) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readwrite');
            const store = transaction.objectStore('users');

            const request = store.add(userData);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // البحث عن مستخدم بالاسم
    async getUserByUsername(username) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');
            const index = store.index('username');

            const request = index.get(username);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // تسجيل الدخول
    async login(username, password) {
        try {
            const user = await this.getUserByUsername(username);

            if (!user) {
                throw new Error('اسم المستخدم غير موجود');
            }

            if (!user.isActive) {
                throw new Error('الحساب غير مفعل');
            }

            const hashedPassword = await this._hashPassword(password);
            if (user.password !== hashedPassword) {
                throw new Error('كلمة المرور غير صحيحة');
            }

            // إنشاء جلسة جديدة
            const session = await this._createSession(user.id);

            // تحديث آخر تسجيل دخول
            await this._updateLastLogin(user.id);

            // تسجيل العملية
            await this._logAction(user.id, 'login', 'تسجيل دخول ناجح');

            this.currentUser = user;

            // حفظ الجلسة في localStorage
            localStorage.setItem('userSession', JSON.stringify({
                sessionId: session.sessionId,
                userId: user.id,
                username: user.username,
                role: user.role,
                expiresAt: session.expiresAt
            }));

            return {
                success: true,
                user: {
                    id: user.id,
                    username: user.username,
                    fullName: user.fullName,
                    role: user.role,
                    email: user.email
                },
                session: session
            };

        } catch (error) {
            await this._logAction(null, 'login_failed', `فشل تسجيل الدخول: ${username} - ${error.message}`);
            throw error;
        }
    }

    // إنشاء جلسة جديدة
    async _createSession(userId) {
        const sessionId = this._generateSessionId();
        const expiresAt = new Date(Date.now() + this.sessionTimeout).toISOString();

        const session = {
            sessionId: sessionId,
            userId: userId,
            createdAt: new Date().toISOString(),
            expiresAt: expiresAt,
            isActive: true
        };

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['sessions'], 'readwrite');
            const store = transaction.objectStore('sessions');

            const request = store.add(session);
            request.onsuccess = () => resolve(session);
            request.onerror = () => reject(request.error);
        });
    }

    // توليد معرف جلسة
    _generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // تحديث آخر تسجيل دخول
    async _updateLastLogin(userId) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readwrite');
            const store = transaction.objectStore('users');

            const getRequest = store.get(userId);
            getRequest.onsuccess = () => {
                const user = getRequest.result;
                if (user) {
                    user.lastLogin = new Date().toISOString();
                    const updateRequest = store.put(user);
                    updateRequest.onsuccess = () => resolve();
                    updateRequest.onerror = () => reject(updateRequest.error);
                } else {
                    reject(new Error('المستخدم غير موجود'));
                }
            };
            getRequest.onerror = () => reject(getRequest.error);
        });
    }

    // تسجيل العمليات
    async _logAction(userId, action, details) {
        const logEntry = {
            userId: userId,
            action: action,
            details: details,
            timestamp: new Date().toISOString(),
            ipAddress: 'localhost', // في بيئة محلية
            userAgent: navigator.userAgent
        };

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['audit_log'], 'readwrite');
            const store = transaction.objectStore('audit_log');

            const request = store.add(logEntry);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // فحص الجلسة الموجودة
    async _checkExistingSession() {
        try {
            const sessionData = localStorage.getItem('userSession');
            if (!sessionData) return false;

            const session = JSON.parse(sessionData);
            const now = new Date();
            const expiresAt = new Date(session.expiresAt);

            if (now > expiresAt) {
                await this.logout();
                return false;
            }

            // التحقق من صحة الجلسة في قاعدة البيانات
            const dbSession = await this._getSession(session.sessionId);
            if (!dbSession || !dbSession.isActive) {
                await this.logout();
                return false;
            }

            // تحميل بيانات المستخدم
            const user = await this._getUserById(session.userId);
            if (user && user.isActive) {
                this.currentUser = user;
                return true;
            }

            await this.logout();
            return false;

        } catch (error) {
            console.error('خطأ في فحص الجلسة:', error);
            await this.logout();
            return false;
        }
    }

    // الحصول على جلسة
    async _getSession(sessionId) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['sessions'], 'readonly');
            const store = transaction.objectStore('sessions');

            const request = store.get(sessionId);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // الحصول على مستخدم بالمعرف
    async _getUserById(userId) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');

            const request = store.get(userId);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // تسجيل الخروج
    async logout() {
        try {
            const sessionData = localStorage.getItem('userSession');
            if (sessionData) {
                const session = JSON.parse(sessionData);
                await this._deactivateSession(session.sessionId);

                if (this.currentUser) {
                    await this._logAction(this.currentUser.id, 'logout', 'تسجيل خروج');
                }
            }

            this.currentUser = null;
            localStorage.removeItem('userSession');

            return true;
        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
            // حذف الجلسة المحلية حتى لو فشل حذفها من قاعدة البيانات
            this.currentUser = null;
            localStorage.removeItem('userSession');
            return true;
        }
    }

    // إلغاء تفعيل الجلسة
    async _deactivateSession(sessionId) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['sessions'], 'readwrite');
            const store = transaction.objectStore('sessions');

            const getRequest = store.get(sessionId);
            getRequest.onsuccess = () => {
                const session = getRequest.result;
                if (session) {
                    session.isActive = false;
                    session.endedAt = new Date().toISOString();

                    const updateRequest = store.put(session);
                    updateRequest.onsuccess = () => resolve();
                    updateRequest.onerror = () => reject(updateRequest.error);
                } else {
                    resolve(); // الجلسة غير موجودة
                }
            };
            getRequest.onerror = () => reject(getRequest.error);
        });
    }

    // فحص الصلاحيات
    hasPermission(permission) {
        if (!this.currentUser) return false;
        if (this.currentUser.role === 'admin') return true;
        return this.currentUser.permissions && this.currentUser.permissions.includes(permission);
    }

    // فحص تسجيل الدخول
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // إضافة مستخدم جديد (للمدير فقط)
    async addUser(userData) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لإضافة مستخدمين');
        }

        try {
            // التحقق من عدم وجود المستخدم
            const existingUser = await this.getUserByUsername(userData.username);
            if (existingUser) {
                throw new Error('اسم المستخدم موجود بالفعل');
            }

            // تشفير كلمة المرور
            userData.password = await this._hashPassword(userData.password);
            userData.createdAt = new Date().toISOString();
            userData.isActive = true;

            const userId = await this._addUser(userData);

            await this._logAction(this.currentUser.id, 'add_user', `إضافة مستخدم جديد: ${userData.username}`);

            return userId;
        } catch (error) {
            await this._logAction(this.currentUser.id, 'add_user_failed', `فشل في إضافة مستخدم: ${userData.username} - ${error.message}`);
            throw error;
        }
    }

    // الحصول على جميع المستخدمين
    async getAllUsers() {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لعرض المستخدمين');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');

            const request = store.getAll();
            request.onsuccess = () => {
                const users = request.result.map(user => ({
                    id: user.id,
                    username: user.username,
                    fullName: user.fullName,
                    email: user.email,
                    role: user.role,
                    isActive: user.isActive,
                    createdAt: user.createdAt,
                    lastLogin: user.lastLogin
                }));
                resolve(users);
            };
            request.onerror = () => reject(request.error);
        });
    }

    // تحديث حالة المستخدم
    async updateUserStatus(userId, isActive) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لتعديل المستخدمين');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readwrite');
            const store = transaction.objectStore('users');

            const getRequest = store.get(userId);
            getRequest.onsuccess = () => {
                const user = getRequest.result;
                if (user) {
                    user.isActive = isActive;
                    const updateRequest = store.put(user);
                    updateRequest.onsuccess = () => {
                        this._logAction(this.currentUser.id, 'update_user_status',
                            `تغيير حالة المستخدم ${user.username} إلى ${isActive ? 'مفعل' : 'معطل'}`);
                        resolve();
                    };
                    updateRequest.onerror = () => reject(updateRequest.error);
                } else {
                    reject(new Error('المستخدم غير موجود'));
                }
            };
            getRequest.onerror = () => reject(getRequest.error);
        });
    }

    // تحديث بيانات المستخدم
    async updateUser(userId, userData) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لتعديل المستخدمين');
        }

        try {
            // التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
            if (userData.username || userData.email) {
                const existingUsers = await this.getAllUsers();

                if (userData.username) {
                    const usernameExists = existingUsers.find(u => u.username === userData.username && u.id !== userId);
                    if (usernameExists) {
                        throw new Error('اسم المستخدم موجود بالفعل');
                    }
                }

                if (userData.email) {
                    const emailExists = existingUsers.find(u => u.email === userData.email && u.id !== userId);
                    if (emailExists) {
                        throw new Error('البريد الإلكتروني موجود بالفعل');
                    }
                }
            }

            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['users'], 'readwrite');
                const store = transaction.objectStore('users');

                const getRequest = store.get(userId);
                getRequest.onsuccess = async () => {
                    const user = getRequest.result;
                    if (user) {
                        // حفظ البيانات القديمة للمقارنة
                        const oldData = { ...user };

                        // تحديث البيانات
                        if (userData.fullName !== undefined) user.fullName = userData.fullName;
                        if (userData.username !== undefined) user.username = userData.username;
                        if (userData.email !== undefined) user.email = userData.email;
                        if (userData.role !== undefined) user.role = userData.role;
                        if (userData.permissions !== undefined) user.permissions = userData.permissions;

                        // تشفير كلمة المرور الجديدة إذا تم توفيرها
                        if (userData.password && userData.password.trim() !== '') {
                            user.password = await this._hashPassword(userData.password);
                        }

                        user.updatedAt = new Date().toISOString();
                        user.updatedBy = this.currentUser.id;

                        const updateRequest = store.put(user);
                        updateRequest.onsuccess = () => {
                            // تسجيل التغييرات
                            const changes = [];
                            if (oldData.fullName !== user.fullName) changes.push(`الاسم: ${oldData.fullName} → ${user.fullName}`);
                            if (oldData.username !== user.username) changes.push(`اسم المستخدم: ${oldData.username} → ${user.username}`);
                            if (oldData.email !== user.email) changes.push(`البريد: ${oldData.email} → ${user.email}`);
                            if (oldData.role !== user.role) changes.push(`الدور: ${oldData.role} → ${user.role}`);
                            if (userData.password) changes.push('تم تغيير كلمة المرور');

                            this._logAction(this.currentUser.id, 'update_user',
                                `تعديل المستخدم ${user.username}: ${changes.join(', ')}`);
                            resolve(user);
                        };
                        updateRequest.onerror = () => reject(updateRequest.error);
                    } else {
                        reject(new Error('المستخدم غير موجود'));
                    }
                };
                getRequest.onerror = () => reject(getRequest.error);
            });
        } catch (error) {
            await this._logAction(this.currentUser.id, 'update_user_failed',
                `فشل في تعديل المستخدم ${userId}: ${error.message}`);
            throw error;
        }
    }

    // حذف المستخدم
    async deleteUser(userId) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لحذف المستخدمين');
        }

        try {
            // التحقق من عدم حذف المستخدم الحالي
            if (userId === this.currentUser.id) {
                throw new Error('لا يمكنك حذف حسابك الخاص');
            }

            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['users', 'sessions'], 'readwrite');
                const usersStore = transaction.objectStore('users');
                const sessionsStore = transaction.objectStore('sessions');

                // الحصول على بيانات المستخدم أولاً
                const getUserRequest = usersStore.get(userId);
                getUserRequest.onsuccess = () => {
                    const user = getUserRequest.result;
                    if (user) {
                        // حذف المستخدم
                        const deleteUserRequest = usersStore.delete(userId);
                        deleteUserRequest.onsuccess = () => {
                            // حذف جميع جلسات المستخدم
                            const sessionsIndex = sessionsStore.index('userId');
                            const getSessionsRequest = sessionsIndex.getAll(userId);

                            getSessionsRequest.onsuccess = () => {
                                const sessions = getSessionsRequest.result;
                                let deletedSessions = 0;

                                if (sessions.length === 0) {
                                    // لا توجد جلسات للحذف
                                    this._logAction(this.currentUser.id, 'delete_user',
                                        `حذف المستخدم ${user.username} (${user.email})`);
                                    resolve({ deletedUser: user, deletedSessions: 0 });
                                    return;
                                }

                                sessions.forEach(session => {
                                    const deleteSessionRequest = sessionsStore.delete(session.sessionId);
                                    deleteSessionRequest.onsuccess = () => {
                                        deletedSessions++;
                                        if (deletedSessions === sessions.length) {
                                            this._logAction(this.currentUser.id, 'delete_user',
                                                `حذف المستخدم ${user.username} (${user.email}) مع ${deletedSessions} جلسة`);
                                            resolve({ deletedUser: user, deletedSessions });
                                        }
                                    };
                                    deleteSessionRequest.onerror = () => {
                                        console.warn('فشل في حذف جلسة:', session.sessionId);
                                        deletedSessions++;
                                        if (deletedSessions === sessions.length) {
                                            resolve({ deletedUser: user, deletedSessions: deletedSessions - 1 });
                                        }
                                    };
                                });
                            };
                            getSessionsRequest.onerror = () => {
                                // فشل في الحصول على الجلسات، لكن المستخدم تم حذفه
                                this._logAction(this.currentUser.id, 'delete_user',
                                    `حذف المستخدم ${user.username} (فشل في حذف الجلسات)`);
                                resolve({ deletedUser: user, deletedSessions: 0 });
                            };
                        };
                        deleteUserRequest.onerror = () => reject(deleteUserRequest.error);
                    } else {
                        reject(new Error('المستخدم غير موجود'));
                    }
                };
                getUserRequest.onerror = () => reject(getUserRequest.error);
            });
        } catch (error) {
            await this._logAction(this.currentUser.id, 'delete_user_failed',
                `فشل في حذف المستخدم ${userId}: ${error.message}`);
            throw error;
        }
    }

    // الحصول على مستخدم واحد بالمعرف (للتعديل)
    async getUserById(userId) {
        if (!this.hasPermission('manage_users')) {
            throw new Error('ليس لديك صلاحية لعرض بيانات المستخدمين');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['users'], 'readonly');
            const store = transaction.objectStore('users');

            const request = store.get(userId);
            request.onsuccess = () => {
                const user = request.result;
                if (user) {
                    // إزالة كلمة المرور من البيانات المرسلة
                    const { password, ...userWithoutPassword } = user;
                    resolve(userWithoutPassword);
                } else {
                    reject(new Error('المستخدم غير موجود'));
                }
            };
            request.onerror = () => reject(request.error);
        });
    }

    // تغيير كلمة مرور المستخدم الحالي
    async changePassword(currentPassword, newPassword) {
        if (!this.isLoggedIn()) {
            throw new Error('يجب تسجيل الدخول أولاً');
        }

        try {
            // التحقق من كلمة المرور الحالية
            const hashedCurrentPassword = await this._hashPassword(currentPassword);
            if (this.currentUser.password !== hashedCurrentPassword) {
                throw new Error('كلمة المرور الحالية غير صحيحة');
            }

            // تشفير كلمة المرور الجديدة
            const hashedNewPassword = await this._hashPassword(newPassword);

            return new Promise((resolve, reject) => {
                const transaction = this.db.transaction(['users'], 'readwrite');
                const store = transaction.objectStore('users');

                const getRequest = store.get(this.currentUser.id);
                getRequest.onsuccess = () => {
                    const user = getRequest.result;
                    if (user) {
                        user.password = hashedNewPassword;
                        user.passwordChangedAt = new Date().toISOString();

                        const updateRequest = store.put(user);
                        updateRequest.onsuccess = () => {
                            // تحديث المستخدم الحالي
                            this.currentUser.password = hashedNewPassword;
                            this.currentUser.passwordChangedAt = user.passwordChangedAt;

                            this._logAction(this.currentUser.id, 'change_password', 'تغيير كلمة المرور');
                            resolve();
                        };
                        updateRequest.onerror = () => reject(updateRequest.error);
                    } else {
                        reject(new Error('المستخدم غير موجود'));
                    }
                };
                getRequest.onerror = () => reject(getRequest.error);
            });
        } catch (error) {
            await this._logAction(this.currentUser.id, 'change_password_failed',
                `فشل في تغيير كلمة المرور: ${error.message}`);
            throw error;
        }
    }
}

// إنشاء مثيل عام لإدارة المستخدمين
const userManager = new UserManager();
