// Cross-Platform Database Manager
// يدير التوافق بين الأجهزة المختلفة ويضمن عمل التطبيق على أي حاسوب

class CrossPlatformManager {
    constructor() {
        this.isInitialized = false;
        this.storageType = null;
        this.capabilities = {};
    }

    // تهيئة النظام وفحص الإمكانيات
    async init() {
        if (this.isInitialized) {
            return this.capabilities;
        }

        console.log('🔍 فحص إمكانيات النظام...');
        
        // فحص دعم IndexedDB
        this.capabilities.indexedDB = this._checkIndexedDBSupport();
        
        // فحص دعم localStorage
        this.capabilities.localStorage = this._checkLocalStorageSupport();
        
        // فحص دعم Web Workers
        this.capabilities.webWorkers = this._checkWebWorkersSupport();
        
        // فحص دعم File API
        this.capabilities.fileAPI = this._checkFileAPISupport();
        
        // فحص مساحة التخزين المتاحة
        this.capabilities.storageQuota = await this._checkStorageQuota();
        
        // تحديد نوع التخزين الأفضل
        this.storageType = this._determineStorageType();
        
        // إعداد معالجات الأخطاء العامة
        this._setupErrorHandlers();
        
        this.isInitialized = true;
        
        console.log('✅ تم فحص النظام بنجاح:', this.capabilities);
        return this.capabilities;
    }

    // فحص دعم IndexedDB
    _checkIndexedDBSupport() {
        try {
            return !!(window.indexedDB && 
                     window.IDBTransaction && 
                     window.IDBKeyRange);
        } catch (error) {
            console.warn('IndexedDB غير مدعوم:', error);
            return false;
        }
    }

    // فحص دعم localStorage
    _checkLocalStorageSupport() {
        try {
            const testKey = 'test_' + Date.now();
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            console.warn('localStorage غير مدعوم:', error);
            return false;
        }
    }

    // فحص دعم Web Workers
    _checkWebWorkersSupport() {
        try {
            return typeof Worker !== 'undefined';
        } catch (error) {
            return false;
        }
    }

    // فحص دعم File API
    _checkFileAPISupport() {
        try {
            return !!(window.File && 
                     window.FileReader && 
                     window.FileList && 
                     window.Blob);
        } catch (error) {
            return false;
        }
    }

    // فحص مساحة التخزين المتاحة
    async _checkStorageQuota() {
        try {
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                return {
                    available: estimate.quota || 0,
                    used: estimate.usage || 0,
                    availableMB: ((estimate.quota || 0) / 1024 / 1024).toFixed(2),
                    usedMB: ((estimate.usage || 0) / 1024 / 1024).toFixed(2),
                    percentage: estimate.quota ? 
                        ((estimate.usage / estimate.quota) * 100).toFixed(2) : 0
                };
            }
            return { available: 0, used: 0, availableMB: 'غير متاح', usedMB: 'غير متاح', percentage: 0 };
        } catch (error) {
            console.warn('لا يمكن فحص مساحة التخزين:', error);
            return { available: 0, used: 0, availableMB: 'غير متاح', usedMB: 'غير متاح', percentage: 0 };
        }
    }

    // تحديد نوع التخزين الأفضل
    _determineStorageType() {
        if (this.capabilities.indexedDB) {
            console.log('📊 سيتم استخدام IndexedDB للتخزين');
            return 'indexedDB';
        } else if (this.capabilities.localStorage) {
            console.log('💾 سيتم استخدام localStorage للتخزين');
            return 'localStorage';
        } else {
            console.error('❌ لا يوجد نوع تخزين مدعوم!');
            return 'none';
        }
    }

    // إعداد معالجات الأخطاء العامة
    _setupErrorHandlers() {
        // معالج أخطاء JavaScript العامة
        window.addEventListener('error', (event) => {
            console.error('خطأ في التطبيق:', event.error);
            this._handleApplicationError(event.error);
        });

        // معالج الوعود المرفوضة
        window.addEventListener('unhandledrejection', (event) => {
            console.error('وعد مرفوض:', event.reason);
            this._handleApplicationError(event.reason);
        });
    }

    // معالجة أخطاء التطبيق
    _handleApplicationError(error) {
        // يمكن إضافة منطق إضافي هنا مثل إرسال تقارير الأخطاء
        console.log('تم تسجيل خطأ في النظام:', error);
    }

    // إنشاء نسخة احتياطية تلقائية
    async createAutoBackup(data) {
        try {
            if (!this.capabilities.fileAPI) {
                console.warn('File API غير مدعوم، لا يمكن إنشاء نسخة احتياطية');
                return false;
            }

            const backupData = {
                timestamp: new Date().toISOString(),
                platform: this._getPlatformInfo(),
                capabilities: this.capabilities,
                data: data
            };

            const dataStr = JSON.stringify(backupData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            // حفظ في localStorage كنسخة احتياطية
            try {
                localStorage.setItem('emergency_backup', dataStr);
                console.log('✅ تم حفظ نسخة احتياطية طارئة');
            } catch (error) {
                console.warn('فشل في حفظ النسخة الاحتياطية الطارئة:', error);
            }

            return true;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return false;
        }
    }

    // استرداد النسخة الاحتياطية الطارئة
    getEmergencyBackup() {
        try {
            const backup = localStorage.getItem('emergency_backup');
            return backup ? JSON.parse(backup) : null;
        } catch (error) {
            console.error('خطأ في استرداد النسخة الاحتياطية:', error);
            return null;
        }
    }

    // الحصول على معلومات المنصة
    _getPlatformInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            timestamp: new Date().toISOString()
        };
    }

    // فحص التوافق مع إصدار معين من البيانات
    isDataVersionCompatible(dataVersion) {
        // يمكن إضافة منطق للتحقق من توافق إصدارات البيانات
        return true;
    }

    // تنظيف البيانات القديمة
    async cleanupOldData() {
        try {
            // تنظيف النسخ الاحتياطية القديمة من localStorage
            const keys = Object.keys(localStorage);
            const backupKeys = keys.filter(key => key.startsWith('citizens_backup_'));
            
            // الاحتفاظ بآخر 5 نسخ احتياطية فقط
            if (backupKeys.length > 5) {
                backupKeys.sort().slice(0, -5).forEach(key => {
                    localStorage.removeItem(key);
                    console.log(`تم حذف النسخة الاحتياطية القديمة: ${key}`);
                });
            }

            return true;
        } catch (error) {
            console.error('خطأ في تنظيف البيانات القديمة:', error);
            return false;
        }
    }

    // الحصول على تقرير حالة النظام
    getSystemStatus() {
        return {
            initialized: this.isInitialized,
            storageType: this.storageType,
            capabilities: this.capabilities,
            platform: this._getPlatformInfo(),
            timestamp: new Date().toISOString()
        };
    }
}

// إنشاء مثيل عام
const crossPlatformManager = new CrossPlatformManager();

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CrossPlatformManager;
}
