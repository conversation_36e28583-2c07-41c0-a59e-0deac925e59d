<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عقد ازدياد مزدوج - عربي/فرنسي</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', 'Times New Roman', serif;
            margin: 0;
            padding: 10mm;
            font-size: 10px; /* تقليل حجم الخط الأساسي */
            font-weight: 500;
            line-height: 1.2; /* تقليل المسافة بين الأسطر */
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .document {
            width: 200mm; /* زيادة العرض ليملأ A5 */
            height: 138mm; /* زيادة الارتفاع ليملأ A5 */
            margin: 0 auto;
            padding: 4mm; /* هوامش داخلية محسنة */
            background: white;
            display: flex;
            flex-direction: column;
            box-sizing: border-box; /* لضمان احتساب الـ padding ضمن الأبعاد */
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2mm; /* تقليل الهامش */
            padding-bottom: 1mm; /* تقليل الـ padding */
            font-size: 9px; /* تقليل خط الترويسة ليناسب المساحة */
            font-weight: bold;
        }
        .header-right { text-align: left; direction: ltr; } /* الفرنسية من اليمين إلى اليسار */
        .header-center { text-align: center; flex: 1; margin: 0 5mm; }
        .header-left { text-align: right; direction: rtl; } /* العربية من اليسار إلى اليمين */

        /* Unified content layout */
        .content {
            height: calc(100% - 15mm); /* تقليل الهامش العلوي */
            padding: 1mm; /* تقليل الـ padding */
            display: flex;
            flex-direction: column;
        }

        /* Title */
        .dual-title {
            text-align: center;
            font-weight: bold;
            font-size: 9px; /* تقليل حجم الخط */
            margin: 1mm 0; /* تقليل الهوامش */
            padding: 1.5mm; /* تقليل الـ padding */
            border: 1px solid #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .title-french {
            flex: 1;
            text-align: left;
            direction: ltr;
        }
        .title-arabic {
            flex: 1;
            text-align: right;
            direction: rtl;
        }

        /* Content lines - Merged */
        .merged-line {
            margin: 0.4mm 0; /* تقليل المسافة بين الأسطر لتناسب نصف A4 */
            font-size: 11px; /* تكبير الخط من 9px إلى 11px */
            font-weight: 500;
            display: flex;
            align-items: baseline;
            justify-content: space-between;
        }
        .french-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: ltr;
            text-align: left;
        }
        .arabic-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: rtl;
            text-align: right;
        }
        .label {
            font-weight: 500; /* تقليل سمك النصوص الثابتة من 600 إلى 500 */
            margin: 0 2mm;
            white-space: nowrap;
            min-width: 15mm;
            color: #333; /* لون أفتح للنصوص الثابتة */
        }
        .underline {
            border-bottom: 1.5px solid #000; /* تسميك الخط السفلي */
            min-width: 15mm;
            padding: 0 1mm;
            flex: 1;
            min-height: 2mm; /* تقليل ارتفاع المنطقة لتوفير مساحة */
            font-weight: 700; /* تسميك المعلومات الشخصية */
            color: #000; /* لون أسود قوي للمعلومات الشخصية */
        }

        /* خط تابع للنص في مكان الازدياد - يزداد مع الكلام */
        .birth-place-adaptive {
            border-bottom: 1.5px solid #000;
            min-width: 15mm;        /* حد أدنى فقط */
            width: auto;            /* عرض تلقائي حسب النص */
            max-width: none;        /* بدون حد أقصى */
            padding: 0 1mm;
            min-height: 2mm;
            font-weight: 700;
            color: #000;
            overflow: visible;      /* إظهار النص الزائد */
            white-space: nowrap;    /* منع التفاف - سطر واحد */
            text-overflow: clip;    /* عدم قطع النص */
            font-size: 15px;        /* زيادة من 13px إلى 15px */
            display: inline-block;
            vertical-align: baseline;
            word-wrap: normal;      /* عدم كسر الكلمات */
            line-height: 1.1;       /* تقليل ارتفاع السطر */
        }



        /* تصغير الخط للنصوص الطويلة */
        .birth-place-adaptive.long-text {
            font-size: 13px;        /* زيادة من 11px إلى 13px */
        }

        .birth-place-adaptive.very-long-text {
            font-size: 11px;        /* زيادة من 9px إلى 11px */
        }



        /* Act info */
        .act-info {
            margin: 2mm 0;
            font-size: 8px;
        }
        .act-line {
            display: flex;
            justify-content: space-between;
            margin: 1mm 0;
        }

        /* Signature area */
        .signature {
            margin-top: auto;
            text-align: center;
            font-size: 7px;
            padding-top: 3mm;
            border-top: 0.5px solid #000;
        }
        .stamp {
            width: 15mm;
            height: 15mm;
            border: 1px solid #000;
            margin: 2mm auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 6px;
            line-height: 1;
        }

        /* Test controls */
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,123,255,0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .controls button {
            background: white;
            color: #007bff;
            border: none;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
        }

        @media print {
            body {
                margin: 0;
                padding: 0; /* إزالة جميع الهوامش عند الطباعة */
                background: white;
                font-size: 9px; /* تقليل حجم الخط عند الطباعة */
            }
            .document {
                width: 210mm; /* العرض الكامل لـ A5 أفقي */
                height: 148mm; /* الارتفاع الكامل لـ A5 أفقي */
                margin: 0; /* إزالة الهوامش الخارجية */
                padding: 5mm; /* هوامش داخلية محسنة */
                box-sizing: border-box;
            }
            .controls {
                display: none !important; /* إخفاء أزرار التحكم */
            }

            /* إعدادات الطباعة المحسنة */
            @page {
                size: A5 landscape; /* A5 أفقي (210mm × 148mm) */
                margin: 0; /* إزالة جميع هوامش الطابعة */
            }

            /* تحسين الخطوط عند الطباعة */
            .header {
                font-size: 8px; /* خط أصغر للهيدر */
            }

            .dual-title {
                font-size: 8px; /* خط أصغر للعنوان */
                margin: 0.5mm 0;
                padding: 1mm;
            }

            /* تحسين المسافات عند الطباعة */
            .content {
                padding: 0.5mm;
            }
        }
    </style>
</head>
<body>
    <div class="controls" style="text-align: center; margin-bottom: 20px; padding: 15px; background: rgba(255,255,255,0.9); border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
        <a href="main-dashboard.html" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; border: none; padding: 10px 15px; margin: 5px; border-radius: 20px; cursor: pointer; text-decoration: none; display: inline-block; font-weight: 600; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3); transition: all 0.3s ease;">🏠 الرئيسية</a>
        <a href="citizens-database-indexeddb.html" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; border: none; padding: 10px 15px; margin: 5px; border-radius: 20px; cursor: pointer; text-decoration: none; display: inline-block; font-weight: 600; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3); transition: all 0.3s ease;">👥 قاعدة البيانات</a>
        <button onclick="window.print()" style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%); color: white; border: none; padding: 10px 15px; margin: 5px; border-radius: 20px; cursor: pointer; font-weight: 600; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3); transition: all 0.3s ease;">🖨️ طباعة</button>
        <button onclick="fillTestData()" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white; border: none; padding: 10px 15px; margin: 5px; border-radius: 20px; cursor: pointer; font-weight: 600; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3); transition: all 0.3s ease;">📊 بيانات تجريبية</button>
    </div>

    <div class="document">
        <!-- Header -->
        <div class="header">
            <div class="header-right">
                <div>ROYAUME DU MAROC</div>
                <div>Ministère de l'Intérieur</div>
                <div>Province de Safi</div>
                <div>Commune d'Ayir</div>
                <div>Bureau de l'État-Civil d'Ayir</div>
            </div>
            <div class="header-center">
                <div style="font-size: 11px; line-height: 1.4; font-weight: 700; color: #000; text-align: center;">
                    <div style="margin-bottom: 1mm;">طبقا للقانون رقم 37.99 المتعلق بالحالة المدنية</div>
                    <div style="margin-bottom: 1mm;">والصادر بتنفيذه الظهير الشريف رقم 1.02.239</div>
                    <div>بتاريخ 25 رجب 1423 (3) أكتوبر (2002)</div>
                </div>
            </div>
            <div class="header-left">
                <div>المملكة المغربية</div>
                <div>وزارة الداخلية</div>
                <div>إقليم أسفي</div>
                <div>جماعة أيير</div>
                <div>مكتب الحالة المدنية أيير</div>
            </div>
        </div>

        <!-- Unified content with merged languages -->
        <div class="content">


            <!-- Title with Act Information -->
            <div style="display: flex; align-items: flex-start; margin: 1mm 0; padding: 1mm;">
                <!-- French Act Info (Left) -->
                <div style="flex: 1; text-align: left; direction: ltr; font-size: 10px;">
                    <div>Acte N°: <span style="border-bottom: 1.5px solid #000; min-width: 25mm; display: inline-block; font-weight: 700;" id="actNumberFr"></span></div>
                    <div style="margin-top: 2mm; display: flex; align-items: flex-start;">
                        <span style="margin-right: 3mm;">Année</span>
                        <div style="display: flex; flex-direction: column;">
                            <span style="font-size: 11px; font-weight: 700; color: #000;">Hégirienne</span>
                            <span style="font-size: 11px; font-weight: 700; color: #000; margin-top: 1mm;">Grégorienne</span>
                        </div>
                        <div style="display: flex; flex-direction: column; margin-left: 2mm;">
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="hijriYearFr"></span>
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block; margin-top: 1mm;" id="gregYearFr"></span>
                        </div>
                    </div>
                </div>

                <!-- Centered Title -->
                <div style="flex: 2; text-align: center; font-weight: bold;">
                    <div style="font-size: 14px; margin-bottom: 2mm; font-weight: 800;">نسخة موجزة من رسم الولادة</div>
                    <div style="font-size: 12px; font-weight: 700;">EXTRAIT D'ACTE DE NAISSANCE</div>
                </div>

                <!-- Arabic Act Info (Right) -->
                <div style="flex: 1; text-align: right; direction: rtl; font-size: 10px;">
                    <div>رقم القيد: <span style="border-bottom: 1.5px solid #000; min-width: 25mm; display: inline-block; font-weight: 700;" id="actNumberAr"></span></div>
                    <table style="margin-top: 2mm; border-collapse: collapse; direction: rtl;">
                        <tr>
                            <td rowspan="2" style="vertical-align: top; padding-left: 3mm;">سنة</td>
                            <td style="font-size: 11px; font-weight: 700; color: #000; padding-left: 2mm;">هجرية</td>
                            <td><span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="hijriYearAr"></span></td>
                        </tr>
                        <tr>
                            <td style="font-size: 11px; font-weight: 700; color: #000; padding-left: 2mm; padding-top: 1mm;">ميلادية</td>
                            <td style="padding-top: 1mm;"><span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="gregYearAr"></span></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Prénom</span>
                    <span class="underline" id="firstNameFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">الاسم الشخصي</span>
                    <span class="underline" id="firstNameAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Nom de famille</span>
                    <span class="underline" id="familyNameFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">الاسم العائلي</span>
                    <span class="underline" id="familyNameAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Lieu de naissance</span>
                    <span class="birth-place-adaptive" id="birthPlaceFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">مكان الازدياد</span>
                    <span class="birth-place-adaptive" id="birthPlaceAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Né(e) le</span>
                    <span class="underline" id="birthDateFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">ولد(ت) في يوم</span>
                    <span class="underline" id="birthDateAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Correspondant au</span>
                    <span class="underline" id="hijriDateFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">الموافق لـ</span>
                    <span class="underline" id="hijriDateAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Sexe</span>
                    <span class="underline" id="genderFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">جنسه(ا)</span>
                    <span class="underline" id="genderAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Père</span>
                    <span class="underline" id="fatherNameFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">والده(ا) هو</span>
                    <span class="underline" id="fatherNameAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Mère</span>
                    <span class="underline" id="motherNameFr"></span>
                </div>
                <div class="arabic-part">
                    <span class="label">والدته(ا) هي</span>
                    <span class="underline" id="motherNameAr"></span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Mention marginale</span>
                    <span class="underline" id="marginalNoteFr">Néant</span>
                </div>
                <div class="arabic-part">
                    <span class="label">بيان (الوفاة) المشار إليه في طرة الرسم</span>
                    <span class="underline" id="marginalNoteAr">لا شيء</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Nous certifions en qualité d'officier</span>
                    <span class="underline" id="certificationFr">de l'état civil</span>
                </div>
                <div class="arabic-part">
                    <span class="label">نشهد بصفتنا ضابطا الحالة المدنية نحن</span>
                    <span class="underline" id="certificationAr">ضابط الحالة المدنية</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">La conformité de cette copie aux registres</span>
                    <span class="underline" id="conformityFr">de l'état civil d'Ayir</span>
                </div>
                <div class="arabic-part">
                    <span class="label">بمطابقة هذه النسخة لما هو مضمن بسجلات الحالة المدنية لمكتب الحالة المدنية بأيير</span>
                    <span class="underline" id="conformityAr">مطابقة</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Établi à</span>
                    <span class="underline" id="issuePlaceFr">أيير</span>
                    <span class="label">le</span>
                    <span class="underline" id="issueDateFr">20 Janvier 2024</span>
                </div>
                <div class="arabic-part">
                    <span class="label">حرر بـ</span>
                    <span class="underline" id="issuePlaceAr">أيير</span>
                    <span class="label">بتاريخ</span>
                    <span class="underline" id="issueDateAr">20 يناير 2024</span>
                </div>
            </div>

            <!-- Signature Section -->
            <div class="merged-line" style="margin-top: 4mm; padding-top: 0mm;">
                <div class="french-part" style="text-align: center; padding-right: 0mm; margin-left: 20mm;">
                    <div style="font-weight: 600;">ضابط الحالة المدنية<br><span style="font-size: 7px; font-weight: 500;">L'Officier de l'État-Civil</span></div>
                </div>
                <div class="arabic-part" style="text-align: center; padding-left: 0mm; margin-right: 20mm;">
                    <div style="font-weight: 600;">طابع مكتب الحالة المدنية<br><span style="font-size: 7px; font-weight: 500;">Cachet du Bureau de l'État-Civil</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function fillTestData() {
            // Test date
            const testDate = '2024-01-15';

            // Arabic data
            document.getElementById('firstNameAr').textContent = 'أحمد';
            document.getElementById('familyNameAr').textContent = 'محمد علي';
            document.getElementById('birthDateAr').textContent = formatArabicDate(testDate);
            document.getElementById('birthPlaceAr').textContent = 'الرباط';
            document.getElementById('fatherNameAr').textContent = 'محمد علي حسن';
            document.getElementById('motherNameAr').textContent = 'فاطمة أحمد محمد';

            // French data
            document.getElementById('firstNameFr').textContent = 'Ahmed';
            document.getElementById('familyNameFr').textContent = 'Mohamed Ali';
            document.getElementById('birthDateFr').textContent = formatFrenchDate(testDate);
            document.getElementById('birthPlaceFr').textContent = 'Rabat';
            document.getElementById('fatherNameFr').textContent = 'Mohamed Ali Hassan';
            document.getElementById('motherNameFr').textContent = 'Fatima Ahmed Mohamed';

            console.log('📊 تم ملء البيانات التجريبية مع تنسيق صحيح للتواريخ');
        }

        // Load data from URL parameters
        async function loadDataFromURL() {
            try {
                console.log('🔍 Starting loadDataFromURL...');
                const urlParams = new URLSearchParams(window.location.search);

                console.log('🔍 URL Parameters:', Object.fromEntries(urlParams.entries()));
                console.log('🔍 Current URL:', window.location.href);

                // Check if this is a request with citizen ID
                if (urlParams.has('id')) {
                    const citizenId = decodeURIComponent(urlParams.get('id'));
                    console.log('📜 Loading citizen data for ID:', citizenId);
                    await loadCitizenDataById(citizenId);
                    return;
                }

                // Check if this is a print request with ID (legacy support)
                if (urlParams.has('print')) {
                    const citizenId = decodeURIComponent(urlParams.get('print'));
                    console.log('🖨️ Loading citizen data for print ID:', citizenId);
                    await loadCitizenDataById(citizenId);
                    return;
                }

                // Original method - load from URL parameters
                if (urlParams.has('firstNameAr') || urlParams.has('firstName')) {
                    console.log('📋 Loading data from URL parameters...');
                // Fill Arabic fields
                document.getElementById('firstNameAr').textContent = urlParams.get('firstNameAr') || urlParams.get('firstName') || '';
                document.getElementById('familyNameAr').textContent = urlParams.get('familyNameAr') || urlParams.get('familyName') || '';
                document.getElementById('birthPlaceAr').textContent = urlParams.get('birthPlaceAr') || urlParams.get('birthPlace') || '';
                document.getElementById('birthDateAr').textContent = urlParams.get('birthDate') || '';
                document.getElementById('hijriDateAr').textContent = urlParams.get('hijriDate') || '';
                document.getElementById('genderAr').textContent = urlParams.get('gender') || '';
                document.getElementById('fatherNameAr').textContent = urlParams.get('fatherNameAr') || urlParams.get('fatherName') || '';
                document.getElementById('motherNameAr').textContent = urlParams.get('motherNameAr') || urlParams.get('motherName') || '';
                document.getElementById('actNumberAr').textContent = urlParams.get('actNumber') || '';

                // Fill French fields
                document.getElementById('firstNameFr').textContent = urlParams.get('firstNameFr') || urlParams.get('firstName') || '';
                document.getElementById('familyNameFr').textContent = urlParams.get('familyNameFr') || urlParams.get('familyName') || '';
                document.getElementById('birthPlaceFr').textContent = urlParams.get('birthPlaceFr') || urlParams.get('birthPlace') || '';
                document.getElementById('birthDateFr').textContent = urlParams.get('birthDate') || '';
                document.getElementById('hijriDateFr').textContent = urlParams.get('hijriDate') || '';
                document.getElementById('genderFr').textContent = urlParams.get('gender') === 'ذكر' ? 'Masculin' : 'Féminin';
                document.getElementById('fatherNameFr').textContent = urlParams.get('fatherNameFr') || urlParams.get('fatherName') || '';
                document.getElementById('motherNameFr').textContent = urlParams.get('motherNameFr') || urlParams.get('motherName') || '';
                document.getElementById('actNumberFr').textContent = urlParams.get('actNumber') || '';

                // تم إزالة تاريخ الطباعة التلقائي حسب طلب الموظف
                } else {
                    console.log('⚠️ No URL parameters found for data loading');
                }
            } catch (error) {
                console.error('❌ Error in loadDataFromURL:', error);
            }
        }

        // Load citizen data by ID from IndexedDB
        async function loadCitizenDataById(citizenId) {
            try {
                console.log('🔍 Attempting to load citizen with ID:', citizenId);

                // Initialize IndexedDB if not already done
                if (typeof citizensDB === 'undefined') {
                    console.log('📚 Loading IndexedDB manager...');
                    // Load IndexedDB manager
                    const script = document.createElement('script');
                    script.src = 'indexeddb-manager.js';
                    document.head.appendChild(script);

                    // Wait for script to load
                    await new Promise(resolve => {
                        script.onload = resolve;
                    });

                    console.log('🔧 Initializing database...');
                    // Initialize database
                    await citizensDB.init();
                }

                // Get citizen data - try different methods
                console.log('📊 Fetching citizen data...');
                let citizen = await citizensDB.getCitizen(citizenId);
                console.log('📋 Retrieved citizen data (direct):', citizen);

                // If not found, try searching by different ID formats
                if (!citizen) {
                    console.log('🔍 Trying alternative search methods...');

                    // Get all citizens and search manually
                    const allCitizens = await citizensDB.getAllCitizens();
                    console.log('📊 Total citizens in database:', allCitizens.length);

                    // Try to find by exact ID match
                    citizen = allCitizens.find(c => c.id === citizenId);
                    if (citizen) {
                        console.log('✅ Found citizen by exact ID match:', citizen);
                    } else {
                        // Try to find by ID as string
                        citizen = allCitizens.find(c => c.id === citizenId.toString());
                        if (citizen) {
                            console.log('✅ Found citizen by string ID match:', citizen);
                        } else {
                            // Try to find by ID as number
                            citizen = allCitizens.find(c => c.id === parseInt(citizenId));
                            if (citizen) {
                                console.log('✅ Found citizen by number ID match:', citizen);
                            } else {
                                // Show some sample IDs for debugging
                                console.log('🔍 Sample citizen IDs in database:',
                                    allCitizens.slice(0, 5).map(c => ({id: c.id, type: typeof c.id, name: c.firstNameAr || c.personalName}))
                                );
                            }
                        }
                    }
                }

                if (citizen) {
                    console.log('📋 Filling certificate with citizen data:', citizen);

                    // Clear any existing data first
                    console.log('🧹 Clearing existing data...');

                    // Fill Arabic fields with detailed logging
                    console.log('📝 Filling Arabic fields...');
                    const firstNameAr = citizen.firstNameAr || '';
                    const familyNameAr = citizen.familyNameAr || '';
                    const birthPlaceAr = citizen.birthPlaceAr || '';
                    const actNumber = citizen.actNumber || '';

                    console.log('📝 Data to fill:', {
                        firstNameAr,
                        familyNameAr,
                        birthPlaceAr,
                        actNumber,
                        birthDate: citizen.birthDate,
                        gender: citizen.gender
                    });

                    document.getElementById('firstNameAr').textContent = firstNameAr;
                    document.getElementById('familyNameAr').textContent = familyNameAr;
                    document.getElementById('birthPlaceAr').textContent = birthPlaceAr;
                    document.getElementById('birthDateAr').textContent = formatArabicDate(citizen.birthDate) || '';
                    document.getElementById('hijriDateAr').textContent = citizen.hijriDate || '';
                    document.getElementById('genderAr').textContent = citizen.gender || '';
                    document.getElementById('fatherNameAr').textContent = citizen.fatherNameAr || '';
                    document.getElementById('motherNameAr').textContent = citizen.motherNameAr || '';
                    document.getElementById('actNumberAr').textContent = actNumber;

                    // Fill French fields
                    console.log('📝 Filling French fields...');
                    document.getElementById('firstNameFr').textContent = citizen.firstNameFr || '';
                    document.getElementById('familyNameFr').textContent = citizen.familyNameFr || '';
                    document.getElementById('birthPlaceFr').textContent = citizen.birthPlaceFr || '';
                    document.getElementById('birthDateFr').textContent = formatFrenchDate(citizen.birthDate) || '';
                    document.getElementById('hijriDateFr').textContent = citizen.hijriDate || '';
                    document.getElementById('genderFr').textContent = citizen.gender === 'ذكر' ? 'Masculin' : 'Féminin';
                    document.getElementById('fatherNameFr').textContent = citizen.fatherNameFr || '';
                    document.getElementById('motherNameFr').textContent = citizen.motherNameFr || '';
                    document.getElementById('actNumberFr').textContent = actNumber;

                    // Extract and fill years from birth date
                    if (citizen.birthDate) {
                        console.log('📅 Processing birth date:', citizen.birthDate);
                        const birthYear = new Date(citizen.birthDate).getFullYear();
                        document.getElementById('gregYearAr').textContent = birthYear;
                        document.getElementById('gregYearFr').textContent = birthYear;

                        // Calculate Hijri year (approximate)
                        const hijriYear = Math.floor(birthYear - 579);
                        document.getElementById('hijriYearAr').textContent = hijriYear;
                        document.getElementById('hijriYearFr').textContent = hijriYear;
                        console.log('📅 Years filled - Gregorian:', birthYear, 'Hijri:', hijriYear);
                    }

                    // Verify data was actually filled
                    console.log('🔍 Verifying filled data...');
                    console.log('Arabic Name:', document.getElementById('firstNameAr').textContent);
                    console.log('Family Name:', document.getElementById('familyNameAr').textContent);
                    console.log('Act Number:', document.getElementById('actNumberAr').textContent);

                    console.log('✅ تم تحميل بيانات المواطن بنجاح:', citizen);
                } else {
                    console.error('❌ لم يتم العثور على المواطن بالرقم:', citizenId);
                    alert('❌ لم يتم العثور على بيانات المواطن');
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن:', error);
                alert('❌ خطأ في تحميل بيانات المواطن');
            }
        }

        function printDocument() {
            window.print();
        }

        // تكييف حجم الخط حسب طول النص
        function adaptTextSize() {
            const birthPlaceElements = document.querySelectorAll('.birth-place-adaptive');

            birthPlaceElements.forEach(element => {
                const text = element.textContent || '';
                const textLength = text.length;

                // إزالة الكلاسات السابقة
                element.classList.remove('long-text', 'very-long-text');

                // تطبيق الكلاس حسب طول النص
                if (textLength > 25) {
                    element.classList.add('very-long-text');
                } else if (textLength > 15) {
                    element.classList.add('long-text');
                }

                console.log(`📝 Text: "${text}" (${textLength} chars) - Class: ${element.className}`);
            });
        }

        // مراقبة تغيير النص
        function observeTextChanges() {
            const birthPlaceElements = document.querySelectorAll('.birth-place-adaptive');

            birthPlaceElements.forEach(element => {
                // مراقب لتغيير المحتوى
                const observer = new MutationObserver(() => {
                    adaptTextSize();
                });

                observer.observe(element, {
                    childList: true,
                    characterData: true,
                    subtree: true
                });
            });
        }

        // وظائف تنسيق التواريخ
        function formatArabicDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const months = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ];

                const day = date.getDate();
                const monthIndex = date.getMonth(); // 0-11
                const month = months[monthIndex];
                const year = date.getFullYear();

                console.log(`📅 تنسيق التاريخ العربي: ${dateString} -> ${day} ${month} ${year}`);

                return `${day} ${month} ${year}`;
            } catch (error) {
                console.error('❌ خطأ في تنسيق التاريخ العربي:', error);
                return dateString;
            }
        }

        function formatFrenchDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const months = [
                    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
                ];

                const day = date.getDate();
                const monthIndex = date.getMonth(); // 0-11
                const month = months[monthIndex];
                const year = date.getFullYear();

                console.log(`📅 تنسيق التاريخ الفرنسي: ${dateString} -> ${day} ${month} ${year}`);

                return `${day} ${month} ${year}`;
            } catch (error) {
                console.error('❌ خطأ في تنسيق التاريخ الفرنسي:', error);
                return dateString;
            }
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Page loaded, starting data loading process...');
            loadDataFromURL();

            // تطبيق التكييف بعد تحميل البيانات
            setTimeout(() => {
                adaptTextSize();
                observeTextChanges();
            }, 1000);
        });
    </script>
</body>
</html>
