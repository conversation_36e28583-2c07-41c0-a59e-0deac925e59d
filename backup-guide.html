<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل النسخ الاحتياطي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #3498db;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .section h3 {
            color: #34495e;
            margin-bottom: 10px;
            margin-top: 15px;
        }

        .section p, .section li {
            line-height: 1.6;
            margin-bottom: 10px;
            color: #555;
        }

        .section ul {
            padding-right: 20px;
        }

        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }

        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }

        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }

        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📚 دليل النسخ الاحتياطي</h1>
            <p>كيفية إنشاء وقراءة واستعادة النسخ الاحتياطية</p>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Introduction -->
            <div class="section info">
                <h2>🎯 مقدمة</h2>
                <p>النسخ الاحتياطي ضروري لحماية بيانات المواطنين والشهادات من الفقدان. يوفر النظام عدة طرق لإنشاء وإدارة النسخ الاحتياطية.</p>
            </div>

            <!-- Creating Backups -->
            <div class="section success">
                <h2>📤 إنشاء النسخ الاحتياطية</h2>
                
                <h3>الطرق المتاحة:</h3>
                <div class="step">
                    <span class="step-number">1</span>
                    <strong>تصدير جميع البيانات:</strong> ينشئ ملف JSON يحتوي على جميع بيانات المواطنين والشهادات
                </div>
                
                <div class="step">
                    <span class="step-number">2</span>
                    <strong>حفظ الشهادة الحالية:</strong> يحفظ صورة الشهادة المحملة حالياً كملف منفصل
                </div>
                
                <div class="step">
                    <span class="step-number">3</span>
                    <strong>نسخ احتياطي تلقائي:</strong> ينشئ نسخة احتياطية مع تذكير يومي
                </div>

                <h3>تنسيق ملف النسخة الاحتياطية:</h3>
                <div class="code">
{
  "exportDate": "2025-01-09T10:30:00.000Z",
  "totalCitizens": 25,
  "withCertificates": 18,
  "version": "1.0",
  "data": [
    {
      "id": 1704567890123,
      "firstNameAr": "أحمد",
      "firstNameFr": "Ahmed",
      "familyNameAr": "محمد",
      "familyNameFr": "Mohamed",
      "actNumber": "15/2025",
      "certificateImage": {
        "data": "data:image/jpeg;base64,/9j/4AAQ...",
        "fileName": "certificate.jpg",
        "uploadDate": "2025-01-09T10:25:00.000Z"
      }
    }
  ]
}
                </div>
            </div>

            <!-- Reading Backups -->
            <div class="section info">
                <h2>👁️ قراءة النسخ الاحتياطية</h2>
                
                <h3>معاينة بدون استيراد:</h3>
                <div class="step">
                    <span class="step-number">1</span>
                    اضغط على زر "👁️ معاينة النسخة الاحتياطية"
                </div>
                
                <div class="step">
                    <span class="step-number">2</span>
                    اختر ملف النسخة الاحتياطية (.json)
                </div>
                
                <div class="step">
                    <span class="step-number">3</span>
                    ستظهر نافذة تحتوي على:
                    <ul>
                        <li>تاريخ ووقت إنشاء النسخة الاحتياطية</li>
                        <li>إجمالي عدد المواطنين</li>
                        <li>عدد الذين لديهم شهادات كاملة</li>
                        <li>عينة من أسماء المواطنين</li>
                    </ul>
                </div>

                <h3>استيراد البيانات:</h3>
                <div class="step">
                    <span class="step-number">1</span>
                    اضغط على زر "📥 استيراد البيانات"
                </div>
                
                <div class="step">
                    <span class="step-number">2</span>
                    اختر ملف النسخة الاحتياطية
                </div>
                
                <div class="step">
                    <span class="step-number">3</span>
                    راجع المعلومات المعروضة وأكد الاستيراد
                </div>
            </div>

            <!-- File Management -->
            <div class="section">
                <h2>📁 إدارة الملفات</h2>
                
                <h3>أسماء الملفات:</h3>
                <ul>
                    <li><strong>النسخ الاحتياطية:</strong> citizens_backup_YYYY-MM-DD_HH-MM-SS.json</li>
                    <li><strong>الشهادات:</strong> certificate_الاسم_العائلة_رقم_القيد_التاريخ.jpg</li>
                </ul>

                <h3>مواقع الحفظ المقترحة:</h3>
                <ul>
                    <li>📁 مجلد Documents/Citizens_Backup</li>
                    <li>☁️ التخزين السحابي (Google Drive, OneDrive)</li>
                    <li>💾 قرص صلب خارجي</li>
                    <li>🖥️ خادم الشبكة المحلية</li>
                </ul>
            </div>

            <!-- Best Practices -->
            <div class="section warning">
                <h2>⚠️ أفضل الممارسات</h2>
                
                <h3>التوقيت:</h3>
                <ul>
                    <li>اعمل نسخة احتياطية يومياً في نهاية العمل</li>
                    <li>اعمل نسخة احتياطية فورية بعد إدخال بيانات مهمة</li>
                    <li>اعمل نسخة احتياطية أسبوعية شاملة</li>
                </ul>

                <h3>التخزين:</h3>
                <ul>
                    <li>احتفظ بنسخ متعددة في أماكن مختلفة</li>
                    <li>استخدم أسماء ملفات واضحة تتضمن التاريخ</li>
                    <li>تحقق من سلامة النسخ الاحتياطية دورياً</li>
                    <li>احذف النسخ القديمة بعد فترة معقولة</li>
                </ul>

                <h3>الأمان:</h3>
                <ul>
                    <li>احم ملفات النسخ الاحتياطي بكلمة مرور</li>
                    <li>لا تشارك النسخ الاحتياطية عبر البريد الإلكتروني</li>
                    <li>استخدم التشفير للبيانات الحساسة</li>
                </ul>
            </div>

            <!-- Troubleshooting -->
            <div class="section">
                <h2>🔧 حل المشاكل الشائعة</h2>
                
                <h3>خطأ في قراءة الملف:</h3>
                <ul>
                    <li>تأكد من أن الملف بصيغة JSON</li>
                    <li>تحقق من عدم تلف الملف</li>
                    <li>جرب فتح الملف في محرر نصوص للتحقق من المحتوى</li>
                </ul>

                <h3>فقدان الصور:</h3>
                <ul>
                    <li>الصور محفوظة داخل ملف JSON كـ base64</li>
                    <li>يمكن استخراج الصور باستخدام أدوات خاصة</li>
                    <li>احفظ الشهادات كملفات منفصلة للأمان</li>
                </ul>
            </div>

            <!-- Navigation -->
            <div style="text-align: center; margin-top: 30px;">
                <a href="main-dashboard.html" class="btn">🏠 العودة للصفحة الرئيسية</a>
                <a href="citizens-database.html" class="btn">👥 إدارة البيانات</a>
                <a href="search-citizens.html" class="btn">🔍 البحث في السجلات</a>
            </div>
        </div>
    </div>
</body>
</html>
