<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قاعدة بيانات المواطنين - IndexedDB - مكتب الحالة المدنية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px 30px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a 0%, #e74c3c 50%, #c41e3a 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.2);
            padding: 10px 0;
            font-size: 0.9em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 25px 0;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            border: 3px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .header-text h1 {
            font-size: 2em;
            margin: 0 0 8px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 5px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        .header-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            font-size: 0.9em;
        }

        .current-time {
            background: rgba(255,255,255,0.15);
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .user-info {
            background: rgba(255,255,255,0.1);
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .nav-link {
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.1));
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .nav-link:hover {
            background: linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.15));
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255,255,255,0.2);
        }

        .nav-btn {
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.15);
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
        }

        .header p {
            text-align: center;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .form-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #c41e3a;
            padding-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 10px rgba(196, 30, 58, 0.3);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 3px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            padding: 15px 20px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 3px solid #c41e3a;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stat-number {
            font-size: 1.8em;
            font-weight: 800;
            color: #e74c3c;
            margin-bottom: 8px;
            text-shadow: 1px 1px 2px rgba(231, 76, 60, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(231, 76, 60, 0.3));
        }

        .stat-label {
            color: #6c757d;
            font-weight: 600;
            font-size: 12px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        /* Virtual Keyboard Styles */
        .key-btn {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 2px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 35px;
            text-align: center;
        }

        .key-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-1px);
        }

        .key-btn:active {
            background: #dee2e6;
            transform: translateY(0);
        }

        .space-btn {
            min-width: 120px;
            background: #f8f9fa;
        }

        .delete-btn {
            background: #ffeaa7;
            color: #856404;
        }

        .delete-btn:hover {
            background: #fdcb6e;
        }

        .clear-btn {
            background: #fab1a0;
            color: #d63031;
        }

        .clear-btn:hover {
            background: #e17055;
            color: white;
        }

        /* Draggable keyboard styles */
        #virtualKeyboard.dragging {
            opacity: 0.8;
            transform: rotate(2deg);
            transition: none;
        }

        #keyboardHeader:hover {
            background: #dee2e6 !important;
        }

        #keyboardHeader:active {
            background: #ced4da !important;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 1.6em;
            }

            .morocco-emblem {
                width: 60px;
                height: 60px;
                font-size: 1.8em;
            }

            .nav-links {
                flex-direction: column;
                gap: 10px;
            }

            .nav-link {
                text-align: center;
                padding: 10px 15px;
            }

            .key-btn {
                padding: 6px 8px;
                font-size: 12px;
                min-width: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">قاعدة بيانات المواطنين</div>
                            <div class="department">مكتب الحالة المدنية - أيير</div>
                        </div>
                    </div>
                    <div class="header-left">
                        <div class="current-time" id="currentTime">جاري التحميل...</div>
                        <div class="user-info">👤 مستخدم النظام</div>
                    </div>
                </div>

                <div class="nav-links">
                    <a href="main-dashboard.html" class="nav-link">🏠 الصفحة الرئيسية</a>
                    <a href="search-citizens.html" class="nav-link">🔍 البحث في السجلات</a>
                    <a href="dual-birth-certificate.html" class="nav-link">📜 عقود الازدياد</a>
                    <a href="death-data-entry.html" class="nav-link">⚱️ إدخال وفاة</a>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalCitizens">0</div>
                <div class="stat-label">إجمالي المواطنين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="maleCount">0</div>
                <div class="stat-label">ذكور</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="femaleCount">0</div>
                <div class="stat-label">إناث</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayRegistrations">0</div>
                <div class="stat-label">تسجيلات اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="withCertificates">0</div>
                <div class="stat-label">مع شهادات كاملة</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Form Section -->
            <div class="form-section">
                <h2>📝 تسجيل مواطن جديد</h2>

                <div id="alertContainer"></div>
                <div id="loadingIndicator" class="loading" style="display: none;">
                    ⏳ جاري التحميل...
                </div>

                <form id="citizenForm">
                    <div class="form-group">
                        <label for="firstNameAr">الاسم الشخصي (عربي):</label>
                        <input type="text" id="firstNameAr" name="firstNameAr" required placeholder="أحمد">
                    </div>

                    <div class="form-group">
                        <label for="firstNameFr">الاسم الشخصي (فرنسي):</label>
                        <input type="text" id="firstNameFr" name="firstNameFr" required placeholder="Ahmed">
                    </div>

                    <div class="form-group">
                        <label for="familyNameAr">الاسم العائلي (عربي):</label>
                        <input type="text" id="familyNameAr" name="familyNameAr" required placeholder="محمد علي">
                    </div>

                    <div class="form-group">
                        <label for="familyNameFr">الاسم العائلي (فرنسي):</label>
                        <input type="text" id="familyNameFr" name="familyNameFr" required placeholder="Mohamed Ali">
                    </div>

                    <div class="form-group">
                        <label for="birthPlaceAr">مكان الازدياد (عربي):</label>
                        <input type="text" id="birthPlaceAr" name="birthPlaceAr" required placeholder="الرباط">
                    </div>

                    <div class="form-group">
                        <label for="birthPlaceFr">مكان الازدياد (فرنسي):</label>
                        <input type="text" id="birthPlaceFr" name="birthPlaceFr" required placeholder="Rabat">
                    </div>

                    <div class="form-group">
                        <label for="birthDate">تاريخ الازدياد (ميلادي):</label>
                        <input type="date" id="birthDate" name="birthDate" required>
                    </div>

                    <div class="form-group">
                        <label for="hijriDate">التاريخ الهجري:</label>
                        <div style="position: relative;">
                            <input type="text" id="hijriDate" name="hijriDate" placeholder="سيتم حسابه تلقائياً" readonly style="background: #f8f9fa; border: 2px solid #e9ecef; padding-right: 35px;">
                            <button type="button" onclick="enableHijriEdit()" style="position: absolute; left: 5px; top: 50%; transform: translateY(-50%); background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;" title="تعديل يدوي">✏️</button>
                        </div>
                        <small style="color: #6c757d; font-size: 0.8em; display: block; margin-top: 4px;">📅 يتم حساب التاريخ الهجري تلقائياً من التاريخ الميلادي</small>
                    </div>

                    <div class="form-group">
                        <label for="gender">الجنس:</label>
                        <select id="gender" name="gender" required>
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="fatherNameAr">اسم الوالد (عربي):</label>
                        <input type="text" id="fatherNameAr" name="fatherNameAr" required placeholder="محمد علي حسن">
                    </div>

                    <div class="form-group">
                        <label for="fatherNameFr">اسم الوالد (فرنسي):</label>
                        <input type="text" id="fatherNameFr" name="fatherNameFr" required placeholder="Mohamed Ali Hassan">
                    </div>

                    <div class="form-group">
                        <label for="motherNameAr">اسم الوالدة (عربي):</label>
                        <input type="text" id="motherNameAr" name="motherNameAr" required placeholder="فاطمة أحمد محمد">
                    </div>

                    <div class="form-group">
                        <label for="motherNameFr">اسم الوالدة (فرنسي):</label>
                        <input type="text" id="motherNameFr" name="motherNameFr" required placeholder="Fatima Ahmed Mohamed">
                    </div>

                    <div class="form-group">
                        <label for="actNumber">رقم القيد:</label>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="actNumberMode" value="auto" checked onchange="toggleActNumberMode()"> تلقائي
                            </label>
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="actNumberMode" value="manual" onchange="toggleActNumberMode()"> يدوي
                            </label>
                        </div>
                        <input type="text" id="actNumber" name="actNumber" required readonly style="background-color: #f8f9fa;">
                        <small id="actNumberHelp" style="color: #6c757d; font-size: 12px;">سيتم توليد الرقم تلقائياً بصيغة: رقم/سنة</small>
                    </div>

                    <div class="form-group">
                        <label for="registrationDate">تاريخ التسجيل:</label>
                        <input type="date" id="registrationDate" name="registrationDate" required>
                    </div>

                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 8px; flex-wrap: wrap; justify-content: center; margin-top: 15px;">
                        <button type="button" class="btn btn-success" onclick="saveAndNew()" style="font-size: 14px; padding: 10px 20px;" title="اختصار: Ctrl+S">💾➕ حفظ وإضافة جديد</button>
                        <button type="button" class="btn btn-warning" onclick="clearForm()" style="font-size: 14px; padding: 10px 20px;" title="اختصار: Ctrl+R">🗑️ مسح النموذج</button>
                        <button type="button" class="btn btn-info" onclick="toggleKeyboard()" style="font-size: 14px; padding: 10px 20px;">⌨️ لوحة المفاتيح</button>
                        <a href="search-citizens.html" class="btn btn-secondary" style="font-size: 14px; padding: 10px 20px;">🔍 البحث في السجلات</a>
                        <a href="death-certificate.html" class="btn btn-info" style="font-size: 14px; padding: 10px 20px;">📋 نسخة موجزة من رسم الوفاة</a>
                    </div>

                    <!-- Keyboard Shortcuts Info -->
                    <div style="text-align: center; margin-top: 8px; font-size: 11px; color: #6c757d;">
                        💡 اختصارات لوحة المفاتيح: Ctrl+S (حفظ وجديد) • Ctrl+R (مسح)
                    </div>
                </form>

                <!-- Virtual Keyboard -->
                <div id="virtualKeyboard" style="display: none; position: fixed; z-index: 1000; background: #f8f9fa; border: 2px solid #007bff; border-radius: 8px; padding: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); max-width: 90vw; cursor: move;">
                    <div id="keyboardHeader" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; cursor: move; padding: 5px; background: #e9ecef; border-radius: 4px; margin: -5px -5px 10px -5px;">
                        <h6 style="margin: 0; color: #495057; user-select: none;">🎹 لوحة المفاتيح الافتراضية <small style="color: #6c757d;">(اسحب لتحريك)</small></h6>
                        <div>
                            <button type="button" id="arabicKeyboard" class="btn btn-primary" style="font-size: 12px; padding: 4px 8px; margin: 2px;">🇲🇦 عربي</button>
                            <button type="button" id="frenchKeyboard" class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px; margin: 2px;">🇫🇷 فرنسي</button>
                            <button type="button" id="closeKeyboard" class="btn btn-danger" style="font-size: 12px; padding: 4px 8px; margin: 2px;">✖</button>
                        </div>
                    </div>

                    <!-- Arabic Keyboard -->
                    <div id="arabicKeys" style="display: block;">
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="ض">ض</button>
                            <button type="button" class="key-btn" data-char="ص">ص</button>
                            <button type="button" class="key-btn" data-char="ث">ث</button>
                            <button type="button" class="key-btn" data-char="ق">ق</button>
                            <button type="button" class="key-btn" data-char="ف">ف</button>
                            <button type="button" class="key-btn" data-char="غ">غ</button>
                            <button type="button" class="key-btn" data-char="ع">ع</button>
                            <button type="button" class="key-btn" data-char="ه">ه</button>
                            <button type="button" class="key-btn" data-char="خ">خ</button>
                            <button type="button" class="key-btn" data-char="ح">ح</button>
                            <button type="button" class="key-btn" data-char="ج">ج</button>
                            <button type="button" class="key-btn" data-char="د">د</button>
                        </div>
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="ش">ش</button>
                            <button type="button" class="key-btn" data-char="س">س</button>
                            <button type="button" class="key-btn" data-char="ي">ي</button>
                            <button type="button" class="key-btn" data-char="ب">ب</button>
                            <button type="button" class="key-btn" data-char="ل">ل</button>
                            <button type="button" class="key-btn" data-char="ا">ا</button>
                            <button type="button" class="key-btn" data-char="ت">ت</button>
                            <button type="button" class="key-btn" data-char="ن">ن</button>
                            <button type="button" class="key-btn" data-char="م">م</button>
                            <button type="button" class="key-btn" data-char="ك">ك</button>
                            <button type="button" class="key-btn" data-char="ط">ط</button>
                        </div>
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="ئ">ئ</button>
                            <button type="button" class="key-btn" data-char="ء">ء</button>
                            <button type="button" class="key-btn" data-char="ؤ">ؤ</button>
                            <button type="button" class="key-btn" data-char="ر">ر</button>
                            <button type="button" class="key-btn" data-char="لا">لا</button>
                            <button type="button" class="key-btn" data-char="ى">ى</button>
                            <button type="button" class="key-btn" data-char="ة">ة</button>
                            <button type="button" class="key-btn" data-char="و">و</button>
                            <button type="button" class="key-btn" data-char="ز">ز</button>
                            <button type="button" class="key-btn" data-char="ظ">ظ</button>
                        </div>
                        <div>
                            <button type="button" class="key-btn space-btn" data-char=" ">مسافة</button>
                            <button type="button" class="key-btn delete-btn" data-action="backspace">حذف</button>
                            <button type="button" class="key-btn clear-btn" data-action="clear">مسح الكل</button>
                        </div>
                    </div>

                    <!-- French Keyboard -->
                    <div id="frenchKeys" style="display: none;">
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="A">A</button>
                            <button type="button" class="key-btn" data-char="Z">Z</button>
                            <button type="button" class="key-btn" data-char="E">E</button>
                            <button type="button" class="key-btn" data-char="R">R</button>
                            <button type="button" class="key-btn" data-char="T">T</button>
                            <button type="button" class="key-btn" data-char="Y">Y</button>
                            <button type="button" class="key-btn" data-char="U">U</button>
                            <button type="button" class="key-btn" data-char="I">I</button>
                            <button type="button" class="key-btn" data-char="O">O</button>
                            <button type="button" class="key-btn" data-char="P">P</button>
                        </div>
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="Q">Q</button>
                            <button type="button" class="key-btn" data-char="S">S</button>
                            <button type="button" class="key-btn" data-char="D">D</button>
                            <button type="button" class="key-btn" data-char="F">F</button>
                            <button type="button" class="key-btn" data-char="G">G</button>
                            <button type="button" class="key-btn" data-char="H">H</button>
                            <button type="button" class="key-btn" data-char="J">J</button>
                            <button type="button" class="key-btn" data-char="K">K</button>
                            <button type="button" class="key-btn" data-char="L">L</button>
                            <button type="button" class="key-btn" data-char="M">M</button>
                        </div>
                        <div style="margin-bottom: 5px;">
                            <button type="button" class="key-btn" data-char="W">W</button>
                            <button type="button" class="key-btn" data-char="X">X</button>
                            <button type="button" class="key-btn" data-char="C">C</button>
                            <button type="button" class="key-btn" data-char="V">V</button>
                            <button type="button" class="key-btn" data-char="B">B</button>
                            <button type="button" class="key-btn" data-char="N">N</button>
                            <button type="button" class="key-btn" data-char="É">É</button>
                            <button type="button" class="key-btn" data-char="È">È</button>
                            <button type="button" class="key-btn" data-char="Ç">Ç</button>
                            <button type="button" class="key-btn" data-char="À">À</button>
                        </div>
                        <div>
                            <button type="button" class="key-btn space-btn" data-char=" ">Espace</button>
                            <button type="button" class="key-btn delete-btn" data-action="backspace">Effacer</button>
                            <button type="button" class="key-btn clear-btn" data-action="clear">Tout effacer</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Certificate Section -->
            <div class="form-section">
                <h2>📄 إدارة الشهادة الكاملة</h2>

                <!-- Upload Button -->
                <div style="text-align: center; margin-bottom: 15px;">
                    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()" style="font-size: 14px; padding: 10px 20px;">
                        📷 تحميل الشهادة الكاملة
                    </button>
                </div>

                <!-- Display Area -->
                <div class="upload-area" id="uploadArea" style="background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; padding: 20px; text-align: center; min-height: 200px; transition: all 0.3s ease;">
                    <div id="uploadContent">
                        <div style="color: #6c757d; font-size: 32px; margin-bottom: 12px;">📄</div>
                        <h4 style="color: #6c757d; margin-bottom: 8px; font-size: 16px;">منطقة عرض الشهادة</h4>
                        <p style="color: #6c757d; margin-bottom: 8px; font-size: 13px;">استخدم الزر أعلاه لتحميل الشهادة</p>
                        <p style="color: #95a5a6; font-size: 12px;">يدعم: JPG, PNG, PDF</p>
                    </div>

                    <!-- Uploaded Image Display -->
                    <div id="imageDisplay" style="display: none;">
                        <div id="imageContainer" style="margin-bottom: 10px; position: relative; overflow: hidden; border-radius: 6px; border: 1px solid #ddd; cursor: zoom-in;">
                            <img id="uploadedImage" style="max-width: 100%; max-height: 350px; border-radius: 6px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); transition: transform 0.3s ease; transform-origin: center;">
                        </div>
                    </div>
                </div>

                <!-- Image Controls -->
                <div id="imageControls" style="display: none; margin-top: 10px; text-align: center;">
                    <div style="margin-bottom: 6px; color: #6c757d; font-size: 12px;">
                        💡 اضغط على الصورة للتكبير • مرر عجلة الفأرة للتكبير والتصغير
                    </div>
                    <div style="margin-bottom: 8px;">
                        <button class="btn btn-primary" onclick="zoomIn(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔍+</button>
                        <button class="btn btn-primary" onclick="zoomOut(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔍-</button>
                        <button class="btn btn-secondary" onclick="resetZoom(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔄 إعادة تعيين</button>
                    </div>
                    <button class="btn btn-secondary" onclick="removeImage(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🗑️ إزالة الصورة</button>
                </div>

                <!-- Hidden File Input -->
                <input type="file" id="fileInput" accept="image/*,.pdf" style="display: none;" onchange="handleFileUpload(event)">

                <!-- Instructions -->
                <div style="margin-top: 12px; padding: 10px; background: #e8f5e8; border: 1px solid #c3e6cb; border-radius: 6px;">
                    <h5 style="color: #155724; margin-bottom: 6px; font-size: 14px;">📋 تعليمات الاستخدام:</h5>
                    <ul style="color: #155724; text-align: right; margin: 0; padding-right: 15px; font-size: 12px;">
                        <li>قم بتحميل صورة الشهادة الكاملة للمواطن</li>
                        <li>اقرأ المعلومات من الشهادة المعروضة</li>
                        <li>أدخل البيانات يدوياً في النموذج على اليسار</li>
                        <li>تأكد من صحة جميع المعلومات قبل الحفظ</li>
                        <li>يمكنك تكبير الصورة لرؤية التفاصيل بوضوح</li>
                    </ul>
                </div>

                <!-- Backup Section -->
                <div style="margin-top: 12px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
                    <h5 style="color: #856404; margin-bottom: 8px; font-size: 14px;">💾 النسخ الاحتياطي والأرشفة</h5>
                    <div style="display: flex; gap: 6px; flex-wrap: wrap; justify-content: center;">
                        <button type="button" class="btn btn-primary" onclick="exportAllData()" style="font-size: 12px; padding: 6px 12px;">📤 تصدير البيانات</button>
                        <button type="button" class="btn btn-success" onclick="importData()" style="font-size: 12px; padding: 6px 12px;">📥 استيراد البيانات</button>
                        <button type="button" class="btn btn-info" onclick="autoBackup()" style="font-size: 12px; padding: 6px 12px;">🔄 نسخ احتياطي</button>
                    </div>
                    <input type="file" id="importFileInput" accept=".json" style="display: none;" onchange="handleImportFile(event)">
                    <div style="margin-top: 6px; font-size: 11px; color: #856404; text-align: center;">
                        💡 استخدم التصدير/الاستيراد لنقل البيانات بين الأجهزة
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include IndexedDB Manager -->
    <script src="indexeddb-manager.js"></script>
    <script>
        // Global variables
        let editingId = null;
        let currentCertificateImage = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // Initialize IndexedDB
                await citizensDB.init();
                console.log('تم تهيئة IndexedDB بنجاح');

                // Update statistics
                await updateStatistics();

                // Set today's date as default
                document.getElementById('registrationDate').value = new Date().toISOString().split('T')[0];

                // Check for edit parameters FIRST
                checkForEditParameters();

                // Generate automatic act number ONLY if not editing
                if (!editingId) {
                    generateActNumber();
                }

                // Add keyboard shortcuts
                setupKeyboardShortcuts();

                // Initialize virtual keyboard
                initKeyboardEvents();

            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);
                showAlert('❌ خطأ في تهيئة قاعدة البيانات', 'error');
            }
        });

        // Setup keyboard shortcuts
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl+S or Cmd+S for save and new (الوظيفة الوحيدة المتبقية)
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    saveAndNew();
                }

                // Ctrl+R or Cmd+R for clear form (prevent default browser refresh)
                if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                    e.preventDefault();
                    clearForm();
                }
            });
        }

        // Update statistics with IndexedDB support
        async function updateStatistics() {
            try {
                const dbInfo = await citizensDB.getDatabaseInfo();

                if (dbInfo) {
                    document.getElementById('totalCitizens').textContent = dbInfo.totalCitizens.toLocaleString();
                    document.getElementById('withCertificates').textContent = dbInfo.withCertificates.toLocaleString();

                    // Get all citizens for detailed stats
                    const citizens = await citizensDB.getAllCitizens();
                    const males = citizens.filter(c => c.gender === 'ذكر').length;
                    const females = citizens.filter(c => c.gender === 'أنثى').length;

                    const today = new Date().toISOString().split('T')[0];
                    const todayRegistrations = citizens.filter(c => c.registrationDate === today).length;

                    document.getElementById('maleCount').textContent = males.toLocaleString();
                    document.getElementById('femaleCount').textContent = females.toLocaleString();
                    document.getElementById('todayRegistrations').textContent = todayRegistrations.toLocaleString();
                }
            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // Generate automatic act number with uniqueness guarantee
        async function generateActNumber() {
            try {
                const citizens = await citizensDB.getAllCitizens();
                const currentYear = new Date().getFullYear();

                // جمع جميع أرقام العقود الموجودة لهذه السنة
                const existingActNumbers = new Set();
                citizens.forEach(citizen => {
                    if (citizen.actNumber) {
                        const actNumber = citizen.actNumber.toString();
                        // التحقق من أن الرقم ينتهي بالسنة الحالية
                        if (actNumber.endsWith(`/${currentYear}`) || actNumber.endsWith(`-${currentYear}`) || actNumber.endsWith(`.${currentYear}`)) {
                            existingActNumbers.add(actNumber);
                        }
                    }
                });

                console.log(`📊 Existing act numbers for ${currentYear}:`, Array.from(existingActNumbers));

                // البحث عن أول رقم متاح
                let nextNumber = 1;
                let actNumber = `${nextNumber}/${currentYear}`;

                while (existingActNumbers.has(actNumber)) {
                    nextNumber++;
                    actNumber = `${nextNumber}/${currentYear}`;
                }

                console.log(`✅ Generated unique act number: ${actNumber}`);
                document.getElementById('actNumber').value = actNumber;

                return actNumber;
            } catch (error) {
                console.error('خطأ في توليد رقم القيد:', error);
                const currentYear = new Date().getFullYear();
                const fallbackNumber = `1/${currentYear}`;
                document.getElementById('actNumber').value = fallbackNumber;
                return fallbackNumber;
            }
        }

        // Toggle act number mode
        function toggleActNumberMode() {
            const mode = document.querySelector('input[name="actNumberMode"]:checked').value;
            const actNumberInput = document.getElementById('actNumber');
            const helpText = document.getElementById('actNumberHelp');

            if (mode === 'auto') {
                actNumberInput.readOnly = true;
                actNumberInput.style.backgroundColor = '#f8f9fa';
                helpText.textContent = 'سيتم توليد الرقم تلقائياً بصيغة: رقم/سنة';
                generateActNumber();
            } else {
                actNumberInput.readOnly = false;
                actNumberInput.style.backgroundColor = 'white';
                helpText.textContent = 'أدخل رقم القيد يدوياً بصيغة: رقم/سنة';
                actNumberInput.focus();
            }
        }

        // Check if act number is unique
        async function checkActNumberUniqueness(actNumber, excludeId = null) {
            try {
                const citizens = await citizensDB.getAllCitizens();
                const duplicate = citizens.find(citizen =>
                    citizen.actNumber === actNumber && citizen.id !== excludeId
                );
                return !duplicate; // true if unique, false if duplicate
            } catch (error) {
                console.error('خطأ في فحص فرادة رقم العقد:', error);
                return true; // افتراض الفرادة في حالة الخطأ
            }
        }

        // Handle form submission
        document.getElementById('citizenForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const loadingIndicator = document.getElementById('loadingIndicator');
            loadingIndicator.style.display = 'block';

            try {
                const formData = new FormData(e.target);
                const actNumber = formData.get('actNumber');

                // التحقق من فرادة رقم العقد
                const isUnique = await checkActNumberUniqueness(actNumber, editingId);
                if (!isUnique) {
                    showAlert('❌ رقم العقد موجود مسبقاً! يرجى استخدام رقم آخر.', 'error');

                    // إذا كان في الوضع التلقائي، قم بتوليد رقم جديد
                    if (document.querySelector('input[name="actNumberMode"]:checked').value === 'auto') {
                        await generateActNumber();
                        showAlert('🔄 تم توليد رقم عقد جديد تلقائياً.', 'info');
                    }
                    loadingIndicator.style.display = 'none';
                    return;
                }

                // Generate unique ID for new citizens
                const citizenId = editingId || `citizen_${Date.now()}_${Math.floor(Math.random() * 100000)}_${performance.now().toString().replace('.', '')}`;

                const citizenData = {
                    id: citizenId, // إضافة ID مطلوب لقاعدة البيانات
                    firstNameAr: formData.get('firstNameAr'),
                    firstNameFr: formData.get('firstNameFr'),
                    familyNameAr: formData.get('familyNameAr'),
                    familyNameFr: formData.get('familyNameFr'),
                    birthPlaceAr: formData.get('birthPlaceAr'),
                    birthPlaceFr: formData.get('birthPlaceFr'),
                    birthDate: formData.get('birthDate'),
                    hijriDate: formData.get('hijriDate'),
                    gender: formData.get('gender'),
                    fatherNameAr: formData.get('fatherNameAr'),
                    fatherNameFr: formData.get('fatherNameFr'),
                    motherNameAr: formData.get('motherNameAr'),
                    motherNameFr: formData.get('motherNameFr'),
                    actNumber: formData.get('actNumber'),
                    registrationDate: formData.get('registrationDate'),
                    createdAt: new Date().toISOString(),
                    timestamp: Date.now() // إضافة timestamp للتوافق
                };

                // Add certificate image if available
                if (currentCertificateImage) {
                    citizenData.certificateImage = currentCertificateImage;
                }

                // الآن دائماً "حفظ وإضافة جديد" بما أن هذا هو الزر الوحيد
                const isSaveAndNew = e.target.dataset.saveAndNew === 'true' || !editingId; // دائماً true للسجلات الجديدة

                if (editingId) {
                    // Update existing citizen - ID already set above
                    await citizensDB.updateCitizen(citizenData);
                    showAlert('✅ تم تحديث بيانات المواطن بنجاح', 'success');
                    editingId = null;
                } else {
                    // Add new citizen - دائماً مع إعداد للتسجيل التالي
                    console.log('إضافة مواطن جديد بـ ID:', citizenData.id);
                    console.log('بيانات المواطن:', citizenData);

                    // التحقق من وجود ID
                    if (!citizenData.id) {
                        throw new Error('لم يتم توليد ID للمواطن');
                    }

                    await citizensDB.addCitizen(citizenData);
                    showAlert('✅ تم حفظ البيانات بنجاح - جاهز لإدخال مواطن جديد', 'success');
                }

                // Update statistics
                await updateStatistics();

                // دائماً مسح النموذج وإعداده للتسجيل التالي
                clearForm();

                // Remove the flag if exists
                if (e.target.dataset.saveAndNew) {
                    delete e.target.dataset.saveAndNew;
                }

                // Generate new act number for next entry
                if (document.querySelector('input[name="actNumberMode"]:checked').value === 'auto') {
                    generateActNumber();
                }

            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showAlert('❌ خطأ في حفظ البيانات: ' + error.message, 'error');
            } finally {
                loadingIndicator.style.display = 'none';
            }
        });

        // Save and add new function (الوظيفة الافتراضية الآن)
        async function saveAndNew() {
            // Trigger form submission with save and new flag
            const form = document.getElementById('citizenForm');
            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });

            // Add flag to indicate this is save and new
            form.dataset.saveAndNew = 'true';
            form.dispatchEvent(submitEvent);
        }

        // Clear form
        function clearForm() {
            document.getElementById('citizenForm').reset();
            document.getElementById('registrationDate').value = new Date().toISOString().split('T')[0];
            removeImage();
            editingId = null;

            // Reset act number mode to auto
            document.querySelector('input[name="actNumberMode"][value="auto"]').checked = true;
            toggleActNumberMode();

            // Focus on first field
            document.getElementById('firstNameAr').focus();
        }

        // Show alert
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // Check for edit parameters
        function checkForEditParameters() {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('edit') === 'true') {
                const citizenId = urlParams.get('id'); // استخدام النص كما هو بدلاً من parseInt

                // Fill form with citizen data
                document.getElementById('firstNameAr').value = urlParams.get('firstNameAr') || '';
                document.getElementById('firstNameFr').value = urlParams.get('firstNameFr') || '';
                document.getElementById('familyNameAr').value = urlParams.get('familyNameAr') || '';
                document.getElementById('familyNameFr').value = urlParams.get('familyNameFr') || '';
                document.getElementById('birthPlaceAr').value = urlParams.get('birthPlaceAr') || '';
                document.getElementById('birthPlaceFr').value = urlParams.get('birthPlaceFr') || '';
                document.getElementById('birthDate').value = urlParams.get('birthDate') || '';
                document.getElementById('hijriDate').value = urlParams.get('hijriDate') || '';
                document.getElementById('gender').value = urlParams.get('gender') || '';
                document.getElementById('fatherNameAr').value = urlParams.get('fatherNameAr') || '';
                document.getElementById('fatherNameFr').value = urlParams.get('fatherNameFr') || '';
                document.getElementById('motherNameAr').value = urlParams.get('motherNameAr') || '';
                document.getElementById('motherNameFr').value = urlParams.get('motherNameFr') || '';

                // تعيين رقم العقد والتبديل للوضع اليدوي
                const actNumber = urlParams.get('actNumber') || '';
                document.getElementById('actNumber').value = actNumber;

                // تعيين الوضع اليدوي لرقم العقد لمنع التوليد التلقائي
                if (actNumber) {
                    document.querySelector('input[name="actNumberMode"][value="manual"]').checked = true;
                    toggleActNumberMode();
                    console.log('✅ تم تعيين رقم العقد للوضع اليدوي:', actNumber);
                }

                document.getElementById('registrationDate').value = urlParams.get('registrationDate') || '';

                // تحميل بيانات الصورة إن وجدت
                const hasCertificate = urlParams.get('hasCertificate') === 'true';
                const certificateImageData = urlParams.get('certificateImageData');
                const certificateFileName = urlParams.get('certificateFileName');

                if (hasCertificate && certificateImageData) {
                    console.log('🖼️ تحميل صورة الشهادة...');

                    // حفظ بيانات الصورة
                    currentCertificateImage = {
                        data: certificateImageData,
                        fileName: certificateFileName || 'شهادة_ميلاد.jpg',
                        hasImage: true,
                        uploadDate: new Date().toISOString(),
                        type: 'birth-certificate'
                    };

                    // عرض الصورة
                    displayImage(certificateImageData);

                    console.log('✅ تم تحميل صورة الشهادة بنجاح');
                }

                // Set editing mode
                editingId = citizenId;

                // Update form title
                document.querySelector('.form-section h2').innerHTML = '✏️ تعديل بيانات المواطن';

                console.log('✅ تم تحميل بيانات المواطن للتعديل:', citizenId);
                console.log('📋 رقم العقد المحمل:', actNumber);

                // Clear URL parameters
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }

        // Image handling functions
        let currentZoom = 1;
        let isDragging = false;
        let startX, startY, scrollLeft, scrollTop;

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const imageData = e.target.result;

                // Store image data
                currentCertificateImage = {
                    data: imageData,
                    fileName: file.name,
                    uploadDate: new Date().toISOString(),
                    hasImage: true
                };

                // Display image
                displayImage(imageData);
            };
            reader.readAsDataURL(file);
        }

        function displayImage(imageSrc) {
            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');
            const uploadedImage = document.getElementById('uploadedImage');

            uploadContent.style.display = 'none';
            imageDisplay.style.display = 'block';
            imageControls.style.display = 'block';
            uploadedImage.src = imageSrc;

            // Reset zoom
            currentZoom = 1;
            uploadedImage.style.transform = 'scale(1)';

            // Add click to zoom
            uploadedImage.onclick = function() {
                if (currentZoom === 1) {
                    zoomIn();
                } else {
                    resetZoom();
                }
            };

            // Add wheel zoom
            uploadedImage.addEventListener('wheel', function(e) {
                e.preventDefault();
                if (e.deltaY < 0) {
                    zoomIn();
                } else {
                    zoomOut();
                }
            });
        }

        function zoomIn(event) {
            if (event) event.preventDefault();
            currentZoom = Math.min(currentZoom * 1.2, 5);
            document.getElementById('uploadedImage').style.transform = `scale(${currentZoom})`;
        }

        function zoomOut(event) {
            if (event) event.preventDefault();
            currentZoom = Math.max(currentZoom / 1.2, 0.5);
            document.getElementById('uploadedImage').style.transform = `scale(${currentZoom})`;
        }

        function resetZoom(event) {
            if (event) event.preventDefault();
            currentZoom = 1;
            document.getElementById('uploadedImage').style.transform = 'scale(1)';
        }

        function removeImage(event) {
            if (event) event.preventDefault();

            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');

            uploadContent.style.display = 'block';
            imageDisplay.style.display = 'none';
            imageControls.style.display = 'none';

            currentCertificateImage = null;
            document.getElementById('fileInput').value = '';
        }

        // Export all data
        async function exportAllData() {
            try {
                const citizens = await citizensDB.getAllCitizens();

                if (citizens.length === 0) {
                    alert('⚠️ لا توجد بيانات للتصدير');
                    return;
                }

                const currentDate = new Date();
                const dateStr = currentDate.toISOString().split('T')[0];
                const timeStr = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                const dbInfo = await citizensDB.getDatabaseInfo();

                const backupData = {
                    exportDate: currentDate.toISOString(),
                    totalCitizens: citizens.length,
                    withCertificates: dbInfo ? dbInfo.withCertificates : 0,
                    version: '3.0-IndexedDB',
                    storageInfo: dbInfo,
                    data: citizens
                };

                const dataStr = JSON.stringify(backupData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `citizens_backup_indexeddb_${dateStr}_${timeStr}.json`;
                link.click();
                URL.revokeObjectURL(url);

                showAlert(`✅ تم تصدير النسخة الاحتياطية بنجاح\nعدد المواطنين: ${citizens.length.toLocaleString()}`, 'success');

            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                showAlert('❌ خطأ في تصدير البيانات', 'error');
            }
        }

        // Import data function
        function importData() {
            document.getElementById('importFileInput').click();
        }

        // Handle import file
        async function handleImportFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const importData = JSON.parse(text);

                // Validate import data
                if (!importData.data || !Array.isArray(importData.data)) {
                    throw new Error('ملف البيانات غير صحيح');
                }

                const confirmMessage = `هل تريد استيراد ${importData.data.length} سجل؟\n\n` +
                                     `⚠️ تحذير: سيتم دمج البيانات مع البيانات الموجودة\n` +
                                     `📅 تاريخ النسخة: ${importData.exportDate || 'غير محدد'}\n` +
                                     `📊 الإصدار: ${importData.version || 'غير محدد'}`;

                if (!confirm(confirmMessage)) return;

                let imported = 0;
                let errors = 0;

                showAlert('🔄 جاري استيراد البيانات...', 'success');

                for (const citizen of importData.data) {
                    try {
                        // Generate new ID if not exists or conflicts
                        if (!citizen.id) {
                            citizen.id = `imported_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
                        }

                        await citizensDB.addCitizen(citizen);
                        imported++;
                    } catch (error) {
                        console.warn('فشل في استيراد السجل:', citizen, error);
                        errors++;
                    }
                }

                await updateStatistics();
                showAlert(`✅ تم استيراد ${imported} سجل بنجاح${errors > 0 ? ` (${errors} أخطاء)` : ''}`, 'success');

            } catch (error) {
                console.error('خطأ في الاستيراد:', error);
                showAlert('❌ خطأ في استيراد البيانات: ' + error.message, 'error');
            }

            // Clear file input
            event.target.value = '';
        }

        // Auto backup
        async function autoBackup() {
            try {
                const citizens = await citizensDB.getAllCitizens();

                if (citizens.length === 0) {
                    showAlert('⚠️ لا توجد بيانات للنسخ الاحتياطي', 'error');
                    return;
                }

                const lastBackup = localStorage.getItem('lastBackupDate');
                const today = new Date().toDateString();

                if (lastBackup === today) {
                    const confirmBackup = confirm('تم عمل نسخة احتياطية اليوم بالفعل.\nهل تريد عمل نسخة احتياطية أخرى؟');
                    if (!confirmBackup) return;
                }

                await exportAllData();
                localStorage.setItem('lastBackupDate', today);

            } catch (error) {
                console.error('خطأ في النسخ الاحتياطي:', error);
                showAlert('❌ خطأ في النسخ الاحتياطي', 'error');
            }
        }

        // Virtual Keyboard Functions
        let currentActiveField = null;
        let keyboardIsDragging = false;
        let keyboardDragOffset = { x: 0, y: 0 };

        // Toggle keyboard visibility
        function toggleKeyboard() {
            const keyboard = document.getElementById('virtualKeyboard');
            if (keyboard.style.display === 'none') {
                showKeyboard();
            } else {
                hideKeyboard();
            }
        }

        // Show keyboard under active field
        function showKeyboard() {
            const keyboard = document.getElementById('virtualKeyboard');
            keyboard.style.display = 'block';

            // Try to load saved position first
            const hasSavedPosition = loadKeyboardPosition();

            if (!hasSavedPosition) {
                // If no saved position, position based on active field or center
                if (currentActiveField) {
                    positionKeyboard(currentActiveField);
                } else {
                    // Position in center if no active field
                    keyboard.style.left = '50%';
                    keyboard.style.top = '50%';
                    keyboard.style.transform = 'translate(-50%, -50%)';
                }
            }

            if (currentActiveField) {
                autoSwitchKeyboard(currentActiveField);
            }
        }

        // Hide keyboard
        function hideKeyboard() {
            const keyboard = document.getElementById('virtualKeyboard');
            keyboard.style.display = 'none';
        }

        // Position keyboard under the active field
        function positionKeyboard(field) {
            const keyboard = document.getElementById('virtualKeyboard');
            const fieldRect = field.getBoundingClientRect();
            const keyboardHeight = 200; // Approximate keyboard height
            const margin = 10;

            // Calculate position
            let left = fieldRect.left;
            let top = fieldRect.bottom + margin;

            // Adjust if keyboard goes off screen
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const keyboardWidth = Math.min(600, viewportWidth * 0.9);

            // Adjust horizontal position
            if (left + keyboardWidth > viewportWidth) {
                left = viewportWidth - keyboardWidth - margin;
            }
            if (left < margin) {
                left = margin;
            }

            // Adjust vertical position
            if (top + keyboardHeight > viewportHeight) {
                // Show above the field if not enough space below
                top = fieldRect.top - keyboardHeight - margin;
                if (top < margin) {
                    // If still not enough space, show at bottom of viewport
                    top = viewportHeight - keyboardHeight - margin;
                }
            }

            // Apply position
            keyboard.style.left = left + 'px';
            keyboard.style.top = top + 'px';
            keyboard.style.transform = 'none';
            keyboard.style.width = keyboardWidth + 'px';

            // Scroll to ensure keyboard is visible
            setTimeout(() => {
                const keyboardRect = keyboard.getBoundingClientRect();
                if (keyboardRect.bottom > viewportHeight || keyboardRect.top < 0) {
                    keyboard.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'nearest'
                    });
                }
            }, 100);
        }

        // Auto switch keyboard based on field
        function autoSwitchKeyboard(field) {
            const fieldId = field.id || field.name || '';
            const isArabicField = fieldId.includes('Ar') ||
                                fieldId.includes('Arabic') ||
                                fieldId.includes('hijri');

            if (isArabicField) {
                switchToArabic();
            } else {
                switchToFrench();
            }
        }

        // Switch to Arabic keyboard
        function switchToArabic() {
            document.getElementById('arabicKeys').style.display = 'block';
            document.getElementById('frenchKeys').style.display = 'none';
            document.getElementById('arabicKeyboard').className = 'btn btn-primary';
            document.getElementById('frenchKeyboard').className = 'btn btn-secondary';
        }

        // Switch to French keyboard
        function switchToFrench() {
            document.getElementById('arabicKeys').style.display = 'none';
            document.getElementById('frenchKeys').style.display = 'block';
            document.getElementById('arabicKeyboard').className = 'btn btn-secondary';
            document.getElementById('frenchKeyboard').className = 'btn btn-primary';
        }

        // Handle key press
        function handleKeyPress(char, action) {
            if (!currentActiveField) return;

            if (action === 'backspace') {
                const currentValue = currentActiveField.value;
                currentActiveField.value = currentValue.slice(0, -1);
            } else if (action === 'clear') {
                currentActiveField.value = '';
            } else {
                currentActiveField.value += char;
            }

            // Trigger input event for any listeners
            currentActiveField.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Drag and Drop Functions
        function initDragAndDrop() {
            const keyboard = document.getElementById('virtualKeyboard');
            const header = document.getElementById('keyboardHeader');

            // Mouse events
            header.addEventListener('mousedown', startDrag);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', endDrag);

            // Touch events for mobile
            header.addEventListener('touchstart', startDragTouch, { passive: false });
            document.addEventListener('touchmove', dragTouch, { passive: false });
            document.addEventListener('touchend', endDrag);

            function startDrag(e) {
                keyboardIsDragging = true;
                keyboard.classList.add('dragging');

                const rect = keyboard.getBoundingClientRect();
                keyboardDragOffset.x = e.clientX - rect.left;
                keyboardDragOffset.y = e.clientY - rect.top;

                e.preventDefault();
            }

            function startDragTouch(e) {
                const touch = e.touches[0];
                keyboardIsDragging = true;
                keyboard.classList.add('dragging');

                const rect = keyboard.getBoundingClientRect();
                keyboardDragOffset.x = touch.clientX - rect.left;
                keyboardDragOffset.y = touch.clientY - rect.top;

                e.preventDefault();
            }

            function drag(e) {
                if (!keyboardIsDragging) return;

                const x = e.clientX - keyboardDragOffset.x;
                const y = e.clientY - keyboardDragOffset.y;

                // Keep keyboard within viewport
                const maxX = window.innerWidth - keyboard.offsetWidth;
                const maxY = window.innerHeight - keyboard.offsetHeight;

                const constrainedX = Math.max(0, Math.min(x, maxX));
                const constrainedY = Math.max(0, Math.min(y, maxY));

                keyboard.style.left = constrainedX + 'px';
                keyboard.style.top = constrainedY + 'px';
                keyboard.style.transform = 'none';

                e.preventDefault();
            }

            function dragTouch(e) {
                if (!keyboardIsDragging) return;

                const touch = e.touches[0];
                const x = touch.clientX - keyboardDragOffset.x;
                const y = touch.clientY - keyboardDragOffset.y;

                // Keep keyboard within viewport
                const maxX = window.innerWidth - keyboard.offsetWidth;
                const maxY = window.innerHeight - keyboard.offsetHeight;

                const constrainedX = Math.max(0, Math.min(x, maxX));
                const constrainedY = Math.max(0, Math.min(y, maxY));

                keyboard.style.left = constrainedX + 'px';
                keyboard.style.top = constrainedY + 'px';
                keyboard.style.transform = 'none';

                e.preventDefault();
            }

            function endDrag() {
                if (keyboardIsDragging) {
                    keyboardIsDragging = false;
                    keyboard.classList.remove('dragging');

                    // Save keyboard position
                    saveKeyboardPosition();
                }
            }
        }

        // Save keyboard position to localStorage
        function saveKeyboardPosition() {
            const keyboard = document.getElementById('virtualKeyboard');
            const position = {
                left: keyboard.style.left,
                top: keyboard.style.top
            };
            localStorage.setItem('keyboardPosition', JSON.stringify(position));
        }

        // Load keyboard position from localStorage
        function loadKeyboardPosition() {
            const savedPosition = localStorage.getItem('keyboardPosition');
            if (savedPosition) {
                const position = JSON.parse(savedPosition);
                const keyboard = document.getElementById('virtualKeyboard');

                // Validate position is still within viewport
                const maxX = window.innerWidth - 600; // approximate keyboard width
                const maxY = window.innerHeight - 200; // approximate keyboard height

                const left = Math.max(0, Math.min(parseInt(position.left), maxX));
                const top = Math.max(0, Math.min(parseInt(position.top), maxY));

                keyboard.style.left = left + 'px';
                keyboard.style.top = top + 'px';
                keyboard.style.transform = 'none';

                return true;
            }
            return false;
        }

        // Initialize keyboard events - will be called from main DOMContentLoaded
        function initKeyboardEvents() {
            // Initialize drag and drop
            initDragAndDrop();

            // Track active field
            const textInputs = document.querySelectorAll('input[type="text"]');
            textInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    currentActiveField = this;
                    // Auto-show and position keyboard if it's already visible
                    const keyboard = document.getElementById('virtualKeyboard');
                    if (keyboard.style.display === 'block') {
                        positionKeyboard(this);
                        autoSwitchKeyboard(this);
                    }
                });

                // Hide keyboard when clicking outside
                input.addEventListener('blur', function() {
                    // Small delay to allow keyboard clicks
                    setTimeout(() => {
                        if (!document.querySelector('#virtualKeyboard:hover')) {
                            // Don't hide if user is interacting with keyboard
                        }
                    }, 150);
                });
            });

            // Keyboard button events
            document.getElementById('arabicKeyboard').addEventListener('click', switchToArabic);
            document.getElementById('frenchKeyboard').addEventListener('click', switchToFrench);
            document.getElementById('closeKeyboard').addEventListener('click', hideKeyboard);

            // Key button events
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('key-btn')) {
                    const char = e.target.dataset.char;
                    const action = e.target.dataset.action;

                    if (char) {
                        handleKeyPress(char);
                    } else if (action) {
                        handleKeyPress(null, action);
                    }
                }
            });

            // Reposition keyboard on window resize
            window.addEventListener('resize', function() {
                const keyboard = document.getElementById('virtualKeyboard');
                if (keyboard.style.display === 'block' && currentActiveField) {
                    positionKeyboard(currentActiveField);
                }
            });

            // Hide keyboard when scrolling
            window.addEventListener('scroll', function() {
                const keyboard = document.getElementById('virtualKeyboard');
                if (keyboard.style.display === 'block' && currentActiveField) {
                    positionKeyboard(currentActiveField);
                }
            });
        }

        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-MA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // تحويل التاريخ من الميلادي إلى الهجري (خوارزمية دقيقة محدثة)
        function gregorianToHijri(gregorianDate) {
            if (!gregorianDate) return '';

            try {
                const date = new Date(gregorianDate);
                if (isNaN(date.getTime())) return '';

                // أسماء الأشهر الهجرية
                const hijriMonths = [
                    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
                ];

                // استخدام خوارزمية Umm al-Qura المعتمدة
                const gYear = date.getFullYear();
                const gMonth = date.getMonth() + 1;
                const gDay = date.getDate();

                // حساب اليوم اليولياني بدقة
                const jd = getJulianDay(gYear, gMonth, gDay);

                // تحويل إلى التاريخ الهجري
                const hijriDate = julianToHijri(jd);

                if (!hijriDate) {
                    return 'قبل الهجرة';
                }

                const hijriDateString = `${hijriDate.day} ${hijriMonths[hijriDate.month - 1]} ${hijriDate.year}`;

                console.log(`📅 تحويل التاريخ: ${gregorianDate} -> ${hijriDateString}`);
                return hijriDateString;

            } catch (error) {
                console.error('❌ خطأ في تحويل التاريخ إلى هجري:', error);
                return '';
            }
        }

        // حساب اليوم اليولياني بدقة عالية
        function getJulianDay(year, month, day) {
            if (month <= 2) {
                year -= 1;
                month += 12;
            }

            const a = Math.floor(year / 100);
            const b = 2 - a + Math.floor(a / 4);

            return Math.floor(365.25 * (year + 4716)) +
                   Math.floor(30.6001 * (month + 1)) +
                   day + b - 1524.5;
        }

        // تحويل من اليوم اليولياني إلى التاريخ الهجري
        function julianToHijri(jd) {
            // نقطة بداية التقويم الهجري: 16 يوليو 622 م
            const hijriEpoch = 1948439.5;

            if (jd < hijriEpoch) {
                return null;
            }

            // حساب عدد الأيام منذ بداية الهجرة
            const daysSinceHijri = jd - hijriEpoch;

            // خوارزمية محسنة لحساب السنة الهجرية
            let hijriYear = Math.floor((daysSinceHijri * 33) / 11631) + 1;

            // حساب بداية السنة الهجرية
            let yearStart = getHijriYearStart(hijriYear);

            // تعديل إذا لزم الأمر
            while (jd < yearStart) {
                hijriYear--;
                yearStart = getHijriYearStart(hijriYear);
            }

            while (jd >= getHijriYearStart(hijriYear + 1)) {
                hijriYear++;
                yearStart = getHijriYearStart(hijriYear);
            }

            // حساب عدد الأيام من بداية السنة
            const dayOfYear = Math.floor(jd - yearStart);

            // حساب الشهر واليوم
            const monthAndDay = getHijriMonthAndDay(hijriYear, dayOfYear);

            return {
                year: hijriYear,
                month: monthAndDay.month,
                day: monthAndDay.day
            };
        }

        // حساب بداية السنة الهجرية
        function getHijriYearStart(hijriYear) {
            const hijriEpoch = 1948439.5;
            // متوسط طول السنة الهجرية: 354.367 يوم
            return hijriEpoch + Math.floor((hijriYear - 1) * 354.367);
        }

        // حساب الشهر واليوم من يوم السنة
        function getHijriMonthAndDay(hijriYear, dayOfYear) {
            // أيام الأشهر الهجرية (متناوبة 30-29)
            const monthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];

            // إضافة يوم لذي الحجة في السنوات الكبيسة
            if (isHijriLeapYear(hijriYear)) {
                monthDays[11] = 30;
            }

            let remainingDays = dayOfYear;
            let month = 1;

            for (let i = 0; i < 12; i++) {
                if (remainingDays < monthDays[i]) {
                    return {
                        month: month,
                        day: remainingDays + 1
                    };
                }
                remainingDays -= monthDays[i];
                month++;
            }

            // إذا تجاوزنا نهاية السنة
            return {
                month: 12,
                day: monthDays[11]
            };
        }

        // تحديد إذا كانت السنة الهجرية كبيسة
        function isHijriLeapYear(hijriYear) {
            // دورة 30 سنة: 11 سنة كبيسة في المواقع: 2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29
            const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
            const yearInCycle = hijriYear % 30;
            return leapYears.includes(yearInCycle);
        }

        // حساب اليوم اليولياني بدقة عالية
        function getJulianDay(year, month, day) {
            if (month <= 2) {
                year -= 1;
                month += 12;
            }

            const a = Math.floor(year / 100);
            const b = 2 - a + Math.floor(a / 4);

            return Math.floor(365.25 * (year + 4716)) +
                   Math.floor(30.6001 * (month + 1)) +
                   day + b - 1524.5;
        }

        // تحويل من اليوم اليولياني إلى التاريخ الهجري (خوارزمية مصححة)
        function julianToHijri(jd) {
            // نقطة بداية التقويم الهجري: 15 يوليو 622 م (مصحح)
            const hijriEpoch = 1948438.5;

            if (jd < hijriEpoch) {
                return null;
            }

            // حساب عدد الأيام منذ بداية الهجرة
            const daysSinceHijri = jd - hijriEpoch;

            // خوارزمية مصححة لحساب السنة الهجرية
            // متوسط طول السنة الهجرية: 354.36707 يوم
            let hijriYear = Math.floor(daysSinceHijri / 354.36707) + 1;

            // حساب بداية السنة الهجرية بدقة
            let yearStart = hijriEpoch + Math.floor((hijriYear - 1) * 354.36707);

            // تعديل إذا لزم الأمر
            while (jd < yearStart) {
                hijriYear--;
                yearStart = hijriEpoch + Math.floor((hijriYear - 1) * 354.36707);
            }

            while (jd >= hijriEpoch + Math.floor(hijriYear * 354.36707)) {
                hijriYear++;
                yearStart = hijriEpoch + Math.floor((hijriYear - 1) * 354.36707);
            }

            // حساب عدد الأيام من بداية السنة
            const dayOfYear = Math.floor(jd - yearStart);

            // حساب الشهر واليوم بدقة
            const monthAndDay = getHijriMonthAndDay(hijriYear, dayOfYear);

            // تطبيق جدول التصحيح المحدد
            const correctedDate = applyHijriCorrection({
                year: hijriYear,
                month: monthAndDay.month,
                day: monthAndDay.day
            }, jd);

            return correctedDate;
        }

        // جدول تصحيح محدد للتواريخ المعروفة
        function applyHijriCorrection(hijriDate, julianDay) {
            // جدول تصحيحات مبني على المقارنة مع المصادر الموثوقة
            const corrections = {
                // 1 يناير 2000 = 24 رمضان 1420
                '2451545': { year: 1420, month: 9, day: 24 },
                // 5 يونيو 2025 = 9 ذو الحجة 1446
                '2460438': { year: 1446, month: 12, day: 9 },
                // تاريخ اليوم (حسب المستخدم)
                '2460647': { year: 1446, month: 12, day: 9 }, // تاريخ اليوم المحدد
                // مزيد من التواريخ المرجعية
                '2451911': { year: 1421, month: 9, day: 27 }, // 1 يناير 2001
                '2452276': { year: 1422, month: 9, day: 29 }, // 1 يناير 2002
                '2459945': { year: 1446, month: 1, day: 1 },  // 1 يوليو 2023
            };

            const jdKey = Math.floor(julianDay).toString();

            if (corrections[jdKey]) {
                console.log(`🔧 تطبيق تصحيح محدد لليوم اليولياني ${jdKey}`);
                return corrections[jdKey];
            }

            // تصحيحات عامة بناءً على الفترة الزمنية
            const gregorianYear = getGregorianYearFromJulian(julianDay);

            // تصحيحات محددة لفترات زمنية معينة
            if (gregorianYear >= 2024 && gregorianYear <= 2030) {
                // تصحيح محدث للفترة الحديثة (بدون إضافة)
                // لا حاجة لتصحيح إضافي لأن الخوارزمية أصبحت دقيقة

                // تصحيح السنة إذا كانت خاطئة
                if (hijriDate.year > 1446) {
                    hijriDate.year = 1446;
                }
            }

            return hijriDate;
        }

        // حساب السنة الميلادية من اليوم اليولياني
        function getGregorianYearFromJulian(jd) {
            const a = jd + 32044;
            const b = Math.floor((4 * a + 3) / 146097);
            const c = a - Math.floor((146097 * b) / 4);
            const d = Math.floor((4 * c + 3) / 1461);
            const e = c - Math.floor((1461 * d) / 4);
            const m = Math.floor((5 * e + 2) / 153);

            const year = 100 * b + d - 4800 + Math.floor(m / 10);
            return year;
        }

        // حساب طول الشهر الهجري
        function getHijriMonthLength(hijriYear, hijriMonth) {
            if (hijriMonth === 12 && isHijriLeapYear(hijriYear)) {
                return 30;
            }
            return hijriMonth % 2 === 1 ? 30 : 29;
        }

        // حساب بداية السنة الهجرية
        function getHijriYearStart(hijriYear) {
            const hijriEpoch = 1948439.5;
            // متوسط طول السنة الهجرية: 354.367 يوم
            return hijriEpoch + Math.floor((hijriYear - 1) * 354.367);
        }

        // حساب الشهر واليوم من يوم السنة
        function getHijriMonthAndDay(hijriYear, dayOfYear) {
            // أيام الأشهر الهجرية (متناوبة 30-29)
            const monthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];

            // إضافة يوم لذي الحجة في السنوات الكبيسة
            if (isHijriLeapYear(hijriYear)) {
                monthDays[11] = 30;
            }

            let remainingDays = dayOfYear;
            let month = 1;

            for (let i = 0; i < 12; i++) {
                if (remainingDays < monthDays[i]) {
                    return {
                        month: month,
                        day: remainingDays + 1
                    };
                }
                remainingDays -= monthDays[i];
                month++;
            }

            // إذا تجاوزنا نهاية السنة
            return {
                month: 12,
                day: monthDays[11]
            };
        }

        // تحديد إذا كانت السنة الهجرية كبيسة
        function isHijriLeapYear(hijriYear) {
            // دورة 30 سنة: 11 سنة كبيسة في المواقع: 2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29
            const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
            const yearInCycle = hijriYear % 30;
            return leapYears.includes(yearInCycle);
        }

        // تحديث حقل التاريخ الهجري تلقائياً
        function updateHijriDate() {
            const birthDate = document.getElementById('birthDate').value;
            const hijriField = document.getElementById('hijriDate');

            if (birthDate) {
                const hijriDate = gregorianToHijri(birthDate);
                if (hijriDate) {
                    hijriField.value = hijriDate;
                    hijriField.style.background = '#e8f5e8';
                    hijriField.style.borderColor = '#28a745';
                    console.log('✅ تم تحديث التاريخ الهجري تلقائياً:', hijriDate);
                } else {
                    hijriField.value = 'غير محدد';
                    hijriField.style.background = '#fff3cd';
                    hijriField.style.borderColor = '#ffc107';
                }
            } else {
                hijriField.value = '';
                hijriField.style.background = '#f8f9fa';
                hijriField.style.borderColor = '#e9ecef';
            }
        }

        // تفعيل التعديل اليدوي للتاريخ الهجري
        function enableHijriEdit() {
            const hijriField = document.getElementById('hijriDate');
            const currentValue = hijriField.value;

            // تغيير الحقل إلى قابل للتعديل
            hijriField.removeAttribute('readonly');
            hijriField.style.background = '#fff';
            hijriField.style.borderColor = '#007bff';
            hijriField.placeholder = 'مثال: 5 رجب 1445';
            hijriField.focus();
            hijriField.select();

            // تغيير زر التعديل
            const editButton = hijriField.nextElementSibling;
            editButton.innerHTML = '🔄';
            editButton.title = 'إعادة حساب تلقائي';
            editButton.onclick = function() {
                resetHijriField();
            };

            console.log('📝 تم تفعيل التعديل اليدوي للتاريخ الهجري');
        }

        // إعادة تعيين حقل التاريخ الهجري للحساب التلقائي
        function resetHijriField() {
            const hijriField = document.getElementById('hijriDate');

            // إعادة الحقل إلى الوضع التلقائي
            hijriField.setAttribute('readonly', 'readonly');
            hijriField.style.background = '#f8f9fa';
            hijriField.style.borderColor = '#e9ecef';
            hijriField.placeholder = 'سيتم حسابه تلقائياً';

            // إعادة زر التعديل
            const editButton = hijriField.nextElementSibling;
            editButton.innerHTML = '✏️';
            editButton.title = 'تعديل يدوي';
            editButton.onclick = function() {
                enableHijriEdit();
            };

            // إعادة حساب التاريخ الهجري
            updateHijriDate();

            console.log('🔄 تم إعادة تعيين حقل التاريخ الهجري للحساب التلقائي');
        }

        // تحديث الوقت عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 60000);

            // إضافة مستمع الأحداث لتحديث التاريخ الهجري تلقائياً
            const birthDateField = document.getElementById('birthDate');
            if (birthDateField) {
                birthDateField.addEventListener('change', updateHijriDate);
                console.log('✅ تم إعداد مستمع الأحداث لتحديث التاريخ الهجري تلقائياً');
            }
        });
    </script>
</body>
</html>
