<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث في سجلات المواطنين</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .content {
            padding: 30px;
        }

        .search-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .search-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .results-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
        }

        .citizen-card {
            background: white;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 10px;
            border-left: 5px solid #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .citizen-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .citizen-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .citizen-actions {
            text-align: left;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }

        .certificate-badge {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 10px;
        }

        .no-certificate {
            background: #e74c3c;
        }

        @media (max-width: 768px) {
            .citizen-info {
                grid-template-columns: 1fr;
            }

            .search-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔍 البحث في سجلات المواطنين</h1>
            <p>مكتب الحالة المدنية - أيير، إقليم أسفي</p>

            <div class="nav-links">
                <a href="main-dashboard.html" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="citizens-database.html" class="nav-link">👥 إدارة البيانات</a>
                <a href="dual-birth-certificate.html" class="nav-link">📜 عقود الازدياد</a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Statistics -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalCitizens">0</div>
                    <div class="stat-label">إجمالي المواطنين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="withCertificates">0</div>
                    <div class="stat-label">لديهم شهادات كاملة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="withoutCertificates">0</div>
                    <div class="stat-label">بدون شهادات كاملة</div>
                </div>
            </div>

            <!-- Search Section -->
            <div class="search-section">
                <h2 style="margin-bottom: 20px; color: #2c3e50;">🔍 البحث المتقدم</h2>

                <div class="search-group">
                    <div class="form-group">
                        <label for="searchName">الاسم (عربي أو فرنسي):</label>
                        <input type="text" id="searchName" placeholder="ابحث بالاسم الشخصي أو العائلي">
                    </div>

                    <div class="form-group">
                        <label for="searchActNumber">رقم القيد:</label>
                        <input type="text" id="searchActNumber" placeholder="مثال: 20/2025">
                    </div>

                    <div class="form-group">
                        <label for="searchBirthDate">تاريخ الازدياد:</label>
                        <input type="date" id="searchBirthDate">
                    </div>

                    <div class="form-group">
                        <label for="searchParent">اسم الوالد/الوالدة:</label>
                        <input type="text" id="searchParent" placeholder="ابحث باسم الوالد أو الوالدة">
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn btn-primary" onclick="searchCitizens()">🔍 بحث</button>
                    <button class="btn btn-secondary" onclick="clearSearch()">🗑️ مسح البحث</button>
                    <button class="btn btn-success" onclick="showAll()">👥 عرض الكل</button>
                </div>
            </div>

            <!-- Results Section -->
            <div class="results-section">
                <h2 style="margin-bottom: 20px; color: #2c3e50;">📋 نتائج البحث</h2>
                <div id="searchResults">
                    <p style="text-align: center; color: #7f8c8d; font-style: italic;">استخدم البحث أعلاه للعثور على المواطنين</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load citizens data
        let citizens = JSON.parse(localStorage.getItem('citizens')) || [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatistics();

            // Add Enter key support for search fields
            const searchFields = ['searchName', 'searchActNumber', 'searchBirthDate', 'searchParent'];
            searchFields.forEach(fieldId => {
                document.getElementById(fieldId).addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchCitizens();
                    }
                });
            });
        });

        // Update statistics
        function updateStatistics() {
            const total = citizens.length;
            const withCertificates = citizens.filter(c => c.certificateImage).length;
            const withoutCertificates = total - withCertificates;

            document.getElementById('totalCitizens').textContent = total;
            document.getElementById('withCertificates').textContent = withCertificates;
            document.getElementById('withoutCertificates').textContent = withoutCertificates;
        }

        // Search citizens
        function searchCitizens() {
            const searchName = document.getElementById('searchName').value.trim().toLowerCase();
            const searchActNumber = document.getElementById('searchActNumber').value.trim().toLowerCase();
            const searchBirthDate = document.getElementById('searchBirthDate').value;
            const searchParent = document.getElementById('searchParent').value.trim().toLowerCase();

            // Check if at least one search criteria is provided
            if (!searchName && !searchActNumber && !searchBirthDate && !searchParent) {
                alert('⚠️ يرجى إدخال معيار بحث واحد على الأقل');
                return;
            }

            const filtered = citizens.filter(citizen => {
                const nameMatch = !searchName ||
                    (citizen.firstNameAr && citizen.firstNameAr.toLowerCase().includes(searchName)) ||
                    (citizen.firstNameFr && citizen.firstNameFr.toLowerCase().includes(searchName)) ||
                    (citizen.familyNameAr && citizen.familyNameAr.toLowerCase().includes(searchName)) ||
                    (citizen.familyNameFr && citizen.familyNameFr.toLowerCase().includes(searchName));

                const actNumberMatch = !searchActNumber ||
                    (citizen.actNumber && citizen.actNumber.toLowerCase().includes(searchActNumber));

                const birthDateMatch = !searchBirthDate ||
                    (citizen.birthDate && citizen.birthDate === searchBirthDate);

                const parentMatch = !searchParent ||
                    (citizen.fatherNameAr && citizen.fatherNameAr.toLowerCase().includes(searchParent)) ||
                    (citizen.fatherNameFr && citizen.fatherNameFr.toLowerCase().includes(searchParent)) ||
                    (citizen.motherNameAr && citizen.motherNameAr.toLowerCase().includes(searchParent)) ||
                    (citizen.motherNameFr && citizen.motherNameFr.toLowerCase().includes(searchParent));

                return nameMatch && actNumberMatch && birthDateMatch && parentMatch;
            });

            displayResults(filtered);
        }

        // Display search results
        function displayResults(citizensToShow) {
            const container = document.getElementById('searchResults');

            if (citizensToShow.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #7f8c8d; font-style: italic;">لم يتم العثور على نتائج مطابقة</p>';
                return;
            }

            let resultsHTML = '';
            citizensToShow.forEach(citizen => {
                const hasCertificate = citizen.certificateImage;
                const certificateBadge = hasCertificate ?
                    '<span class="certificate-badge">📄 يوجد شهادة كاملة</span>' :
                    '<span class="certificate-badge no-certificate">❌ لا توجد شهادة</span>';

                resultsHTML += '<div class="citizen-card">' +
                    '<div class="citizen-info">' +
                    '<div><strong>الاسم:</strong> ' + (citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '') + '</div>' +
                    '<div><strong>الاسم (فرنسي):</strong> ' + (citizen.firstNameFr || '') + ' ' + (citizen.familyNameFr || '') + '</div>' +
                    '<div><strong>رقم القيد:</strong> ' + citizen.actNumber + '</div>' +
                    '<div><strong>تاريخ الازدياد:</strong> ' + citizen.birthDate + '</div>' +
                    '<div><strong>الجنس:</strong> ' + citizen.gender + '</div>' +
                    '<div><strong>مكان الازدياد:</strong> ' + (citizen.birthPlaceAr || '') + '</div>' +
                    '<div><strong>الوالد:</strong> ' + (citizen.fatherNameAr || '') + '</div>' +
                    '<div><strong>الوالدة:</strong> ' + (citizen.motherNameAr || '') + '</div>' +
                    '</div>' +
                    '<div style="margin-bottom: 15px;">' + certificateBadge + '</div>' +
                    '<div class="citizen-actions">' +
                    '<button class="btn btn-success" onclick="printCertificate(' + citizen.id + ')">🖨️ طباعة عقد الازدياد</button>' +
                    (hasCertificate ? '<button class="btn btn-primary" onclick="printFullCertificate(' + citizen.id + ')">🖨️ طباعة الشهادة الكاملة</button>' : '') +
                    '<button class="btn btn-secondary" onclick="editCitizen(' + citizen.id + ')">✏️ تعديل البيانات</button>' +
                    '<button class="btn" style="background: #e74c3c; color: white;" onclick="deleteCitizen(' + citizen.id + ')">🗑️ حذف</button>' +
                    '</div>' +
                    '</div>';
            });

            container.innerHTML = resultsHTML;
        }

        // Show all citizens
        function showAll() {
            displayResults(citizens);
        }

        // Clear search
        function clearSearch() {
            document.getElementById('searchName').value = '';
            document.getElementById('searchActNumber').value = '';
            document.getElementById('searchBirthDate').value = '';
            document.getElementById('searchParent').value = '';

            // Clear results and show initial message
            const container = document.getElementById('searchResults');
            container.innerHTML = '<p style="text-align: center; color: #7f8c8d; font-style: italic;">استخدم البحث أعلاه للعثور على المواطنين</p>';
        }

        // Print certificate
        function printCertificate(id) {
            const citizen = citizens.find(c => c.id === id);
            if (!citizen) return;

            const params = new URLSearchParams({
                firstNameAr: citizen.firstNameAr || '',
                firstNameFr: citizen.firstNameFr || '',
                familyNameAr: citizen.familyNameAr || '',
                familyNameFr: citizen.familyNameFr || '',
                birthPlaceAr: citizen.birthPlaceAr || '',
                birthPlaceFr: citizen.birthPlaceFr || '',
                birthDate: citizen.birthDate,
                hijriDate: citizen.hijriDate,
                gender: citizen.gender,
                fatherNameAr: citizen.fatherNameAr || '',
                fatherNameFr: citizen.fatherNameFr || '',
                motherNameAr: citizen.motherNameAr || '',
                motherNameFr: citizen.motherNameFr || '',
                actNumber: citizen.actNumber,
                registrationDate: citizen.registrationDate
            });

            window.open('dual-birth-certificate.html?' + params.toString(), '_blank');
        }

        // Print full certificate image
        function printFullCertificate(id) {
            const citizen = citizens.find(c => c.id === id);
            if (!citizen || !citizen.certificateImage) return;

            // Create a new window for printing
            const printWindow = window.open('', '_blank');

            const htmlContent = '<!DOCTYPE html>' +
                '<html lang="ar" dir="rtl">' +
                '<head>' +
                '<meta charset="UTF-8">' +
                '<title>الشهادة الكاملة - ' + (citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '') + '</title>' +
                '<style>' +
                'body { margin: 0; padding: 20px; font-family: Arial, sans-serif; text-align: center; }' +
                '.header { margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 15px; }' +
                '.header h1 { color: #2c3e50; margin: 0; }' +
                '.info { margin-bottom: 20px; text-align: right; }' +
                '.info p { margin: 5px 0; font-size: 14px; }' +
                '.certificate-image { max-width: 100%; max-height: 80vh; border: 1px solid #ddd; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }' +
                '@media print { body { margin: 0; padding: 10px; } .header { page-break-inside: avoid; } .certificate-image { max-height: none; } }' +
                '</style>' +
                '</head>' +
                '<body>' +
                '<div class="header">' +
                '<h1>الشهادة الكاملة</h1>' +
                '<p>مكتب الحالة المدنية - أيير، إقليم أسفي</p>' +
                '</div>' +
                '<div class="info">' +
                '<p><strong>الاسم:</strong> ' + (citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '') + '</p>' +
                '<p><strong>رقم القيد:</strong> ' + citizen.actNumber + '</p>' +
                '<p><strong>تاريخ التحميل:</strong> ' + new Date(citizen.certificateImage.uploadDate).toLocaleDateString('ar-EG') + '</p>' +
                '<p><strong>تاريخ الطباعة:</strong> ' + new Date().toLocaleDateString('ar-EG') + ' - ' + new Date().toLocaleTimeString('ar-EG') + '</p>' +
                '</div>' +
                '<img src="' + citizen.certificateImage.data + '" alt="الشهادة الكاملة" class="certificate-image">' +
                '<script>' +
                'window.onload = function() {' +
                'window.print();' +
                'window.onafterprint = function() { window.close(); }' +
                '}' +
                '</script>' +
                '</body>' +
                '</html>';

            printWindow.document.write(htmlContent);
            printWindow.document.close();
        }

        // Edit citizen
        function editCitizen(id) {
            const citizen = citizens.find(c => c.id === id);
            if (!citizen) return;

            // Create URL parameters for the citizen data
            const params = new URLSearchParams({
                edit: 'true',
                id: citizen.id,
                firstNameAr: citizen.firstNameAr || '',
                firstNameFr: citizen.firstNameFr || '',
                familyNameAr: citizen.familyNameAr || '',
                familyNameFr: citizen.familyNameFr || '',
                birthPlaceAr: citizen.birthPlaceAr || '',
                birthPlaceFr: citizen.birthPlaceFr || '',
                birthDate: citizen.birthDate || '',
                hijriDate: citizen.hijriDate || '',
                gender: citizen.gender || '',
                fatherNameAr: citizen.fatherNameAr || '',
                fatherNameFr: citizen.fatherNameFr || '',
                motherNameAr: citizen.motherNameAr || '',
                motherNameFr: citizen.motherNameFr || '',
                actNumber: citizen.actNumber || '',
                registrationDate: citizen.registrationDate || ''
            });

            // Redirect to citizens database page with edit parameters
            window.location.href = 'citizens-database.html?' + params.toString();
        }

        // Delete citizen
        function deleteCitizen(id) {
            const citizen = citizens.find(c => c.id === id);
            if (!citizen) return;

            const citizenName = ((citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '')).trim();
            const confirmMessage = 'هل أنت متأكد من حذف بيانات المواطن؟\n\nالاسم: ' + citizenName + '\nرقم القيد: ' + citizen.actNumber + '\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!';

            if (confirm(confirmMessage)) {
                // Remove citizen from array
                citizens = citizens.filter(c => c.id !== id);

                // Save updated data to localStorage
                localStorage.setItem('citizens', JSON.stringify(citizens));

                // Update statistics
                updateStatistics();

                // Refresh current search results
                searchCitizens();

                alert('✅ تم حذف بيانات المواطن بنجاح\nالاسم: ' + citizenName);
            }
        }
    </script>
</body>
</html>
