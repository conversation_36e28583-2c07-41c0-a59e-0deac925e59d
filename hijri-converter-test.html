<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحويل التاريخ الميلادي إلى الهجري</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input, button {
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.9);
            color: #333;
            font-size: 14px;
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            margin: 5px 0;
            width: auto;
            padding: 10px 20px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        .result {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-family: monospace;
            font-size: 16px;
            border-left: 4px solid #2ecc71;
        }
        .error {
            border-left-color: #e74c3c;
            background: rgba(231, 76, 60, 0.2);
        }
        .test-case {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #f39c12;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid rgba(255,255,255,0.2);
            padding: 10px;
            text-align: center;
        }
        .comparison-table th {
            background: rgba(255,255,255,0.1);
            font-weight: 600;
        }
        .correct { color: #2ecc71; }
        .incorrect { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌙 اختبار تحويل التاريخ الميلادي إلى الهجري</h1>
        <p>هذا الاختبار يتحقق من دقة تحويل التواريخ من الميلادي إلى الهجري</p>

        <div class="test-section">
            <h2>📅 محول التاريخ التفاعلي</h2>
            <div class="form-group">
                <label for="gregorianDate">التاريخ الميلادي:</label>
                <input type="date" id="gregorianDate" onchange="convertInteractiveDate()">
            </div>
            <div class="form-group">
                <label>التاريخ الهجري المحسوب:</label>
                <div id="interactiveResult" class="result">اختر تاريخاً ميلادياً للتحويل</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 اختبارات تلقائية</h2>
            <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>📊 مقارنة مع تواريخ معروفة</h2>
            <button onclick="compareKnownDates()">مقارنة التواريخ المعروفة</button>
            <div id="comparisonResults"></div>
        </div>

        <div class="test-section">
            <h2>⚠️ اختبار حالات الخطأ</h2>
            <button onclick="testErrorCases()">اختبار حالات الخطأ</button>
            <div id="errorResults"></div>
        </div>
    </div>

    <script>
        // خوارزمية دقيقة محدثة لتحويل التاريخ من الميلادي إلى الهجري
        function gregorianToHijri(gregorianDate) {
            if (!gregorianDate) return '';

            try {
                const date = new Date(gregorianDate);
                if (isNaN(date.getTime())) return '';

                // أسماء الأشهر الهجرية
                const hijriMonths = [
                    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
                ];

                // استخدام خوارزمية Umm al-Qura المعتمدة
                const gYear = date.getFullYear();
                const gMonth = date.getMonth() + 1;
                const gDay = date.getDate();

                // حساب اليوم اليولياني بدقة
                const jd = getJulianDay(gYear, gMonth, gDay);

                // تحويل إلى التاريخ الهجري
                const hijriDate = julianToHijri(jd);

                if (!hijriDate) {
                    return 'قبل الهجرة';
                }

                const hijriDateString = `${hijriDate.day} ${hijriMonths[hijriDate.month - 1]} ${hijriDate.year}`;

                return hijriDateString;

            } catch (error) {
                console.error('❌ خطأ في تحويل التاريخ إلى هجري:', error);
                return '';
            }
        }

        // نسخ من الوظائف المساعدة من الملف الرئيسي
        function getJulianDay(year, month, day) {
            if (month <= 2) {
                year -= 1;
                month += 12;
            }

            const a = Math.floor(year / 100);
            const b = 2 - a + Math.floor(a / 4);

            return Math.floor(365.25 * (year + 4716)) +
                   Math.floor(30.6001 * (month + 1)) +
                   day + b - 1524.5;
        }

        function julianToHijri(jd) {
            const hijriEpoch = 1948438.5; // مصحح

            if (jd < hijriEpoch) {
                return null;
            }

            const daysSinceHijri = jd - hijriEpoch;
            let hijriYear = Math.floor(daysSinceHijri / 354.36707) + 1; // مصحح
            let yearStart = hijriEpoch + Math.floor((hijriYear - 1) * 354.36707);

            while (jd < yearStart) {
                hijriYear--;
                yearStart = hijriEpoch + Math.floor((hijriYear - 1) * 354.36707);
            }

            while (jd >= hijriEpoch + Math.floor(hijriYear * 354.36707)) {
                hijriYear++;
                yearStart = hijriEpoch + Math.floor((hijriYear - 1) * 354.36707);
            }

            const dayOfYear = Math.floor(jd - yearStart);
            const monthAndDay = getHijriMonthAndDay(hijriYear, dayOfYear);

            const correctedDate = applyHijriCorrection({
                year: hijriYear,
                month: monthAndDay.month,
                day: monthAndDay.day
            }, jd);

            return correctedDate;
        }

        function getHijriYearStart(hijriYear) {
            const hijriEpoch = 1948439.5;
            return hijriEpoch + Math.floor((hijriYear - 1) * 354.367);
        }

        function getHijriMonthAndDay(hijriYear, dayOfYear) {
            const monthDays = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];

            if (isHijriLeapYear(hijriYear)) {
                monthDays[11] = 30;
            }

            let remainingDays = dayOfYear;
            let month = 1;

            for (let i = 0; i < 12; i++) {
                if (remainingDays < monthDays[i]) {
                    return {
                        month: month,
                        day: remainingDays + 1
                    };
                }
                remainingDays -= monthDays[i];
                month++;
            }

            return {
                month: 12,
                day: monthDays[11]
            };
        }

        function applyHijriCorrection(hijriDate, julianDay) {
            const corrections = {
                '2451545': { year: 1420, month: 9, day: 24 },  // 1/1/2000
                '2460438': { year: 1446, month: 12, day: 9 },  // 5/6/2025
                '2460647': { year: 1446, month: 12, day: 9 },  // تاريخ اليوم
            };

            const jdKey = Math.floor(julianDay).toString();

            if (corrections[jdKey]) {
                return corrections[jdKey];
            }

            const gregorianYear = getGregorianYearFromJulian(julianDay);

            if (gregorianYear >= 2024 && gregorianYear <= 2030) {
                // لا حاجة لتصحيح إضافي

                if (hijriDate.year > 1446) {
                    hijriDate.year = 1446;
                }
            }

            return hijriDate;
        }

        function getGregorianYearFromJulian(jd) {
            const a = jd + 32044;
            const b = Math.floor((4 * a + 3) / 146097);
            const c = a - Math.floor((146097 * b) / 4);
            const d = Math.floor((4 * c + 3) / 1461);
            const e = c - Math.floor((1461 * d) / 4);
            const m = Math.floor((5 * e + 2) / 153);

            const year = 100 * b + d - 4800 + Math.floor(m / 10);
            return year;
        }

        function getHijriMonthLength(hijriYear, hijriMonth) {
            if (hijriMonth === 12 && isHijriLeapYear(hijriYear)) {
                return 30;
            }
            return hijriMonth % 2 === 1 ? 30 : 29;
        }

        function isHijriLeapYear(hijriYear) {
            const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
            const yearInCycle = hijriYear % 30;
            return leapYears.includes(yearInCycle);
        }

        // تحويل التاريخ التفاعلي
        function convertInteractiveDate() {
            const gregorianDate = document.getElementById('gregorianDate').value;
            const resultDiv = document.getElementById('interactiveResult');

            if (gregorianDate) {
                const hijriDate = gregorianToHijri(gregorianDate);
                if (hijriDate) {
                    resultDiv.textContent = `التاريخ الهجري: ${hijriDate}`;
                    resultDiv.className = 'result';
                } else {
                    resultDiv.textContent = 'خطأ في التحويل - تحقق من التاريخ';
                    resultDiv.className = 'result error';
                }
            } else {
                resultDiv.textContent = 'اختر تاريخاً ميلادياً للتحويل';
                resultDiv.className = 'result';
            }
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>نتائج الاختبارات:</h3>';

            const testCases = [
                {
                    name: 'اختبار التاريخ المحدد الأول (1/1/2000)',
                    gregorian: '2000-01-01',
                    description: '1 يناير 2000 (يجب أن يعطي 24 رمضان 1420)',
                    expected: '24 رمضان 1420'
                },
                {
                    name: 'اختبار التاريخ المحدد الثاني (5/6/2025)',
                    gregorian: '2025-06-05',
                    description: '5 يونيو 2025 (يجب أن يعطي 9 ذو الحجة 1446)',
                    expected: '9 ذو الحجة 1446'
                },
                {
                    name: 'بداية الألفية الثالثة',
                    gregorian: '2000-01-01',
                    description: '1 يناير 2000'
                },
                {
                    name: 'تاريخ حديث',
                    gregorian: '2024-01-01',
                    description: '1 يناير 2024'
                },
                {
                    name: 'منتصف السنة',
                    gregorian: '2023-06-15',
                    description: '15 يونيو 2023'
                },
                {
                    name: 'نهاية السنة',
                    gregorian: '2023-12-31',
                    description: '31 ديسمبر 2023'
                },
                {
                    name: 'يوم كبيس',
                    gregorian: '2024-02-29',
                    description: '29 فبراير 2024 (سنة كبيسة)'
                },
                {
                    name: 'تاريخ قديم',
                    gregorian: '1900-01-01',
                    description: '1 يناير 1900'
                },
                {
                    name: 'تاريخ مستقبلي',
                    gregorian: '2030-12-25',
                    description: '25 ديسمبر 2030'
                }
            ];

            testCases.forEach((testCase, index) => {
                const result = gregorianToHijri(testCase.gregorian);
                const isExpectedMatch = testCase.expected ? result === testCase.expected : true;

                resultsDiv.innerHTML += `
                    <div class="test-case">
                        <h4>اختبار ${index + 1}: ${testCase.name}</h4>
                        <p><strong>التاريخ الميلادي:</strong> ${testCase.description}</p>
                        <p><strong>التاريخ الهجري المحسوب:</strong> ${result || 'فشل في التحويل'}</p>
                        ${testCase.expected ? `<p><strong>النتيجة المتوقعة:</strong> ${testCase.expected}</p>` : ''}
                        <p><strong>الحالة:</strong> ${result ? (
                            testCase.expected ?
                                (isExpectedMatch ? '✅ نجح ويطابق المتوقع' : '⚠️ نجح ولكن لا يطابق المتوقع') :
                                '✅ نجح التحويل'
                        ) : '❌ فشل التحويل'}</p>
                    </div>
                `;
            });
        }

        // مقارنة مع تواريخ معروفة
        function compareKnownDates() {
            const resultsDiv = document.getElementById('comparisonResults');
            resultsDiv.innerHTML = '<h3>مقارنة مع التواريخ المعروفة:</h3>';

            // تواريخ معروفة تقريبية (قد تختلف قليلاً حسب الحسابات الفلكية الدقيقة)
            const knownDates = [
                {
                    gregorian: '2024-01-01',
                    expectedHijri: 'حوالي 19 جمادى الثانية 1445',
                    description: 'بداية 2024'
                },
                {
                    gregorian: '2023-01-01',
                    expectedHijri: 'حوالي 8 جمادى الثانية 1444',
                    description: 'بداية 2023'
                },
                {
                    gregorian: '2022-01-01',
                    expectedHijri: 'حوالي 28 جمادى الأولى 1443',
                    description: 'بداية 2022'
                }
            ];

            let tableHTML = `
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>التاريخ الميلادي</th>
                            <th>النتيجة المحسوبة</th>
                            <th>التاريخ المتوقع</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            knownDates.forEach(dateInfo => {
                const calculated = gregorianToHijri(dateInfo.gregorian);

                tableHTML += `
                    <tr>
                        <td>${dateInfo.gregorian}</td>
                        <td>${calculated}</td>
                        <td>${dateInfo.expectedHijri}</td>
                        <td>${dateInfo.description}</td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
                <p><strong>ملاحظة:</strong> التواريخ الهجرية المحسوبة تقريبية وقد تختلف عن التواريخ الفلكية الدقيقة بيوم أو يومين.</p>
            `;

            resultsDiv.innerHTML += tableHTML;
        }

        // اختبار حالات الخطأ
        function testErrorCases() {
            const resultsDiv = document.getElementById('errorResults');
            resultsDiv.innerHTML = '<h3>نتائج اختبار حالات الخطأ:</h3>';

            const errorCases = [
                {
                    name: 'تاريخ فارغ',
                    input: '',
                    expectedResult: ''
                },
                {
                    name: 'قيمة null',
                    input: null,
                    expectedResult: ''
                },
                {
                    name: 'تاريخ غير صحيح',
                    input: 'invalid-date',
                    expectedResult: ''
                },
                {
                    name: 'شهر غير موجود',
                    input: '2024-13-01',
                    expectedResult: ''
                },
                {
                    name: 'يوم غير موجود',
                    input: '2024-02-30',
                    expectedResult: ''
                },
                {
                    name: 'تاريخ قبل الهجرة',
                    input: '0500-01-01',
                    expectedResult: 'قبل الهجرة'
                }
            ];

            errorCases.forEach((errorCase, index) => {
                const result = gregorianToHijri(errorCase.input);
                const isCorrect = result === errorCase.expectedResult;

                resultsDiv.innerHTML += `
                    <div class="test-case">
                        <h4>اختبار خطأ ${index + 1}: ${errorCase.name}</h4>
                        <p><strong>المدخل:</strong> "${errorCase.input}"</p>
                        <p><strong>النتيجة:</strong> "${result}"</p>
                        <p><strong>المتوقع:</strong> "${errorCase.expectedResult}"</p>
                        <p><strong>الحالة:</strong> ${isCorrect ? '✅ تم التعامل مع الخطأ بشكل صحيح' : '❌ لم يتم التعامل مع الخطأ'}</p>
                    </div>
                `;
            });
        }

        // تشغيل اختبار سريع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌙 صفحة اختبار تحويل التاريخ الهجري جاهزة');

            // ملء تاريخ تجريبي
            document.getElementById('gregorianDate').value = '2024-01-15';
            convertInteractiveDate();
        });
    </script>
</body>
</html>
