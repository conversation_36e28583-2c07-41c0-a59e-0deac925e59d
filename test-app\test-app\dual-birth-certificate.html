<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عقد ازدياد مزدوج - عربي/فرنسي</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 5mm;
            font-size: 10px;
            font-weight: 500;
            line-height: 1.2;
            background: #f0f0f0;
        }
        .document {
            width: 190mm;
            height: 120mm;
            margin: 0 auto;
            padding: 3mm 3mm 3mm 3mm;
            background: white;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 3mm;
            padding-bottom: 2mm;
            font-size: 9px;
            font-weight: bold;
        }
        .header-right { text-align: right; direction: rtl; }
        .header-center { text-align: center; flex: 1; margin: 0 5mm; }
        .header-left { text-align: left; direction: ltr; }

        /* Unified content layout */
        .content {
            height: calc(100% - 20mm);
            padding: 2mm;
            display: flex;
            flex-direction: column;
        }

        /* Title */
        .dual-title {
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            margin: 2mm 0;
            padding: 2mm;
            border: 1px solid #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .title-french {
            flex: 1;
            text-align: left;
            direction: ltr;
        }
        .title-arabic {
            flex: 1;
            text-align: right;
            direction: rtl;
        }

        /* Content lines - Merged */
        .merged-line {
            margin: 0.5mm 0;
            font-size: 9px;
            font-weight: 500;
            display: flex;
            align-items: baseline;
            justify-content: space-between;
        }
        .french-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: ltr;
            text-align: left;
        }
        .arabic-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: rtl;
            text-align: right;
        }
        .label {
            font-weight: 600;
            margin: 0 2mm;
            white-space: nowrap;
            min-width: 15mm;
        }
        .underline {
            border-bottom: 1px solid #000;
            min-width: 15mm;
            padding: 0 1mm;
            flex: 1;
            min-height: 2mm;
            font-weight: 500;
        }

        /* Act info */
        .act-info {
            margin: 2mm 0;
            font-size: 8px;
        }
        .act-line {
            display: flex;
            justify-content: space-between;
            margin: 1mm 0;
        }

        /* Signature area */
        .signature {
            margin-top: auto;
            text-align: center;
            font-size: 7px;
            padding-top: 3mm;
            border-top: 0.5px solid #000;
        }
        .stamp {
            width: 15mm;
            height: 15mm;
            border: 1px solid #000;
            margin: 2mm auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 6px;
            line-height: 1;
        }

        /* Test controls */
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,123,255,0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .controls button {
            background: white;
            color: #007bff;
            border: none;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
                background: white;
            }
            .document {
                width: 190mm;
                height: 120mm;
            }
            .controls { display: none; }
        }
    </style>
</head>
<body>
    <div class="controls">
        <div>عقد ازدياد مزدوج - نصف A4</div>
        <a href="main-dashboard.html" style="background: white; color: #007bff; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block;">🏠 الرئيسية</a>
        <a href="citizens-database.html" style="background: white; color: #007bff; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block;">👥 قاعدة البيانات</a>
        <button onclick="window.print()">طباعة</button>
        <button onclick="fillTestData()">بيانات تجريبية</button>
    </div>

    <div class="document">
        <!-- Header -->
        <div class="header">
            <div class="header-right">
                <div>المملكة المغربية</div>
                <div>وزارة الداخلية</div>
                <div>إقليم أسفي</div>
                <div>جماعة أيير</div>
                <div>مكتب الحالة المدنية أيير</div>
            </div>
            <div class="header-center">
                <div style="font-size: 7px; line-height: 1.2;">
                    طبقا للقانون رقم 37.99 المتعلق بالحالة المدنية والصادر بتنفيذه<br>
                    الظهير الشريف رقم 1.02.239<br>
                    بتاريخ 25 رجب 1423 (3) أكتوبر (2002)
                </div>
            </div>
            <div class="header-left">
                <div>ROYAUME DU MAROC</div>
                <div>Ministère de l'Intérieur</div>
                <div>Province de Safi</div>
                <div>Commune d'Ayir</div>
                <div>Bureau de l'État-Civil d'Ayir</div>
            </div>
        </div>

        <!-- Unified content with merged languages -->
        <div class="content">


            <!-- Title with Act Information -->
            <div style="display: flex; align-items: flex-start; margin: 2mm 0; padding: 2mm;">
                <!-- French Act Info (Left) -->
                <div style="flex: 1; text-align: left; direction: ltr; font-size: 8px;">
                    <div>Acte N°: <span style="border-bottom: 1px solid #000; min-width: 25mm; display: inline-block;" id="actNumberFr">2024/0001</span></div>
                    <div style="margin-top: 2mm; display: flex; align-items: flex-start;">
                        <span style="margin-right: 3mm;">Année</span>
                        <div style="display: flex; flex-direction: column;">
                            <span style="font-size: 7px;">Hégirienne</span>
                            <span style="font-size: 7px; margin-top: 1mm;">Grégorienne</span>
                        </div>
                        <div style="display: flex; flex-direction: column; margin-left: 2mm;">
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="hijriYearFr">1445</span>
                            <span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block; margin-top: 1mm;" id="gregYearFr">2024</span>
                        </div>
                    </div>
                </div>

                <!-- Centered Title -->
                <div style="flex: 2; text-align: center; font-weight: bold;">
                    <div style="font-size: 12px; margin-bottom: 2mm;">نسخة موجزة من رسم الولادة</div>
                    <div style="font-size: 10px;">COPIE ABRÉGÉE D'ACTE DE NAISSANCE</div>
                </div>

                <!-- Arabic Act Info (Right) -->
                <div style="flex: 1; text-align: right; direction: rtl; font-size: 8px;">
                    <div>رقم القيد: <span style="border-bottom: 1px solid #000; min-width: 25mm; display: inline-block;" id="actNumberAr">2024/0001</span></div>
                    <table style="margin-top: 2mm; border-collapse: collapse; direction: rtl;">
                        <tr>
                            <td rowspan="2" style="vertical-align: top; padding-left: 3mm;">سنة</td>
                            <td style="font-size: 7px; padding-left: 2mm;">هجرية</td>
                            <td><span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="hijriYearAr">1445</span></td>
                        </tr>
                        <tr>
                            <td style="font-size: 7px; padding-left: 2mm; padding-top: 1mm;">ميلادية</td>
                            <td style="padding-top: 1mm;"><span style="border-bottom: 1px solid #000; min-width: 15mm; display: inline-block;" id="gregYearAr">2024</span></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Prénom</span>
                    <span class="underline" id="firstNameFr">Ahmed</span>
                </div>
                <div class="arabic-part">
                    <span class="label">الاسم الشخصي</span>
                    <span class="underline" id="firstNameAr">أحمد</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Nom de famille</span>
                    <span class="underline" id="familyNameFr">Mohamed Ali</span>
                </div>
                <div class="arabic-part">
                    <span class="label">الاسم العائلي</span>
                    <span class="underline" id="familyNameAr">محمد علي</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Lieu de naissance</span>
                    <span class="underline" id="birthPlaceFr">Rabat</span>
                </div>
                <div class="arabic-part">
                    <span class="label">مكان الازدياد</span>
                    <span class="underline" id="birthPlaceAr">الرباط</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Né(e) le</span>
                    <span class="underline" id="birthDateFr">15 Janvier 2024</span>
                </div>
                <div class="arabic-part">
                    <span class="label">ولد(ت) في يوم</span>
                    <span class="underline" id="birthDateAr">15 يناير 2024</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Correspondant au</span>
                    <span class="underline" id="hijriDateFr">5 رجب 1445</span>
                </div>
                <div class="arabic-part">
                    <span class="label">الموافق لـ</span>
                    <span class="underline" id="hijriDateAr">5 رجب 1445</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Sexe</span>
                    <span class="underline" id="genderFr">Masculin</span>
                </div>
                <div class="arabic-part">
                    <span class="label">جنسه(ا)</span>
                    <span class="underline" id="genderAr">ذكر</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Père</span>
                    <span class="underline" id="fatherNameFr">Mohamed Ali Hassan</span>
                </div>
                <div class="arabic-part">
                    <span class="label">والده(ا) هو</span>
                    <span class="underline" id="fatherNameAr">محمد علي حسن</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Mère</span>
                    <span class="underline" id="motherNameFr">Fatima Ahmed Mohamed</span>
                </div>
                <div class="arabic-part">
                    <span class="label">والدته(ا) هي</span>
                    <span class="underline" id="motherNameAr">فاطمة أحمد محمد</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Mention marginale</span>
                    <span class="underline" id="marginalNoteFr">Néant</span>
                </div>
                <div class="arabic-part">
                    <span class="label">بيان (الوفاة) المشار إليه في طرة الرسم</span>
                    <span class="underline" id="marginalNoteAr">لا شيء</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Nous certifions en qualité d'officier</span>
                    <span class="underline" id="certificationFr">de l'état civil</span>
                </div>
                <div class="arabic-part">
                    <span class="label">نشهد بصفتنا ضابطا الحالة المدنية نحن</span>
                    <span class="underline" id="certificationAr">ضابط الحالة المدنية</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">La conformité de cette copie aux registres</span>
                    <span class="underline" id="conformityFr">de l'état civil d'Ayir</span>
                </div>
                <div class="arabic-part">
                    <span class="label">بمطابقة هذه النسخة لما هو مضمن بسجلات الحالة المدنية لمكتب الحالة المدنية بأيير</span>
                    <span class="underline" id="conformityAr">مطابقة</span>
                </div>
            </div>

            <div class="merged-line">
                <div class="french-part">
                    <span class="label">Établi à</span>
                    <span class="underline" id="issuePlaceFr">أيير</span>
                    <span class="label">le</span>
                    <span class="underline" id="issueDateFr">20 Janvier 2024</span>
                </div>
                <div class="arabic-part">
                    <span class="label">حرر بـ</span>
                    <span class="underline" id="issuePlaceAr">أيير</span>
                    <span class="label">بتاريخ</span>
                    <span class="underline" id="issueDateAr">20 يناير 2024</span>
                </div>
            </div>

            <!-- Signature Section -->
            <div class="merged-line" style="margin-top: 10mm; padding-top: 0mm;">
                <div class="french-part" style="text-align: center; padding-right: 0mm; margin-left: 20mm;">
                    <div style="font-weight: 600;">ضابط الحالة المدنية<br><span style="font-size: 7px; font-weight: 500;">L'Officier de l'État-Civil</span></div>
                </div>
                <div class="arabic-part" style="text-align: center; padding-left: 0mm; margin-right: 20mm;">
                    <div style="font-weight: 600;">طابع مكتب الحالة المدنية<br><span style="font-size: 7px; font-weight: 500;">Cachet du Bureau de l'État-Civil</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function fillTestData() {
            // Arabic data
            document.getElementById('firstNameAr').textContent = 'أحمد';
            document.getElementById('familyNameAr').textContent = 'محمد علي';
            document.getElementById('birthDateAr').textContent = '15 يناير 2024';
            document.getElementById('birthPlaceAr').textContent = 'الرباط';
            document.getElementById('fatherNameAr').textContent = 'محمد علي حسن';
            document.getElementById('motherNameAr').textContent = 'فاطمة أحمد محمد';

            // French data
            document.getElementById('firstNameFr').textContent = 'Ahmed';
            document.getElementById('familyNameFr').textContent = 'Mohamed Ali';
            document.getElementById('birthDateFr').textContent = '15 Janvier 2024';
            document.getElementById('birthPlaceFr').textContent = 'Rabat';
            document.getElementById('fatherNameFr').textContent = 'Mohamed Ali Hassan';
            document.getElementById('motherNameFr').textContent = 'Fatima Ahmed Mohamed';
        }

        // Load data from URL parameters
        function loadDataFromURL() {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.has('firstNameAr') || urlParams.has('firstName')) {
                // Fill Arabic fields
                document.getElementById('firstNameAr').textContent = urlParams.get('firstNameAr') || urlParams.get('firstName') || '';
                document.getElementById('familyNameAr').textContent = urlParams.get('familyNameAr') || urlParams.get('familyName') || '';
                document.getElementById('birthPlaceAr').textContent = urlParams.get('birthPlaceAr') || urlParams.get('birthPlace') || '';
                document.getElementById('birthDateAr').textContent = urlParams.get('birthDate') || '';
                document.getElementById('hijriDateAr').textContent = urlParams.get('hijriDate') || '';
                document.getElementById('genderAr').textContent = urlParams.get('gender') || '';
                document.getElementById('fatherNameAr').textContent = urlParams.get('fatherNameAr') || urlParams.get('fatherName') || '';
                document.getElementById('motherNameAr').textContent = urlParams.get('motherNameAr') || urlParams.get('motherName') || '';
                document.getElementById('actNumberAr').textContent = urlParams.get('actNumber') || '';

                // Fill French fields
                document.getElementById('firstNameFr').textContent = urlParams.get('firstNameFr') || urlParams.get('firstName') || '';
                document.getElementById('familyNameFr').textContent = urlParams.get('familyNameFr') || urlParams.get('familyName') || '';
                document.getElementById('birthPlaceFr').textContent = urlParams.get('birthPlaceFr') || urlParams.get('birthPlace') || '';
                document.getElementById('birthDateFr').textContent = urlParams.get('birthDate') || '';
                document.getElementById('hijriDateFr').textContent = urlParams.get('hijriDate') || '';
                document.getElementById('genderFr').textContent = urlParams.get('gender') === 'ذكر' ? 'Masculin' : 'Féminin';
                document.getElementById('fatherNameFr').textContent = urlParams.get('fatherNameFr') || urlParams.get('fatherName') || '';
                document.getElementById('motherNameFr').textContent = urlParams.get('motherNameFr') || urlParams.get('motherName') || '';
                document.getElementById('actNumberFr').textContent = urlParams.get('actNumber') || '';

                // Set current date for issue
                const today = new Date().toLocaleDateString('ar-EG');
                document.getElementById('issueDateAr').textContent = today;
                document.getElementById('issueDateFr').textContent = today;
            }
        }

        function printDocument() {
            window.print();
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', loadDataFromURL);
    </script>
</body>
</html>
