<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنسيق التواريخ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .test-result {
            background: rgba(0,0,0,0.2);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        .correct { color: #2ecc71; }
        .incorrect { color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تنسيق التواريخ</h1>
        <p>هذا الاختبار يتحقق من صحة تنسيق أسماء الأشهر وأرقامها</p>

        <div class="test-section">
            <h2>📅 اختبار الأشهر العربية</h2>
            <button onclick="testArabicMonths()">اختبار الأشهر العربية</button>
            <div id="arabicResults"></div>
        </div>

        <div class="test-section">
            <h2>📅 اختبار الأشهر الفرنسية</h2>
            <button onclick="testFrenchMonths()">اختبار الأشهر الفرنسية</button>
            <div id="frenchResults"></div>
        </div>

        <div class="test-section">
            <h2>🔍 اختبار تواريخ محددة</h2>
            <button onclick="testSpecificDates()">اختبار تواريخ محددة</button>
            <div id="specificResults"></div>
        </div>

        <div class="test-section">
            <h2>⚠️ اختبار حالات الخطأ</h2>
            <button onclick="testErrorCases()">اختبار حالات الخطأ</button>
            <div id="errorResults"></div>
        </div>
    </div>

    <script>
        // وظائف تنسيق التواريخ المحدثة
        function formatArabicDate(dateString) {
            if (!dateString) return '';
            
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;
                
                const months = [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ];

                const day = date.getDate();
                const monthIndex = date.getMonth(); // 0-11
                const month = months[monthIndex];
                const year = date.getFullYear();
                
                return `${day} ${month} ${year}`;
            } catch (error) {
                console.error('❌ خطأ في تنسيق التاريخ العربي:', error);
                return dateString;
            }
        }

        function formatFrenchDate(dateString) {
            if (!dateString) return '';
            
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;
                
                const months = [
                    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
                ];

                const day = date.getDate();
                const monthIndex = date.getMonth(); // 0-11
                const month = months[monthIndex];
                const year = date.getFullYear();
                
                return `${day} ${month} ${year}`;
            } catch (error) {
                console.error('❌ خطأ في تنسيق التاريخ الفرنسي:', error);
                return dateString;
            }
        }

        // اختبار الأشهر العربية
        function testArabicMonths() {
            const results = document.getElementById('arabicResults');
            results.innerHTML = '<h3>نتائج اختبار الأشهر العربية:</h3>';
            
            const expectedMonths = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            for (let i = 0; i < 12; i++) {
                const testDate = `2024-${String(i + 1).padStart(2, '0')}-15`;
                const formatted = formatArabicDate(testDate);
                const expectedMonth = expectedMonths[i];
                const isCorrect = formatted.includes(expectedMonth);
                
                results.innerHTML += `
                    <div class="test-result ${isCorrect ? 'correct' : 'incorrect'}">
                        الشهر ${i + 1}: ${testDate} → ${formatted} 
                        ${isCorrect ? '✅' : '❌'} (متوقع: ${expectedMonth})
                    </div>
                `;
            }
        }

        // اختبار الأشهر الفرنسية
        function testFrenchMonths() {
            const results = document.getElementById('frenchResults');
            results.innerHTML = '<h3>نتائج اختبار الأشهر الفرنسية:</h3>';
            
            const expectedMonths = [
                'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
            ];

            for (let i = 0; i < 12; i++) {
                const testDate = `2024-${String(i + 1).padStart(2, '0')}-15`;
                const formatted = formatFrenchDate(testDate);
                const expectedMonth = expectedMonths[i];
                const isCorrect = formatted.includes(expectedMonth);
                
                results.innerHTML += `
                    <div class="test-result ${isCorrect ? 'correct' : 'incorrect'}">
                        الشهر ${i + 1}: ${testDate} → ${formatted} 
                        ${isCorrect ? '✅' : '❌'} (متوقع: ${expectedMonth})
                    </div>
                `;
            }
        }

        // اختبار تواريخ محددة
        function testSpecificDates() {
            const results = document.getElementById('specificResults');
            results.innerHTML = '<h3>نتائج اختبار تواريخ محددة:</h3>';
            
            const testCases = [
                { date: '2024-01-01', expectedAr: '1 يناير 2024', expectedFr: '1 Janvier 2024' },
                { date: '2024-02-29', expectedAr: '29 فبراير 2024', expectedFr: '29 Février 2024' },
                { date: '2024-12-31', expectedAr: '31 ديسمبر 2024', expectedFr: '31 Décembre 2024' },
                { date: '2023-06-15', expectedAr: '15 يونيو 2023', expectedFr: '15 Juin 2023' },
                { date: '2025-09-08', expectedAr: '8 سبتمبر 2025', expectedFr: '8 Septembre 2025' }
            ];

            testCases.forEach(testCase => {
                const arabicResult = formatArabicDate(testCase.date);
                const frenchResult = formatFrenchDate(testCase.date);
                const arabicCorrect = arabicResult === testCase.expectedAr;
                const frenchCorrect = frenchResult === testCase.expectedFr;
                
                results.innerHTML += `
                    <div class="test-result">
                        <strong>التاريخ: ${testCase.date}</strong><br>
                        العربية: ${arabicResult} ${arabicCorrect ? '✅' : '❌'}<br>
                        الفرنسية: ${frenchResult} ${frenchCorrect ? '✅' : '❌'}
                    </div>
                `;
            });
        }

        // اختبار حالات الخطأ
        function testErrorCases() {
            const results = document.getElementById('errorResults');
            results.innerHTML = '<h3>نتائج اختبار حالات الخطأ:</h3>';
            
            const errorCases = [
                { input: '', description: 'نص فارغ' },
                { input: null, description: 'قيمة null' },
                { input: undefined, description: 'قيمة undefined' },
                { input: 'invalid-date', description: 'تاريخ غير صحيح' },
                { input: '2024-13-01', description: 'شهر غير موجود' },
                { input: '2024-02-30', description: 'يوم غير موجود' }
            ];

            errorCases.forEach(errorCase => {
                const arabicResult = formatArabicDate(errorCase.input);
                const frenchResult = formatFrenchDate(errorCase.input);
                
                results.innerHTML += `
                    <div class="test-result">
                        <strong>${errorCase.description}: "${errorCase.input}"</strong><br>
                        العربية: ${arabicResult || 'فارغ'}<br>
                        الفرنسية: ${frenchResult || 'فارغ'}
                    </div>
                `;
            });
        }

        // تشغيل جميع الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 صفحة اختبار التواريخ جاهزة');
        });
    </script>
</body>
</html>
