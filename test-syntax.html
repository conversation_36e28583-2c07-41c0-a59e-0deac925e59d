<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأخطاء النحوية</title>
</head>
<body>
    <h1>اختبار الأخطاء النحوية</h1>
    <p>إذا رأيت هذه الرسالة، فلا توجد أخطاء نحوية في JavaScript</p>
    
    <script>
        console.log('Testing JavaScript syntax...');
        
        // Test template literal with Arabic text
        const arabicText = `
            <div>
                <span>النص العربي: ${new Date().toLocaleDateString('ar-MA')}</span>
            </div>
        `;
        
        console.log('Arabic template literal test passed');
        
        // Test string concatenation with Arabic
        const htmlContent = '<!DOCTYPE html>' +
            '<html dir="rtl">' +
            '<head>' +
            '<meta charset="UTF-8">' +
            '<title>Test</title>' +
            '</head>' +
            '<body>' +
            '<p>Test content</p>' +
            '</body>' +
            '</html>';
            
        console.log('String concatenation test passed');
        
        alert('✅ جميع الاختبارات نجحت - لا توجد أخطاء نحوية');
    </script>
</body>
</html>
