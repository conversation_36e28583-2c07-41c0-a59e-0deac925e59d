<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث في سجلات المواطنين</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', '<PERSON>i', 'Times New Roman', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
            color: white;
            padding: 0;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #006233 0%, #c41e3a 50%, #006233 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 25px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
            letter-spacing: 1px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.95;
            margin-bottom: 20px;
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.15);
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
        }

        .content {
            padding: 20px;
        }

        .search-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .search-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .form-group label {
            display: block;
            margin-bottom: 3px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 3px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #c41e3a, #8b0000);
            color: white;
            box-shadow: 0 4px 15px rgba(196, 30, 58, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #006233, #004d26);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 98, 51, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .results-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .citizen-card {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid #c41e3a;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .citizen-card:hover {
            transform: translateX(5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            border-color: #c41e3a;
        }

        .citizen-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .citizen-actions {
            text-align: left;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 12px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 3px solid #c41e3a;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        .stat-number {
            font-size: 1.6em;
            font-weight: 800;
            color: #c41e3a;
            text-shadow: 0 1px 2px rgba(196, 30, 58, 0.2);
        }

        .stat-label {
            color: #6c757d;
            margin-top: 4px;
            font-weight: 600;
            font-size: 12px;
        }

        .certificate-badge {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 10px;
        }

        .no-certificate {
            background: #e74c3c;
        }

        /* Delete Modal */
        .delete-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        .delete-modal-content {
            position: relative;
            margin: 10% auto;
            padding: 0;
            width: 90%;
            max-width: 500px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }

        .delete-modal-header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }

        .delete-modal-header h2 {
            margin: 0;
            font-size: 1.5em;
        }

        .delete-modal-body {
            padding: 30px;
            text-align: center;
        }

        .citizen-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #e74c3c;
        }

        .citizen-details h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .citizen-details p {
            margin: 5px 0;
            color: #555;
        }

        .warning-text {
            color: #e74c3c;
            font-weight: bold;
            font-size: 1.1em;
            margin: 20px 0;
        }

        .delete-modal-footer {
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #eee;
            border-radius: 0 0 15px 15px;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 12px 25px;
            margin: 0 10px;
            font-size: 16px;
        }

        .btn-cancel {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
            padding: 12px 25px;
            margin: 0 10px;
            font-size: 16px;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            .citizen-info {
                grid-template-columns: 1fr;
            }

            .search-group {
                grid-template-columns: 1fr;
            }

            .delete-modal-content {
                margin: 5% auto;
                width: 95%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <h1>🔍 البحث في سجلات المواطنين</h1>
                <p>مكتب الحالة المدنية - أيير • نظام IndexedDB المتطور</p>

                <div class="nav-links">
                    <a href="main-dashboard.html" class="nav-link">🏠 الصفحة الرئيسية</a>
                    <a href="citizens-database-indexeddb.html" class="nav-link">🗃️ إدارة البيانات</a>
                    <a href="dual-birth-certificate.html" class="nav-link">📜 عقود الازدياد</a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Statistics -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalCitizens">0</div>
                    <div class="stat-label">إجمالي المواطنين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="livingCitizens">0</div>
                    <div class="stat-label">الأحياء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="deceasedCitizens">0</div>
                    <div class="stat-label">المتوفين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="withCertificates">0</div>
                    <div class="stat-label">لديهم شهادات كاملة</div>
                </div>
            </div>

            <!-- Search Section -->
            <div class="search-section">
                <h2 style="margin-bottom: 20px; color: #2c3e50;">🔍 البحث المتقدم</h2>

                <!-- Search Type Selection -->
                <div style="margin-bottom: 20px; text-align: center;">
                    <label style="margin-left: 20px; font-weight: 600; color: #2c3e50;">
                        <input type="radio" name="searchType" value="all" checked onchange="toggleSearchFields()">
                        جميع السجلات
                    </label>
                    <label style="margin-left: 20px; font-weight: 600; color: #2c3e50;">
                        <input type="radio" name="searchType" value="living" onchange="toggleSearchFields()">
                        الأحياء فقط
                    </label>
                    <label style="margin-left: 20px; font-weight: 600; color: #2c3e50;">
                        <input type="radio" name="searchType" value="deceased" onchange="toggleSearchFields()">
                        المتوفين فقط
                    </label>
                    <label style="margin-left: 20px; font-weight: 600; color: #2c3e50;">
                        <input type="radio" name="searchType" value="actNumber" onchange="toggleSearchFields()">
                        البحث برقم العقد فقط
                    </label>
                </div>

                <div class="search-group">
                    <div class="form-group">
                        <label for="searchName">الاسم (عربي أو فرنسي):</label>
                        <input type="text" id="searchName" placeholder="ابحث بالاسم الشخصي أو العائلي">
                    </div>

                    <div class="form-group">
                        <label for="searchActNumber">رقم القيد:</label>
                        <input type="text" id="searchActNumber" placeholder="مثال: 20/2025">
                    </div>

                    <div class="form-group">
                        <label for="searchBirthDate">تاريخ الازدياد:</label>
                        <input type="date" id="searchBirthDate">
                    </div>

                    <div class="form-group">
                        <label for="searchParent">اسم الوالد/الوالدة:</label>
                        <input type="text" id="searchParent" placeholder="ابحث باسم الوالد أو الوالدة">
                    </div>
                </div>

                <!-- Death Search Fields -->
                <div id="deathSearchFields" style="display: none;">
                    <h3 style="color: #2c3e50; margin: 20px 0 15px 0; text-align: center;">⚱️ البحث في سجلات الوفيات</h3>
                    <div class="search-group">
                        <div class="form-group">
                            <label for="searchDeathDate">تاريخ الوفاة:</label>
                            <input type="date" id="searchDeathDate">
                        </div>

                        <div class="form-group">
                            <label for="searchDeathPlace">مكان الوفاة:</label>
                            <input type="text" id="searchDeathPlace" placeholder="ابحث بمكان الوفاة">
                        </div>

                        <div class="form-group">
                            <label for="searchDeathCause">سبب الوفاة:</label>
                            <input type="text" id="searchDeathCause" placeholder="ابحث بسبب الوفاة">
                        </div>

                        <div class="form-group">
                            <label for="searchDeathYear">سنة الوفاة:</label>
                            <input type="number" id="searchDeathYear" placeholder="أدخل السنة فقط (مثال: 2024)" min="1900" max="2100" title="البحث بالسنة فقط">
                        </div>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn btn-primary" onclick="searchCitizens()">🔍 بحث</button>
                    <button class="btn btn-secondary" onclick="clearSearch()">🗑️ مسح البحث</button>
                    <button class="btn btn-success" onclick="showAll()">👥 عرض الكل</button>
                    <button class="btn btn-warning" onclick="refreshStatistics()">🔄 تحديث الإحصائيات</button>
                    <button class="btn btn-info" onclick="testDeathSearch()">🧪 اختبار بحث الوفيات</button>
                    <button class="btn btn-success" onclick="testDeathCertificate()">📄 اختبار شهادة وفاة</button>
                </div>

                <!-- Backup Section -->
                <div style="margin-top: 15px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
                    <h5 style="color: #856404; margin-bottom: 8px; text-align: center; font-size: 14px;">💾 النسخ الاحتياطي والأداء</h5>
                    <div style="display: flex; gap: 6px; flex-wrap: wrap; justify-content: center;">
                        <button class="btn btn-primary" onclick="exportAllData()" style="font-size: 12px; padding: 6px 12px;">📤 تصدير البيانات</button>
                        <button class="btn btn-warning" onclick="autoBackup()" style="font-size: 12px; padding: 6px 12px;">🔄 نسخ احتياطي</button>
                        <button class="btn btn-secondary" onclick="checkPerformance()" style="font-size: 12px; padding: 6px 12px;">⚡ فحص الأداء</button>
                        <button class="btn btn-success" onclick="optimizeStoredImages()" style="font-size: 12px; padding: 6px 12px;">🗜️ ضغط الصور</button>
                        <button class="btn btn-danger" onclick="fixDatabase()" style="font-size: 12px; padding: 6px 12px;">🔧 إصلاح قاعدة البيانات</button>
                    </div>
                </div>

                <!-- Performance Settings -->
                <div style="margin-top: 12px; padding: 12px; background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 6px;">
                    <h5 style="color: #0c5460; margin-bottom: 8px; text-align: center; font-size: 14px;">⚙️ إعدادات الأداء</h5>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap; justify-content: center; align-items: center;">
                        <label style="color: #0c5460; font-size: 13px;">
                            <strong>عدد النتائج في الصفحة:</strong>
                            <select id="itemsPerPageSelect" onchange="changeItemsPerPage()" style="margin-right: 5px; padding: 4px; border-radius: 4px; font-size: 12px;">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </label>
                        <button class="btn btn-secondary" onclick="cleanupMemory()" style="font-size: 11px; padding: 5px 10px;">🧹 تنظيف الذاكرة</button>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="results-section">
                <h2 style="margin-bottom: 20px; color: #2c3e50;">📋 نتائج البحث</h2>
                <div id="searchResults">
                    <p style="text-align: center; color: #7f8c8d; font-style: italic;">استخدم البحث أعلاه للعثور على المواطنين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="delete-modal">
        <div class="delete-modal-content">
            <div class="delete-modal-header">
                <h2>⚠️ تأكيد الحذف</h2>
            </div>
            <div class="delete-modal-body">
                <p>هل أنت متأكد من حذف بيانات هذا المواطن؟</p>

                <div class="citizen-details" id="citizenDetails">
                    <h3>بيانات المواطن:</h3>
                    <p><strong>الاسم:</strong> <span id="deleteCitizenName"></span></p>
                    <p><strong>رقم القيد:</strong> <span id="deleteCitizenActNumber"></span></p>
                    <p><strong>تاريخ الازدياد:</strong> <span id="deleteCitizenBirthDate"></span></p>
                </div>

                <div class="warning-text">
                    ⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!
                </div>
            </div>
            <div class="delete-modal-footer">
                <button class="btn btn-danger" onclick="confirmDelete()">🗑️ تأكيد الحذف</button>
                <button class="btn btn-cancel" onclick="cancelDelete()">❌ إلغاء</button>
            </div>
        </div>
    </div>

    <script>
        // متغيرات قاعدة البيانات
        let db;
        const DB_NAME = 'CitizensDatabase';  // تطابق مع الملف الأصلي
        const DB_VERSION = 2;
        const STORE_NAME = 'citizens';

        // تهيئة قاعدة البيانات مع معالجة أفضل للأخطاء
        function initDB() {
            return new Promise((resolve, reject) => {
                try {
                    console.log('محاولة فتح قاعدة البيانات:', DB_NAME);

                    // فحص دعم IndexedDB
                    if (!window.indexedDB) {
                        throw new Error('IndexedDB غير مدعوم في هذا المتصفح');
                    }

                    const request = indexedDB.open(DB_NAME, DB_VERSION);

                    request.onerror = (event) => {
                        console.error('خطأ في فتح قاعدة البيانات:', event.target.error);
                        reject(new Error('فشل في فتح قاعدة البيانات: ' + event.target.error));
                    };

                    request.onsuccess = (event) => {
                        db = event.target.result;
                        console.log('تم فتح قاعدة البيانات بنجاح');

                        // التحقق من وجود المخزن المطلوب
                        if (!db.objectStoreNames.contains(STORE_NAME)) {
                            console.warn('المخزن غير موجود، سيتم إنشاؤه في التحديث التالي');
                            db.close();
                            // إعادة فتح بإصدار أعلى لإنشاء المخزن
                            const upgradeRequest = indexedDB.open(DB_NAME, DB_VERSION + 1);
                            upgradeRequest.onupgradeneeded = (upgradeEvent) => {
                                const upgradeDb = upgradeEvent.target.result;
                                if (!upgradeDb.objectStoreNames.contains(STORE_NAME)) {
                                    const store = upgradeDb.createObjectStore(STORE_NAME, { keyPath: 'id' });
                                    store.createIndex('firstNameAr', 'firstNameAr', { unique: false });
                                    store.createIndex('familyNameAr', 'familyNameAr', { unique: false });
                                    store.createIndex('actNumber', 'actNumber', { unique: false });
                                    store.createIndex('isDeceased', 'isDeceased', { unique: false });
                                    console.log('تم إنشاء المخزن الجديد');
                                }
                            };
                            upgradeRequest.onsuccess = () => {
                                db = upgradeRequest.result;
                                resolve(db);
                            };
                            upgradeRequest.onerror = () => reject(upgradeRequest.error);
                        } else {
                            console.log('المخزن موجود:', STORE_NAME);
                            resolve(db);
                        }
                    };

                    request.onupgradeneeded = (event) => {
                        console.log('تحديث قاعدة البيانات...');
                        db = event.target.result;

                        // إنشاء المخزن إذا لم يكن موجوداً
                        if (!db.objectStoreNames.contains(STORE_NAME)) {
                            console.log('إنشاء مخزن جديد:', STORE_NAME);
                            const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
                            store.createIndex('firstNameAr', 'firstNameAr', { unique: false });
                            store.createIndex('familyNameAr', 'familyNameAr', { unique: false });
                            store.createIndex('actNumber', 'actNumber', { unique: false });
                            store.createIndex('isDeceased', 'isDeceased', { unique: false });
                            console.log('تم إنشاء المخزن بنجاح');
                        } else {
                            console.log('المخزن موجود بالفعل:', STORE_NAME);
                        }
                    };

                    request.onblocked = () => {
                        console.warn('قاعدة البيانات محجوبة، يرجى إغلاق التبويبات الأخرى');
                        reject(new Error('قاعدة البيانات محجوبة'));
                    };

                } catch (error) {
                    console.error('خطأ في تهيئة قاعدة البيانات:', error);
                    reject(error);
                }
            });
        }

        // وظيفة إظهار/إخفاء حقول البحث
        function toggleSearchFields() {
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            const deathFields = document.getElementById('deathSearchFields');

            console.log('🔄 تغيير نوع البحث إلى:', searchType);

            if (searchType === 'deceased') {
                deathFields.style.display = 'block';
                console.log('👁️ إظهار حقول البحث في الوفيات');
            } else if (searchType === 'actNumber') {
                deathFields.style.display = 'none';
                // تركيز على حقل رقم العقد
                setTimeout(() => {
                    document.getElementById('searchActNumber').focus();
                }, 100);
                console.log('🔢 التركيز على حقل رقم العقد');
            } else {
                deathFields.style.display = 'none';
                console.log('👥 الوضع العام للبحث');
            }
        }

        // وظيفة البحث المحدثة
        async function searchCitizens() {
            try {
                if (!db) {
                    await initDB();
                }

                const searchType = document.querySelector('input[name="searchType"]:checked').value;
                const searchName = document.getElementById('searchName').value.toLowerCase().trim();
                const searchActNumber = document.getElementById('searchActNumber').value.trim();
                const searchBirthDate = document.getElementById('searchBirthDate').value;
                const searchParent = document.getElementById('searchParent').value.toLowerCase().trim();

                // حقول البحث الخاصة بالوفيات
                const searchDeathDate = document.getElementById('searchDeathDate').value;
                const searchDeathPlace = document.getElementById('searchDeathPlace').value.toLowerCase().trim();
                const searchDeathCause = document.getElementById('searchDeathCause').value.toLowerCase().trim();
                const searchDeathYear = document.getElementById('searchDeathYear').value;

                // تشخيص معايير البحث
                console.log('🔍 معايير البحث:', {
                    searchType: searchType,
                    searchName: searchName,
                    searchActNumber: searchActNumber,
                    searchBirthDate: searchBirthDate,
                    searchParent: searchParent,
                    searchDeathDate: searchDeathDate,
                    searchDeathPlace: searchDeathPlace,
                    searchDeathCause: searchDeathCause,
                    searchDeathYear: searchDeathYear
                });

                // في وضع البحث برقم العقد فقط، تجاهل الحقول الأخرى
                if (searchType === 'actNumber') {
                    if (!searchActNumber) {
                        alert('⚠️ يرجى إدخال رقم العقد للبحث');
                        document.getElementById('searchActNumber').focus();
                        return;
                    }
                    console.log('🔢 البحث برقم العقد فقط:', searchActNumber);
                }

                const transaction = db.transaction([STORE_NAME], 'readonly');
                const store = transaction.objectStore(STORE_NAME);
                const request = store.getAll();

                request.onsuccess = () => {
                    let results = request.result;
                    console.log(`📊 إجمالي السجلات في قاعدة البيانات: ${results.length}`);

                    // عرض عينة من البيانات للتشخيص
                    if (results.length > 0) {
                        const sampleRecord = results[0];
                        console.log('📋 عينة من البيانات:', {
                            id: sampleRecord.id,
                            name: `${sampleRecord.firstNameAr || sampleRecord.personalName} ${sampleRecord.familyNameAr || sampleRecord.familyName}`,
                            isDeceased: sampleRecord.isDeceased,
                            hasDeathInfo: !!sampleRecord.deathInfo
                        });
                    }

                    // إحصائيات قبل التصفية
                    const totalLiving = results.filter(citizen => !citizen.isDeceased).length;
                    const totalDeceased = results.filter(citizen => citizen.isDeceased).length;
                    console.log(`📈 قبل التصفية: ${totalLiving} أحياء، ${totalDeceased} متوفين`);

                    // تصفية حسب نوع البحث
                    if (searchType === 'living') {
                        results = results.filter(citizen => !citizen.isDeceased);
                        console.log(`🔍 تصفية الأحياء: ${results.length} نتيجة`);
                    } else if (searchType === 'deceased') {
                        results = results.filter(citizen => citizen.isDeceased);
                        console.log(`⚱️ تصفية المتوفين: ${results.length} نتيجة`);
                    } else if (searchType === 'actNumber') {
                        // في وضع البحث برقم العقد، البحث بالمطابقة التامة فقط
                        results = results.filter(citizen => {
                            const actNumber = citizen.actNumber || '';
                            // مطابقة تامة (حساسة لحالة الأحرف أو غير حساسة)
                            return actNumber.toLowerCase() === searchActNumber.toLowerCase();
                        });
                        console.log(`🔢 البحث بالمطابقة التامة لرقم العقد (${searchActNumber}): ${results.length} نتيجة`);

                        // عرض النتائج مباشرة بدون تطبيق فلاتر أخرى
                        console.log(`✅ النتائج النهائية للبحث برقم العقد: ${results.length} سجل`);
                        displayResults(results);
                        return;
                    }

                    // تطبيق فلاتر البحث العامة
                    if (searchName) {
                        results = results.filter(citizen =>
                            (citizen.firstNameAr && citizen.firstNameAr.toLowerCase().includes(searchName)) ||
                            (citizen.familyNameAr && citizen.familyNameAr.toLowerCase().includes(searchName)) ||
                            (citizen.firstNameFr && citizen.firstNameFr.toLowerCase().includes(searchName)) ||
                            (citizen.familyNameFr && citizen.familyNameFr.toLowerCase().includes(searchName)) ||
                            (citizen.personalName && citizen.personalName.toLowerCase().includes(searchName)) ||
                            (citizen.familyName && citizen.familyName.toLowerCase().includes(searchName))
                        );
                    }

                    if (searchActNumber) {
                        results = results.filter(citizen => {
                            const actNumber = citizen.actNumber || '';
                            // مطابقة تامة لرقم العقد
                            return actNumber.toLowerCase() === searchActNumber.toLowerCase();
                        });
                        console.log(`🔢 فلتر رقم العقد بالمطابقة التامة (${searchActNumber}): ${results.length} نتيجة`);
                    }

                    if (searchBirthDate) {
                        results = results.filter(citizen =>
                            citizen.birthDate === searchBirthDate
                        );
                    }

                    if (searchParent) {
                        results = results.filter(citizen =>
                            (citizen.fatherNameAr && citizen.fatherNameAr.toLowerCase().includes(searchParent)) ||
                            (citizen.motherNameAr && citizen.motherNameAr.toLowerCase().includes(searchParent)) ||
                            (citizen.fatherNameFr && citizen.fatherNameFr.toLowerCase().includes(searchParent)) ||
                            (citizen.motherNameFr && citizen.motherNameFr.toLowerCase().includes(searchParent)) ||
                            (citizen.fatherName && citizen.fatherName.toLowerCase().includes(searchParent)) ||
                            (citizen.motherName && citizen.motherName.toLowerCase().includes(searchParent))
                        );
                    }

                    // تطبيق فلاتر البحث الخاصة بالوفيات
                    if (searchType === 'deceased' || searchType === 'all') {
                        console.log('🔍 تطبيق فلاتر الوفيات...');

                        if (searchDeathDate) {
                            const beforeFilter = results.length;
                            results = results.filter(citizen =>
                                citizen.deathInfo && citizen.deathInfo.deathDate === searchDeathDate
                            );
                            console.log(`📅 فلتر تاريخ الوفاة (${searchDeathDate}): ${beforeFilter} → ${results.length}`);
                        }

                        if (searchDeathPlace) {
                            const beforeFilter = results.length;
                            results = results.filter(citizen =>
                                citizen.deathInfo && citizen.deathInfo.deathPlace &&
                                citizen.deathInfo.deathPlace.toLowerCase().includes(searchDeathPlace)
                            );
                            console.log(`🏥 فلتر مكان الوفاة (${searchDeathPlace}): ${beforeFilter} → ${results.length}`);
                        }

                        if (searchDeathCause) {
                            const beforeFilter = results.length;
                            results = results.filter(citizen =>
                                citizen.deathInfo && citizen.deathInfo.deathCause &&
                                citizen.deathInfo.deathCause.toLowerCase().includes(searchDeathCause)
                            );
                            console.log(`💊 فلتر سبب الوفاة (${searchDeathCause}): ${beforeFilter} → ${results.length}`);
                        }

                        if (searchDeathYear) {
                            const beforeFilter = results.length;
                            results = results.filter(citizen => {
                                if (!citizen.deathInfo || !citizen.deathInfo.deathDate) {
                                    return false;
                                }

                                // استخراج السنة من تاريخ الوفاة
                                let deathYear;
                                try {
                                    // التعامل مع تنسيقات التاريخ المختلفة
                                    const deathDate = citizen.deathInfo.deathDate;
                                    if (deathDate.includes('-')) {
                                        // تنسيق YYYY-MM-DD أو DD-MM-YYYY
                                        const parts = deathDate.split('-');
                                        if (parts[0].length === 4) {
                                            deathYear = parts[0]; // YYYY-MM-DD
                                        } else {
                                            deathYear = parts[2]; // DD-MM-YYYY
                                        }
                                    } else if (deathDate.includes('/')) {
                                        // تنسيق DD/MM/YYYY أو MM/DD/YYYY
                                        const parts = deathDate.split('/');
                                        deathYear = parts[2]; // افتراض أن السنة في النهاية
                                    } else {
                                        // محاولة تحويل التاريخ باستخدام Date
                                        deathYear = new Date(deathDate).getFullYear().toString();
                                    }

                                    const isMatch = deathYear === searchDeathYear;
                                    if (isMatch) {
                                        console.log(`✅ تطابق سنة الوفاة: ${deathDate} → ${deathYear}`);
                                    }
                                    return isMatch;
                                } catch (error) {
                                    console.warn('خطأ في تحليل تاريخ الوفاة:', deathDate, error);
                                    return false;
                                }
                            });
                            console.log(`📆 فلتر سنة الوفاة (${searchDeathYear}): ${beforeFilter} → ${results.length}`);
                        }
                    }

                    console.log(`✅ النتائج النهائية: ${results.length} سجل`);
                    displayResults(results);
                };

                request.onerror = () => {
                    console.error('خطأ في البحث:', request.error);
                };

            } catch (error) {
                console.error('خطأ في البحث:', error);
            }
        }

        // وظيفة عرض النتائج المحدثة
        function displayResults(citizens) {
            const resultsContainer = document.getElementById('searchResults');

            if (citizens.length === 0) {
                resultsContainer.innerHTML = '<p style="text-align: center; color: #7f8c8d;">لم يتم العثور على نتائج مطابقة</p>';
                return;
            }

            let html = `<p style="margin-bottom: 20px; font-weight: 600; color: #2c3e50;">تم العثور على ${citizens.length} نتيجة:</p>`;

            citizens.forEach(citizen => {
                const isDeceased = citizen.isDeceased;
                const statusBadge = isDeceased ?
                    '<span style="background: #e74c3c; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-right: 10px;">⚱️ متوفى</span>' :
                    '<span style="background: #27ae60; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-right: 10px;">💚 حي</span>';

                const certificateBadge = citizen.certificateImage ?
                    '<span class="certificate-badge">📄 شهادة كاملة</span>' :
                    '<span class="certificate-badge no-certificate">❌ بدون شهادة</span>';

                html += `
                    <div class="citizen-card">
                        <div class="citizen-info">
                            <div>
                                <strong>الاسم:</strong> ${citizen.firstNameAr || citizen.personalName || 'غير محدد'} ${citizen.familyNameAr || citizen.familyName || ''}
                                ${statusBadge}
                                ${certificateBadge}
                            </div>
                            <div><strong>رقم القيد:</strong> ${citizen.actNumber || 'غير محدد'}</div>
                            <div><strong>تاريخ الازدياد:</strong> ${citizen.birthDate || 'غير محدد'}</div>
                            <div><strong>الوالد:</strong> ${citizen.fatherNameAr || citizen.fatherName || 'غير محدد'}</div>
                            <div><strong>الوالدة:</strong> ${citizen.motherNameAr || citizen.motherName || 'غير محدد'}</div>
                            ${isDeceased && citizen.deathInfo ? `
                                <div><strong>تاريخ الوفاة:</strong> ${citizen.deathInfo.deathDate || 'غير محدد'}</div>
                                <div><strong>مكان الوفاة:</strong> ${citizen.deathInfo.deathPlace || 'غير محدد'}</div>
                                ${citizen.deathInfo.deathCause ? `<div><strong>سبب الوفاة:</strong> ${citizen.deathInfo.deathCause}</div>` : ''}
                            ` : ''}
                        </div>
                        <div class="citizen-actions">
                            <button class="btn btn-primary" onclick="editCitizen('${citizen.id}')">✏️ تعديل</button>
                            <button class="btn btn-warning" onclick="deleteCitizen(${citizen.id}, '${citizen.personalName} ${citizen.familyName}', '${citizen.actNumber}', '${citizen.birthDate}')">🗑️ حذف</button>
                            ${citizen.certificateImage ?
                                `<button class="btn btn-success" onclick="viewCertificate(${citizen.id})">👁️ عرض الشهادة</button>` :
                                ''
                            }

                            ${isDeceased ?
                                `<button class="btn btn-secondary" onclick="viewDeathCertificate('${citizen.id}')">👁️ عرض شهادة الوفاة</button>
                                 <button class="btn btn-info" onclick="editDeathCertificate('${citizen.id}')">✏️ تعديل شهادة الوفاة</button>` :
                                `<button class="btn btn-secondary" onclick="printBirthCertificate('${citizen.id}')">🖨️ عقد ازدياد</button>`
                            }
                        </div>
                    </div>
                `;
            });

            resultsContainer.innerHTML = html;
        }

        // تحديث الإحصائيات
        async function updateStatistics() {
            try {
                console.log('بدء تحديث الإحصائيات...');

                if (!db) {
                    console.log('قاعدة البيانات غير متاحة، جاري التهيئة...');
                    await initDB();
                }

                console.log('قاعدة البيانات متاحة، جاري قراءة البيانات...');
                const transaction = db.transaction([STORE_NAME], 'readonly');
                const store = transaction.objectStore(STORE_NAME);
                const request = store.getAll();

                request.onsuccess = () => {
                    const citizens = request.result;
                    console.log('تم العثور على', citizens.length, 'سجل في قاعدة البيانات');

                    // طباعة عينة من البيانات للتشخيص
                    if (citizens.length > 0) {
                        console.log('عينة من البيانات:', citizens[0]);
                    }

                    const total = citizens.length;
                    const living = citizens.filter(c => !c.isDeceased).length;
                    const deceased = citizens.filter(c => c.isDeceased === true).length;
                    const withCertificates = citizens.filter(c => c.certificateImage).length;

                    console.log('الإحصائيات:', {
                        total: total,
                        living: living,
                        deceased: deceased,
                        withCertificates: withCertificates
                    });

                    // تحديث العناصر في الصفحة
                    const totalElement = document.getElementById('totalCitizens');
                    const livingElement = document.getElementById('livingCitizens');
                    const deceasedElement = document.getElementById('deceasedCitizens');
                    const certificatesElement = document.getElementById('withCertificates');

                    if (totalElement) totalElement.textContent = total;
                    if (livingElement) livingElement.textContent = living;
                    if (deceasedElement) deceasedElement.textContent = deceased;
                    if (certificatesElement) certificatesElement.textContent = withCertificates;

                    console.log('تم تحديث الإحصائيات بنجاح');
                };

                request.onerror = () => {
                    console.error('خطأ في قراءة البيانات:', request.error);
                };

                transaction.onerror = () => {
                    console.error('خطأ في المعاملة:', transaction.error);
                };

            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // مسح البحث
        function clearSearch() {
            console.log('🗑️ مسح جميع حقول البحث');

            document.getElementById('searchName').value = '';
            document.getElementById('searchActNumber').value = '';
            document.getElementById('searchBirthDate').value = '';
            document.getElementById('searchParent').value = '';
            document.getElementById('searchDeathDate').value = '';
            document.getElementById('searchDeathPlace').value = '';
            document.getElementById('searchDeathCause').value = '';
            document.getElementById('searchDeathYear').value = '';

            // إعادة تعيين نوع البحث
            document.querySelector('input[name="searchType"][value="all"]').checked = true;
            toggleSearchFields();

            document.getElementById('searchResults').innerHTML = '<p style="text-align: center; color: #7f8c8d; font-style: italic;">استخدم البحث أعلاه للعثور على المواطنين</p>';

            console.log('✅ تم مسح جميع الحقول وإعادة تعيين الوضع الافتراضي');
        }

        // إعادة تحديث الإحصائيات يدوياً
        async function refreshStatistics() {
            console.log('إعادة تحديث الإحصائيات يدوياً...');
            await updateStatistics();
        }

        // فحص قواعد البيانات المتاحة وإصلاح المشاكل
        async function checkDatabases() {
            try {
                console.log('فحص قواعد البيانات المتاحة...');

                // فحص IndexedDB
                if (!('indexedDB' in window)) {
                    console.error('IndexedDB غير متاح في هذا المتصفح');
                    return false;
                }

                console.log('IndexedDB متاح');

                // قائمة بأسماء قواعد البيانات المحتملة
                const possibleDBNames = ['CitizensDatabase', 'CivilRegistryDB', 'citizens-db'];

                for (const dbName of possibleDBNames) {
                    try {
                        console.log(`فحص قاعدة البيانات: ${dbName}`);

                        // فتح قاعدة البيانات بدون تحديد إصدار للفحص فقط
                        const request = indexedDB.open(dbName);
                        const result = await new Promise((resolve, reject) => {
                            request.onsuccess = () => {
                                const db = request.result;
                                const stores = Array.from(db.objectStoreNames);
                                console.log(`✅ قاعدة البيانات ${dbName} موجودة وتحتوي على المخازن:`, stores);

                                // فحص البيانات في المخزن
                                if (stores.includes('citizens')) {
                                    const transaction = db.transaction(['citizens'], 'readonly');
                                    const store = transaction.objectStore('citizens');
                                    const countRequest = store.count();

                                    countRequest.onsuccess = () => {
                                        const count = countRequest.result;
                                        console.log(`📊 عدد السجلات في ${dbName}/citizens:`, count);
                                        db.close();
                                        resolve({ name: dbName, count: count, stores: stores });
                                    };

                                    countRequest.onerror = () => {
                                        console.log(`❌ خطأ في عد السجلات في ${dbName}`);
                                        db.close();
                                        resolve({ name: dbName, count: 0, stores: stores });
                                    };
                                } else {
                                    console.log(`⚠️ قاعدة البيانات ${dbName} لا تحتوي على مخزن 'citizens'`);
                                    db.close();
                                    resolve({ name: dbName, count: 0, stores: stores });
                                }
                            };

                            request.onerror = () => {
                                console.log(`❌ لا توجد قاعدة بيانات باسم: ${dbName}`);
                                resolve(null);
                            };

                            request.onupgradeneeded = () => {
                                console.log(`🆕 قاعدة البيانات ${dbName} جديدة أو تحتاج تحديث`);
                                const db = request.result;
                                const stores = Array.from(db.objectStoreNames);
                                db.close();
                                resolve({ name: dbName, count: 0, stores: stores });
                            };
                        });

                        if (result && result.count > 0) {
                            console.log(`تم العثور على بيانات في: ${result.name}`);
                            return result;
                        }

                    } catch (error) {
                        console.log(`خطأ في فحص ${dbName}:`, error);
                    }
                }

                return null;

            } catch (error) {
                console.error('خطأ في فحص قواعد البيانات:', error);
                return null;
            }
        }

        // إصلاح قاعدة البيانات
        async function fixDatabase() {
            try {
                console.log('محاولة إصلاح قاعدة البيانات...');

                // حذف قاعدة البيانات الحالية وإعادة إنشائها
                const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

                await new Promise((resolve, reject) => {
                    deleteRequest.onsuccess = () => {
                        console.log('تم حذف قاعدة البيانات القديمة');
                        resolve();
                    };
                    deleteRequest.onerror = () => {
                        console.log('فشل في حذف قاعدة البيانات القديمة');
                        resolve(); // متابعة حتى لو فشل الحذف
                    };
                    deleteRequest.onblocked = () => {
                        console.warn('حذف قاعدة البيانات محجوب، يرجى إغلاق التبويبات الأخرى');
                        setTimeout(resolve, 2000); // انتظار ثم متابعة
                    };
                });

                // إنشاء قاعدة بيانات جديدة
                await initDB();
                console.log('تم إصلاح قاعدة البيانات بنجاح');
                return true;

            } catch (error) {
                console.error('فشل في إصلاح قاعدة البيانات:', error);
                return false;
            }
        }

        // تهيئة الصفحة مع معالجة شاملة للأخطاء
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('🚀 بدء تهيئة صفحة البحث...');

                // تهيئة قاعدة البيانات أولاً
                console.log('🔧 تهيئة قاعدة البيانات...');
                await initDB();

                // فحص قواعد البيانات المتاحة
                console.log('📊 فحص قواعد البيانات المتاحة...');
                const dbInfo = await checkDatabases();

                if (dbInfo && dbInfo.count > 0) {
                    console.log(`✅ تم العثور على بيانات في: ${dbInfo.name} (${dbInfo.count} سجل)`);
                } else if (dbInfo && dbInfo.count === 0) {
                    console.log(`⚠️ قاعدة البيانات ${dbInfo.name} موجودة ولكن فارغة`);
                } else {
                    console.log('⚠️ لم يتم العثور على قواعد بيانات موجودة');
                }

                // تحديث الإحصائيات
                console.log('📈 تحديث الإحصائيات...');
                await updateStatistics();

                console.log('✅ تم تهيئة الصفحة بنجاح');

                // عرض رسالة إذا كانت قاعدة البيانات فارغة
                if (!dbInfo || dbInfo.count === 0) {
                    const resultsContainer = document.getElementById('searchResults');
                    if (resultsContainer) {
                        resultsContainer.innerHTML = `
                            <div style="text-align: center; padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; color: #856404;">
                                <h3>📋 قاعدة البيانات فارغة</h3>
                                <p>لا توجد بيانات مواطنين في النظام حالياً</p>
                                <p>يمكنك إضافة بيانات جديدة من خلال:</p>
                                <div style="margin: 15px 0;">
                                    <a href="citizens-database-indexeddb.html" class="btn btn-primary" style="margin: 5px;">👥 إضافة مواطن جديد</a>
                                    <a href="death-data-entry.html" class="btn btn-secondary" style="margin: 5px;">⚱️ تسجيل وفاة</a>
                                </div>
                                <small style="color: #6c757d;">بعد إضافة البيانات، ارجع إلى هذه الصفحة لرؤية الإحصائيات والبحث</small>
                            </div>
                        `;
                    }
                }

            } catch (error) {
                console.error('❌ خطأ في تهيئة الصفحة:', error);

                // عرض رسالة للمستخدم
                const resultsContainer = document.getElementById('searchResults');
                if (resultsContainer) {
                    resultsContainer.innerHTML = `
                        <div style="text-align: center; padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; color: #721c24;">
                            <h3>⚠️ مشكلة في قاعدة البيانات</h3>
                            <p>حدث خطأ في تحميل قاعدة البيانات. يرجى:</p>
                            <ul style="text-align: right; margin: 10px 0;">
                                <li>إعادة تحميل الصفحة</li>
                                <li>التأكد من دعم المتصفح لـ IndexedDB</li>
                                <li>إغلاق التبويبات الأخرى للتطبيق</li>
                            </ul>
                            <button onclick="location.reload()" class="btn btn-primary" style="margin: 10px;">🔄 إعادة تحميل</button>
                            <button onclick="fixDatabase()" class="btn btn-warning" style="margin: 10px;">🔧 إصلاح قاعدة البيانات</button>
                        </div>
                    `;
                }

                // محاولة إعادة التهيئة بعد 3 ثوان
                setTimeout(async () => {
                    console.log('🔄 محاولة إعادة التهيئة...');
                    try {
                        await initDB();
                        await updateStatistics();
                        console.log('✅ نجحت إعادة التهيئة');

                        // إخفاء رسالة الخطأ
                        const resultsContainer = document.getElementById('searchResults');
                        if (resultsContainer) {
                            resultsContainer.innerHTML = '<p style="text-align: center; color: #7f8c8d; font-style: italic;">استخدم البحث أعلاه للعثور على المواطنين</p>';
                        }

                    } catch (retryError) {
                        console.error('❌ فشل في إعادة التهيئة:', retryError);
                    }
                }, 3000);
            }
        });

        // وظيفة تعديل المواطن مع تمرير البيانات
        async function editCitizen(id) {
            try {
                console.log('🔍 بدء تعديل المواطن:', id);

                // الحصول على بيانات المواطن من قاعدة البيانات
                if (!db) {
                    await initDB();
                }

                const transaction = db.transaction([STORE_NAME], 'readonly');
                const store = transaction.objectStore(STORE_NAME);

                // تحويل المعرف للنوع الصحيح إذا لزم الأمر
                let searchId = id;
                if (typeof id === 'string' && !isNaN(id)) {
                    searchId = parseInt(id);
                }

                const request = store.get(searchId);

                request.onsuccess = () => {
                    const citizen = request.result;
                    console.log('📋 بيانات المواطن للتعديل:', citizen);

                    if (!citizen) {
                        console.error('❌ لم يتم العثور على المواطن بالمعرف:', searchId);
                        alert('❌ لم يتم العثور على بيانات المواطن');
                        return;
                    }

                    // إنشاء معاملات URL مع جميع بيانات المواطن
                    const params = new URLSearchParams({
                        edit: 'true',
                        id: citizen.id,
                        firstNameAr: citizen.firstNameAr || citizen.personalName || '',
                        firstNameFr: citizen.firstNameFr || '',
                        familyNameAr: citizen.familyNameAr || citizen.familyName || '',
                        familyNameFr: citizen.familyNameFr || '',
                        birthPlaceAr: citizen.birthPlaceAr || citizen.birthPlace || '',
                        birthPlaceFr: citizen.birthPlaceFr || '',
                        birthDate: citizen.birthDate || '',
                        hijriDate: citizen.hijriDate || '',
                        gender: citizen.gender || '',
                        fatherNameAr: citizen.fatherNameAr || citizen.fatherName || '',
                        fatherNameFr: citizen.fatherNameFr || '',
                        motherNameAr: citizen.motherNameAr || citizen.motherName || '',
                        motherNameFr: citizen.motherNameFr || '',
                        actNumber: citizen.actNumber || '',
                        registrationDate: citizen.registrationDate || '',
                        // إضافة بيانات إضافية إذا كانت موجودة
                        profession: citizen.profession || '',
                        residence: citizen.residence || '',
                        maritalStatus: citizen.maritalStatus || '',
                        nationality: citizen.nationality || '',
                        isDeceased: citizen.isDeceased || false
                    });

                    // إضافة بيانات الوفاة إذا كانت موجودة
                    if (citizen.isDeceased && citizen.deathInfo) {
                        params.append('deathPlace', citizen.deathInfo.deathPlace || '');
                        params.append('deathDate', citizen.deathInfo.deathDate || '');
                        params.append('deathTime', citizen.deathInfo.deathTime || '');
                        params.append('deathCause', citizen.deathInfo.deathCause || '');
                    }

                    console.log('🔗 الانتقال إلى صفحة التعديل مع البيانات');

                    // الانتقال إلى صفحة قاعدة البيانات مع معاملات التعديل
                    window.location.href = 'citizens-database-indexeddb.html?' + params.toString();
                };

                request.onerror = () => {
                    console.error('❌ خطأ في قراءة بيانات المواطن:', request.error);
                    alert('❌ خطأ في قراءة بيانات المواطن');
                };

            } catch (error) {
                console.error('❌ خطأ في تعديل المواطن:', error);
                alert('❌ خطأ في تعديل المواطن');
            }
        }

        function deleteCitizen(id, name, actNumber, birthDate) {
            // تنفيذ حذف مبسط
            if (confirm(`هل أنت متأكد من حذف ${name}؟`)) {
                // كود الحذف هنا
                console.log('حذف المواطن:', id);
            }
        }

        function viewCertificate(id) {
            console.log('عرض الشهادة:', id);
        }

        // عرض شهادة الوفاة في نافذة منبثقة
        async function viewDeathCertificate(id) {
            try {
                console.log('🔍 بدء عرض شهادة الوفاة للمواطن:', id);
                console.log('نوع المعرف:', typeof id);

                // التحقق من وجود المعرف
                if (!id) {
                    console.error('❌ معرف المواطن غير موجود');
                    alert('❌ معرف المواطن غير صحيح');
                    return;
                }

                // الحصول على بيانات المواطن
                if (!db) {
                    console.log('🔧 تهيئة قاعدة البيانات...');
                    await initDB();
                }

                console.log('📊 البحث عن المواطن في قاعدة البيانات...');
                const transaction = db.transaction([STORE_NAME], 'readonly');
                const store = transaction.objectStore(STORE_NAME);

                // تحويل المعرف للنوع الصحيح إذا لزم الأمر
                let searchId = id;
                if (typeof id === 'string' && !isNaN(id)) {
                    searchId = parseInt(id);
                    console.log('🔄 تحويل المعرف من نص إلى رقم:', searchId);
                }

                const request = store.get(searchId);

                request.onsuccess = () => {
                    const citizen = request.result;
                    console.log('📋 نتيجة البحث:', citizen);

                    if (!citizen) {
                        console.error('❌ لم يتم العثور على المواطن بالمعرف:', searchId);
                        alert('❌ لم يتم العثور على بيانات المواطن');
                        return;
                    }

                    console.log('✅ تم العثور على المواطن:', {
                        id: citizen.id,
                        name: `${citizen.firstNameAr || citizen.personalName} ${citizen.familyNameAr || citizen.familyName}`,
                        isDeceased: citizen.isDeceased,
                        hasDeathInfo: !!citizen.deathInfo
                    });

                    if (!citizen.isDeceased) {
                        console.error('❌ المواطن ليس متوفى');
                        alert('❌ هذا المواطن ليس متوفى');
                        return;
                    }

                    if (!citizen.deathInfo) {
                        console.error('❌ لا توجد بيانات وفاة');
                        alert('❌ لا توجد بيانات وفاة لهذا المواطن');
                        return;
                    }

                    console.log('📄 بيانات الوفاة:', citizen.deathInfo);

                    // إنشاء محتوى شهادة الوفاة باستخدام النموذج الأصلي
                    const deathCertificateHTML = `
                        <div style="font-family: 'Arial', sans-serif; direction: rtl; background: white; padding: 20px; max-width: 800px; margin: 0 auto; border: 2px solid #333;">
                            <!-- Certificate Content -->
                            <div class="certificate-content">
                                <!-- Header -->
                                <div style="display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 14px; line-height: 1.6;">
                                    <div style="text-align: right;">
                                        المملكة المغربية<br>
                                        وزارة الداخلية<br>
                                        (إقليم أسفي)<br>
                                        جماعة أيير<br>
                                        مكتب الحالة المدنية :<br>
                                        ............./............. : عقد رقم
                                    </div>
                                    <div style="text-align: left;">
                                        <br><br><br><br><br><br>
                                    </div>
                                </div>

                                <!-- Title -->
                                <div style="text-align: center; font-size: 18px; font-weight: bold; margin: 20px 0; text-decoration: underline;">
                                    نسخة موجزة من رسم الوفاة
                                </div>

                                <!-- Body -->
                                <div style="font-size: 16px; line-height: 2.5; margin: 20px 0;">
                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">توفي(ت) بـ :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.deathInfo.deathPlace || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">في :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.deathInfo.deathDate || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">الاسم الشخصي :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.firstNameAr || citizen.personalName || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">الاسم العائلي :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.familyNameAr || citizen.familyName || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">تاريخ الازدياد :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.birthDate || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">مكان الازدياد :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.birthPlaceAr || citizen.birthPlace || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">مهنته (ها) :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.profession || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">الساكن (ة) بـ :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.residence || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">والده :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.fatherNameAr || citizen.fatherName || ''}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span style="display: inline-block; width: 120px;">والدته :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 400px; padding-bottom: 2px;">${citizen.motherNameAr || citizen.motherName || ''}</span>
                                    </div>

                                    <div style="margin: 15px 0 8px 0;">
                                        <span style="display: inline-block; width: 300px;">نشهد بصفتنا ضابطا للحالة المدنية نحن :</span>
                                        <span style="border-bottom: 1px dotted #333; display: inline-block; min-width: 220px; padding-bottom: 2px;">${citizen.deathInfo.certificationOfficer || 'ضابط الحالة المدنية - أيير'}</span>
                                    </div>

                                    <div style="margin: 8px 0;">
                                        <span>بمطابقة هذه النسخة لما هو مضمن في سجلات الحالة المدنية بالمكتب المذكور</span>
                                    </div>
                                </div>

                                <!-- Footer -->
                                <div style="display: flex; justify-content: space-between; margin-top: 40px; font-size: 14px;">
                                    <div style="text-align: right;">
                                        أيير : في ${new Date().toLocaleDateString('ar-MA')}<br>
                                        ضابط الحالة المدنية
                                    </div>
                                    <div style="text-align: left;">
                                        طابع مكتب الحالة المدنية<br>
                                        ضابط الحالة المدنية
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    console.log('🪟 إنشاء نافذة جديدة لعرض الشهادة...');

                    // فتح نافذة جديدة لعرض الشهادة
                    const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

                    if (!newWindow) {
                        console.error('❌ فشل في فتح النافذة الجديدة - قد يكون محجوب بواسطة المتصفح');
                        alert('❌ فشل في فتح النافذة الجديدة\nيرجى السماح للنوافذ المنبثقة في المتصفح');
                        return;
                    }

                    console.log('✅ تم فتح النافذة الجديدة بنجاح');
                    newWindow.document.write(`
                        <!DOCTYPE html>
                        <html dir="rtl">
                        <head>
                            <meta charset="UTF-8">
                            <title>نسخة موجزة من رسم الوفاة - ${citizen.firstNameAr || citizen.personalName} ${citizen.familyNameAr || citizen.familyName}</title>
                            <style>
                                body {
                                    margin: 20px;
                                    background: #f5f5f5;
                                    font-family: 'Arial', sans-serif;
                                }
                                .print-btn {
                                    position: fixed;
                                    top: 10px;
                                    right: 10px;
                                    background: #2196f3;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 5px;
                                    cursor: pointer;
                                    font-size: 14px;
                                    z-index: 1000;
                                    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                                }
                                .print-btn:hover {
                                    background: #1976d2;
                                    transform: translateY(-1px);
                                }
                                .close-btn {
                                    position: fixed;
                                    top: 10px;
                                    left: 10px;
                                    background: #f44336;
                                    color: white;
                                    border: none;
                                    padding: 10px 20px;
                                    border-radius: 5px;
                                    cursor: pointer;
                                    font-size: 14px;
                                    z-index: 1000;
                                    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                                }
                                .close-btn:hover {
                                    background: #d32f2f;
                                    transform: translateY(-1px);
                                }
                                @media print {
                                    .print-btn, .close-btn { display: none; }
                                    body { background: white; margin: 0; }
                                }
                            </style>
                        </head>
                        <body>
                            <button class="print-btn" onclick="window.print()">🖨️ طباعة</button>
                            <button class="close-btn" onclick="window.close()">❌ إغلاق</button>
                            ${deathCertificateHTML}
                        </body>
                        </html>
                    `);
                    newWindow.document.close();
                    console.log('✅ تم إنشاء شهادة الوفاة بنجاح');
                };

                request.onerror = () => {
                    console.error('خطأ في قراءة بيانات المواطن:', request.error);
                    alert('❌ خطأ في قراءة بيانات المواطن');
                };

            } catch (error) {
                console.error('خطأ في عرض شهادة الوفاة:', error);
                alert('❌ خطأ في عرض شهادة الوفاة');
            }
        }

        // تعديل شهادة الوفاة
        async function editDeathCertificate(id) {
            try {
                console.log('✏️ بدء تعديل شهادة الوفاة للمواطن:', id);

                // الحصول على بيانات المواطن من قاعدة البيانات
                if (!db) {
                    await initDB();
                }

                const transaction = db.transaction([STORE_NAME], 'readonly');
                const store = transaction.objectStore(STORE_NAME);

                // تحويل المعرف للنوع الصحيح إذا لزم الأمر
                let searchId = id;
                if (typeof id === 'string' && !isNaN(id)) {
                    searchId = parseInt(id);
                }

                const request = store.get(searchId);

                request.onsuccess = () => {
                    const citizen = request.result;
                    console.log('📋 بيانات المواطن لتعديل شهادة الوفاة:', citizen);

                    if (!citizen) {
                        console.error('❌ لم يتم العثور على المواطن بالمعرف:', searchId);
                        alert('❌ لم يتم العثور على بيانات المواطن');
                        return;
                    }

                    if (!citizen.isDeceased) {
                        alert('❌ هذا المواطن ليس متوفى');
                        return;
                    }

                    // إنشاء معاملات URL لصفحة تعديل شهادة الوفاة
                    const params = new URLSearchParams({
                        edit: 'true',
                        id: citizen.id,
                        // البيانات الشخصية
                        personalName: citizen.firstNameAr || citizen.personalName || '',
                        familyName: citizen.familyNameAr || citizen.familyName || '',
                        birthDate: citizen.birthDate || '',
                        birthPlace: citizen.birthPlaceAr || citizen.birthPlace || '',
                        gender: citizen.gender || '',
                        profession: citizen.profession || '',
                        residence: citizen.residence || '',
                        maritalStatus: citizen.maritalStatus || '',
                        nationality: citizen.nationality || 'مغربية',
                        fatherName: citizen.fatherNameAr || citizen.fatherName || '',
                        motherName: citizen.motherNameAr || citizen.motherName || '',
                        // بيانات الوفاة
                        deathPlace: citizen.deathInfo?.deathPlace || '',
                        deathDate: citizen.deathInfo?.deathDate || '',
                        deathTime: citizen.deathInfo?.deathTime || '',
                        deathCause: citizen.deathInfo?.deathCause || '',
                        age: citizen.age || ''
                    });

                    console.log('🔗 الانتقال إلى صفحة تعديل شهادة الوفاة');

                    // الانتقال إلى صفحة تعديل شهادة الوفاة
                    window.location.href = 'death-data-entry.html?' + params.toString();
                };

                request.onerror = () => {
                    console.error('❌ خطأ في قراءة بيانات المواطن:', request.error);
                    alert('❌ خطأ في قراءة بيانات المواطن');
                };

            } catch (error) {
                console.error('❌ خطأ في تعديل شهادة الوفاة:', error);
                alert('❌ خطأ في تعديل شهادة الوفاة');
            }
        }

        function printDeathCertificate(id) {
            window.location.href = `death-data-entry.html?print=${id}`;
        }

        function printBirthCertificate(id) {
            window.location.href = `dual-birth-certificate.html?print=${id}`;
        }

        function showAll() {
            searchCitizens();
        }

        // وظيفة اختبار البحث في الوفيات
        async function testDeathSearch() {
            try {
                console.log('🧪 بدء اختبار البحث في الوفيات...');

                // تعيين نوع البحث للمتوفين
                document.querySelector('input[name="searchType"][value="deceased"]').checked = true;
                toggleSearchFields();

                // مسح جميع الحقول أولاً
                clearSearch();

                // تعيين نوع البحث مرة أخرى بعد المسح
                document.querySelector('input[name="searchType"][value="deceased"]').checked = true;
                toggleSearchFields();

                // تشغيل البحث لعرض جميع المتوفين
                await searchCitizens();

                console.log('✅ تم تشغيل اختبار البحث في الوفيات');

            } catch (error) {
                console.error('❌ خطأ في اختبار البحث:', error);
            }
        }

        // وظيفة اختبار شهادة الوفاة
        async function testDeathCertificate() {
            try {
                console.log('📄 بدء اختبار شهادة الوفاة...');

                // البحث عن أول متوفى في قاعدة البيانات
                if (!db) {
                    await initDB();
                }

                const transaction = db.transaction([STORE_NAME], 'readonly');
                const store = transaction.objectStore(STORE_NAME);
                const request = store.getAll();

                request.onsuccess = () => {
                    const citizens = request.result;
                    const deceasedCitizens = citizens.filter(c => c.isDeceased && c.deathInfo);

                    console.log(`📊 تم العثور على ${deceasedCitizens.length} متوفى في قاعدة البيانات`);

                    if (deceasedCitizens.length === 0) {
                        alert('❌ لا توجد سجلات وفيات في قاعدة البيانات\nيرجى إضافة بيانات وفيات أولاً من مولد البيانات');
                        return;
                    }

                    // اختيار أول متوفى للاختبار
                    const testCitizen = deceasedCitizens[0];
                    console.log('🎯 اختبار شهادة الوفاة للمواطن:', {
                        id: testCitizen.id,
                        name: `${testCitizen.firstNameAr || testCitizen.personalName} ${testCitizen.familyNameAr || testCitizen.familyName}`
                    });

                    // عرض شهادة الوفاة
                    viewDeathCertificate(testCitizen.id);
                };

                request.onerror = () => {
                    console.error('❌ خطأ في قراءة قاعدة البيانات');
                    alert('❌ خطأ في قراءة قاعدة البيانات');
                };

            } catch (error) {
                console.error('❌ خطأ في اختبار شهادة الوفاة:', error);
                alert('❌ خطأ في اختبار شهادة الوفاة');
            }
        }



        // وظائف إضافية للأزرار
        function exportAllData() {
            console.log('تصدير البيانات...');
            alert('وظيفة التصدير قيد التطوير');
        }

        function autoBackup() {
            console.log('نسخ احتياطي...');
            alert('وظيفة النسخ الاحتياطي قيد التطوير');
        }

        function checkPerformance() {
            console.log('فحص الأداء...');
            alert('وظيفة فحص الأداء قيد التطوير');
        }

        function optimizeStoredImages() {
            console.log('ضغط الصور...');
            alert('وظيفة ضغط الصور قيد التطوير');
        }

        function changeItemsPerPage() {
            console.log('تغيير عدد العناصر في الصفحة...');
        }

        function cleanupMemory() {
            console.log('تنظيف الذاكرة...');
            if (window.gc) {
                window.gc();
            }
            alert('تم تنظيف الذاكرة');
        }
    </script>
</body>
</html>
