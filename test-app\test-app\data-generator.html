<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد البيانات التجريبية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content {
            padding: 30px;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid #ffc107;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .progress {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            background: linear-gradient(135deg, #3498db, #2980b9);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #3498db;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🧪 مولد البيانات التجريبية</h1>
            <p>إنشاء بيانات وهمية لاختبار أداء النظام</p>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Warning -->
            <div class="warning">
                <h3>⚠️ تحذير مهم</h3>
                <p><strong>هذه أداة اختبار فقط!</strong></p>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li>ستقوم بإنشاء بيانات وهمية لاختبار الأداء</li>
                    <li>قد تستغرق العملية وقتاً طويلاً مع الأرقام الكبيرة</li>
                    <li>تأكد من عمل نسخة احتياطية قبل البدء</li>
                    <li>لا تستخدم هذه البيانات في الإنتاج الفعلي</li>
                </ul>
            </div>

            <!-- Generator Form -->
            <div class="form-group">
                <label for="recordCount">عدد السجلات المطلوب إنشاؤها:</label>
                <select id="recordCount" onchange="updateImageRecommendation()">
                    <option value="100">100 سجل (اختبار سريع)</option>
                    <option value="1000" selected>1,000 سجل (اختبار متوسط)</option>
                    <option value="5000">5,000 سجل (اختبار كبير)</option>
                    <option value="10000">10,000 سجل (اختبار ضخم)</option>
                    <option value="50000">50,000 سجل (اختبار عملاق)</option>
                    <option value="100000">100,000 سجل (اختبار أقصى)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="recordType">نوع السجلات المطلوب إنشاؤها:</label>
                <select id="recordType" onchange="updateRecordTypeOptions()">
                    <option value="living" selected>مواطنين أحياء فقط</option>
                    <option value="deceased">متوفين فقط</option>
                    <option value="mixed">مختلط (أحياء ومتوفين)</option>
                </select>
            </div>

            <div class="form-group" id="deathPercentageGroup" style="display: none;">
                <label for="deathPercentage">نسبة المتوفين في السجلات المختلطة:</label>
                <select id="deathPercentage">
                    <option value="5">5% (نسبة قليلة)</option>
                    <option value="10" selected>10% (نسبة متوسطة)</option>
                    <option value="20">20% (نسبة عالية)</option>
                    <option value="30">30% (نسبة عالية جداً)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="withImages">نسبة السجلات التي تحتوي على شهادات:</label>
                <select id="withImages">
                    <option value="0" selected>0% (بدون صور - موصى به للاختبارات الكبيرة)</option>
                    <option value="10">10% (عُشر السجلات)</option>
                    <option value="25">25% (ربع السجلات)</option>
                    <option value="50">50% (نصف السجلات)</option>
                    <option value="75">75% (ثلاثة أرباع)</option>
                    <option value="100">100% (جميع السجلات - للاختبارات الصغيرة فقط)</option>
                </select>
                <div id="imageRecommendation" style="margin-top: 5px; font-size: 12px; color: #856404;"></div>
            </div>

            <div class="form-group">
                <label for="batchSize">حجم الدفعة (لتجنب تجمد المتصفح):</label>
                <select id="batchSize">
                    <option value="50">50 سجل في الدفعة (أكثر استقرار)</option>
                    <option value="100">100 سجل في الدفعة (متوازن)</option>
                    <option value="250" selected>250 سجل في الدفعة (سريع)</option>
                    <option value="500">500 سجل في الدفعة (أسرع)</option>
                </select>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin: 30px 0;">
                <button class="btn btn-danger" onclick="generateTestData()">🚀 بدء إنشاء البيانات</button>
                <button class="btn btn-primary" onclick="clearAllData()">🗑️ مسح جميع البيانات</button>
                <button class="btn btn-success" onclick="window.location.href='search-citizens.html'">🔍 اختبار البحث</button>
            </div>

            <!-- Progress -->
            <div class="progress" id="progressSection">
                <h4>⏳ جاري إنشاء البيانات باستخدام IndexedDB...</h4>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText" style="font-size: 18px; font-weight: bold; margin: 10px 0;">0%</div>
                <div id="progressDetails" style="background: #f8f9fa; padding: 15px; border-radius: 8px; white-space: pre-line; font-family: monospace; font-size: 14px; line-height: 1.4;"></div>

                <!-- Real-time stats during generation -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; margin-top: 15px;">
                    <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; text-align: center;">
                        <div style="font-weight: bold; color: #27ae60;" id="liveGenerated">0</div>
                        <div style="font-size: 12px;">تم إنشاؤها</div>
                    </div>
                    <div style="background: #e3f2fd; padding: 10px; border-radius: 5px; text-align: center;">
                        <div style="font-weight: bold; color: #2196f3;" id="liveLiving">0</div>
                        <div style="font-size: 12px;">أحياء</div>
                    </div>
                    <div style="background: #f3e5f5; padding: 10px; border-radius: 5px; text-align: center;">
                        <div style="font-weight: bold; color: #9c27b0;" id="liveDeceased">0</div>
                        <div style="font-size: 12px;">متوفين</div>
                    </div>
                    <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; text-align: center;">
                        <div style="font-weight: bold; color: #4caf50;" id="liveWithImages">0</div>
                        <div style="font-size: 12px;">مع شهادات</div>
                    </div>
                    <div style="background: #fff3e0; padding: 10px; border-radius: 5px; text-align: center;">
                        <div style="font-weight: bold; color: #ff9800;" id="liveSpeed">0</div>
                        <div style="font-size: 12px">سجل/ثانية</div>
                    </div>
                    <div style="background: #ffebee; padding: 10px; border-radius: 5px; text-align: center;">
                        <div style="font-weight: bold; color: #f44336;" id="liveErrors">0</div>
                        <div style="font-size: 12px;">أخطاء</div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalRecords">0</div>
                    <div class="stat-label">إجمالي السجلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="livingRecords">0</div>
                    <div class="stat-label">الأحياء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="deceasedRecords">0</div>
                    <div class="stat-label">المتوفين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="withCertificates">0</div>
                    <div class="stat-label">مع شهادات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="dataSize">0 MB</div>
                    <div class="stat-label">حجم البيانات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="generationTime">0s</div>
                    <div class="stat-label">وقت الإنشاء</div>
                </div>
            </div>

            <!-- Navigation -->
            <div style="text-align: center; margin-top: 30px;">
                <a href="main-dashboard.html" class="btn btn-primary">🏠 العودة للصفحة الرئيسية</a>
                <a href="citizens-database.html" class="btn btn-primary">👥 إدارة البيانات</a>
            </div>
        </div>
    </div>

    <script src="indexeddb-manager.js"></script>
    <script src="data-generator-indexeddb.js"></script>
</body>
</html>
