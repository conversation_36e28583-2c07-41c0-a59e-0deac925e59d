<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المستخدمين</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2em;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .result {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }

        .nav-link {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            margin: 0 10px;
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .nav-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام المستخدمين</h1>
        
        <div class="nav-links">
            <a href="main-dashboard.html" class="nav-link">🏠 الصفحة الرئيسية</a>
            <a href="login.html" class="nav-link">🔐 تسجيل الدخول</a>
            <a href="user-management.html" class="nav-link">👥 إدارة المستخدمين</a>
        </div>

        <!-- Test Database Connection -->
        <div class="test-section">
            <h2>🔗 اختبار الاتصال بقاعدة البيانات</h2>
            <button class="btn" onclick="testDatabaseConnection()">اختبار الاتصال</button>
            <div id="dbResult" class="result"></div>
        </div>

        <!-- Test User Manager -->
        <div class="test-section">
            <h2>👤 اختبار نظام إدارة المستخدمين</h2>
            <button class="btn" onclick="testUserManager()">تهيئة نظام المستخدمين</button>
            <button class="btn" onclick="testCreateUser()">إنشاء مستخدم تجريبي</button>
            <button class="btn" onclick="testLogin()">اختبار تسجيل الدخول</button>
            <div id="userResult" class="result"></div>
        </div>

        <!-- Test Statistics -->
        <div class="test-section">
            <h2>📊 اختبار الإحصائيات</h2>
            <button class="btn" onclick="testStatistics()">تحميل الإحصائيات</button>
            <button class="btn" onclick="testUserStats()">إحصائيات المستخدمين</button>
            <div id="statsResult" class="result"></div>
        </div>

        <!-- Test Auth Guard -->
        <div class="test-section">
            <h2>🛡️ اختبار نظام الحماية</h2>
            <button class="btn" onclick="testAuthGuard()">اختبار نظام الحماية</button>
            <button class="btn" onclick="testPermissions()">اختبار الصلاحيات</button>
            <div id="authResult" class="result"></div>
        </div>

        <!-- System Info -->
        <div class="test-section">
            <h2>ℹ️ معلومات النظام</h2>
            <button class="btn" onclick="showSystemInfo()">عرض معلومات النظام</button>
            <div id="systemResult" class="result"></div>
        </div>
    </div>

    <!-- Include User Management System -->
    <script src="user-management.js"></script>
    <script src="auth-guard.js"></script>

    <script>
        // Test functions
        async function testDatabaseConnection() {
            const result = document.getElementById('dbResult');
            result.className = 'result info';
            result.textContent = 'جاري اختبار الاتصال...';

            try {
                // Test IndexedDB availability
                if (!window.indexedDB) {
                    throw new Error('IndexedDB غير مدعوم في هذا المتصفح');
                }

                // Try to open database
                const request = indexedDB.open('CitizensDatabase', 3);
                
                const dbResult = await new Promise((resolve, reject) => {
                    request.onsuccess = () => {
                        const db = request.result;
                        const stores = Array.from(db.objectStoreNames);
                        db.close();
                        resolve({
                            success: true,
                            version: db.version,
                            stores: stores
                        });
                    };
                    request.onerror = () => reject(request.error);
                    request.onupgradeneeded = (event) => {
                        const db = event.target.result;
                        // Database will be created/updated
                        resolve({
                            success: true,
                            version: db.version,
                            stores: Array.from(db.objectStoreNames),
                            upgraded: true
                        });
                    };
                });

                result.className = 'result success';
                result.textContent = `✅ نجح الاتصال بقاعدة البيانات
الإصدار: ${dbResult.version}
الجداول: ${dbResult.stores.join(', ')}
${dbResult.upgraded ? 'تم تحديث قاعدة البيانات' : ''}`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ فشل الاتصال: ${error.message}`;
            }
        }

        async function testUserManager() {
            const result = document.getElementById('userResult');
            result.className = 'result info';
            result.textContent = 'جاري تهيئة نظام المستخدمين...';

            try {
                await userManager.init();
                
                result.className = 'result success';
                result.textContent = `✅ تم تهيئة نظام المستخدمين بنجاح
حالة تسجيل الدخول: ${userManager.isLoggedIn() ? 'مسجل دخول' : 'غير مسجل'}
المستخدم الحالي: ${userManager.getCurrentUser()?.username || 'لا يوجد'}`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ فشل في تهيئة نظام المستخدمين: ${error.message}`;
            }
        }

        async function testCreateUser() {
            const result = document.getElementById('userResult');
            result.className = 'result info';
            result.textContent = 'جاري إنشاء مستخدم تجريبي...';

            try {
                // First ensure user manager is initialized
                await userManager.init();

                // Try to login as admin first
                if (!userManager.isLoggedIn()) {
                    await userManager.login('admin', 'admin123');
                }

                const testUser = {
                    username: 'test_user',
                    email: '<EMAIL>',
                    password: 'test123',
                    fullName: 'مستخدم تجريبي',
                    role: 'employee'
                };

                const userId = await userManager.addUser(testUser);
                
                result.className = 'result success';
                result.textContent = `✅ تم إنشاء المستخدم التجريبي بنجاح
معرف المستخدم: ${userId}
اسم المستخدم: ${testUser.username}
الدور: ${testUser.role}`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ فشل في إنشاء المستخدم: ${error.message}`;
            }
        }

        async function testLogin() {
            const result = document.getElementById('userResult');
            result.className = 'result info';
            result.textContent = 'جاري اختبار تسجيل الدخول...';

            try {
                await userManager.init();

                // Test admin login
                const loginResult = await userManager.login('admin', 'admin123');
                
                result.className = 'result success';
                result.textContent = `✅ نجح تسجيل الدخول
المستخدم: ${loginResult.user.fullName}
الدور: ${loginResult.user.role}
البريد: ${loginResult.user.email}
معرف الجلسة: ${loginResult.session.sessionId}`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ فشل تسجيل الدخول: ${error.message}`;
            }
        }

        async function testStatistics() {
            const result = document.getElementById('statsResult');
            result.className = 'result info';
            result.textContent = 'جاري تحميل الإحصائيات...';

            try {
                // Simulate loading statistics like in main dashboard
                const dbName = 'CitizensDatabase';
                const request = indexedDB.open(dbName, 3);
                
                const stats = await new Promise((resolve, reject) => {
                    request.onsuccess = () => {
                        const db = request.result;
                        const transaction = db.transaction(['citizens', 'users'], 'readonly');
                        
                        const citizensStore = transaction.objectStore('citizens');
                        const usersStore = transaction.objectStore('users');
                        
                        const citizensRequest = citizensStore.getAll();
                        const usersRequest = usersStore.getAll();
                        
                        Promise.all([
                            new Promise(resolve => citizensRequest.onsuccess = () => resolve(citizensRequest.result)),
                            new Promise(resolve => usersRequest.onsuccess = () => resolve(usersRequest.result))
                        ]).then(([citizens, users]) => {
                            db.close();
                            resolve({ citizens, users });
                        });
                    };
                    request.onerror = () => reject(request.error);
                });

                result.className = 'result success';
                result.textContent = `✅ تم تحميل الإحصائيات بنجاح
عدد المواطنين: ${stats.citizens.length}
عدد المستخدمين: ${stats.users.length}
المستخدمين النشطين: ${stats.users.filter(u => u.isActive).length}
المديرين: ${stats.users.filter(u => u.role === 'admin').length}`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ فشل في تحميل الإحصائيات: ${error.message}`;
            }
        }

        async function testUserStats() {
            const result = document.getElementById('statsResult');
            result.className = 'result info';
            result.textContent = 'جاري تحميل إحصائيات المستخدمين...';

            try {
                await userManager.init();
                
                if (!userManager.isLoggedIn()) {
                    await userManager.login('admin', 'admin123');
                }

                const users = await userManager.getAllUsers();
                
                const stats = {
                    total: users.length,
                    active: users.filter(u => u.isActive).length,
                    inactive: users.filter(u => !u.isActive).length,
                    admins: users.filter(u => u.role === 'admin').length,
                    managers: users.filter(u => u.role === 'manager').length,
                    employees: users.filter(u => u.role === 'employee').length,
                    viewers: users.filter(u => u.role === 'viewer').length
                };

                result.className = 'result success';
                result.textContent = `✅ إحصائيات المستخدمين
إجمالي المستخدمين: ${stats.total}
النشطين: ${stats.active}
المعطلين: ${stats.inactive}
المديرين: ${stats.admins}
مديري الأقسام: ${stats.managers}
الموظفين: ${stats.employees}
المشاهدين: ${stats.viewers}`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ فشل في تحميل إحصائيات المستخدمين: ${error.message}`;
            }
        }

        async function testAuthGuard() {
            const result = document.getElementById('authResult');
            result.className = 'result info';
            result.textContent = 'جاري اختبار نظام الحماية...';

            try {
                await userManager.init();
                
                const isLoggedIn = userManager.isLoggedIn();
                const currentUser = userManager.getCurrentUser();
                const hasAdminPermission = userManager.hasPermission('manage_users');
                
                result.className = 'result success';
                result.textContent = `✅ نتائج اختبار نظام الحماية
حالة تسجيل الدخول: ${isLoggedIn ? 'مسجل دخول' : 'غير مسجل'}
المستخدم الحالي: ${currentUser?.username || 'لا يوجد'}
صلاحية إدارة المستخدمين: ${hasAdminPermission ? 'متوفرة' : 'غير متوفرة'}
الدور: ${currentUser?.role || 'لا يوجد'}`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ فشل في اختبار نظام الحماية: ${error.message}`;
            }
        }

        async function testPermissions() {
            const result = document.getElementById('authResult');
            result.className = 'result info';
            result.textContent = 'جاري اختبار الصلاحيات...';

            try {
                await userManager.init();
                
                const permissions = [
                    'manage_users',
                    'manage_data',
                    'view_reports',
                    'manage_certificates',
                    'add_data',
                    'edit_data',
                    'view_data'
                ];

                const permissionResults = permissions.map(permission => ({
                    permission,
                    hasPermission: userManager.hasPermission(permission)
                }));

                result.className = 'result success';
                result.textContent = `✅ نتائج اختبار الصلاحيات
${permissionResults.map(p => `${p.permission}: ${p.hasPermission ? '✅' : '❌'}`).join('\n')}`;

            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ فشل في اختبار الصلاحيات: ${error.message}`;
            }
        }

        function showSystemInfo() {
            const result = document.getElementById('systemResult');
            result.className = 'result info';
            
            const info = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                indexedDBSupport: !!window.indexedDB,
                localStorageSupport: !!window.localStorage,
                sessionStorageSupport: !!window.sessionStorage,
                cryptoSupport: !!window.crypto?.subtle,
                currentURL: window.location.href,
                timestamp: new Date().toLocaleString('ar-SA')
            };

            result.textContent = `ℹ️ معلومات النظام
المتصفح: ${info.userAgent}
اللغة: ${info.language}
المنصة: ${info.platform}
الكوكيز مفعلة: ${info.cookieEnabled ? 'نعم' : 'لا'}
متصل بالإنترنت: ${info.onLine ? 'نعم' : 'لا'}
دعم IndexedDB: ${info.indexedDBSupport ? 'نعم' : 'لا'}
دعم localStorage: ${info.localStorageSupport ? 'نعم' : 'لا'}
دعم sessionStorage: ${info.sessionStorageSupport ? 'نعم' : 'لا'}
دعم التشفير: ${info.cryptoSupport ? 'نعم' : 'لا'}
الرابط الحالي: ${info.currentURL}
الوقت: ${info.timestamp}`;
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 بدء اختبارات النظام التلقائية...');
            
            setTimeout(() => {
                testDatabaseConnection();
            }, 1000);
            
            setTimeout(() => {
                testUserManager();
            }, 2000);
            
            setTimeout(() => {
                showSystemInfo();
            }, 3000);
        });
    </script>
</body>
</html>
