<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الحالة المدنية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            color: #2c3e50;
            overflow: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
            z-index: 0;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 50px 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 450px;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.3);
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5em;
            color: white;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .login-title {
            font-size: 1.8em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #7f8c8d;
            font-size: 1em;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
            font-size: 0.95em;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            font-size: 1em;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            direction: rtl;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .form-input::placeholder {
            color: #bdc3c7;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #7f8c8d;
            cursor: pointer;
            font-size: 1.1em;
            padding: 5px;
            border-radius: 5px;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #667eea;
        }

        .login-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.1em;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .login-button:active {
            transform: translateY(0);
        }

        .login-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .login-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .login-button:hover::before {
            width: 300px;
            height: 300px;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
            text-align: center;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .alert-error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .alert-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
            border: 1px solid rgba(39, 174, 96, 0.3);
        }

        .alert-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 25px;
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .remember-me input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .footer-info {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #7f8c8d;
            font-size: 0.85em;
        }

        .default-credentials {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 0.9em;
            text-align: center;
        }

        .default-credentials strong {
            display: block;
            margin-bottom: 5px;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* تأثيرات متقدمة */
        .form-group {
            position: relative;
        }

        .form-group::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #667eea, transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .form-group:focus-within::before {
            opacity: 1;
        }

        /* تجاوب مع الشاشات الصغيرة */
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
                border-radius: 20px;
            }

            .login-title {
                font-size: 1.5em;
            }

            .logo {
                width: 70px;
                height: 70px;
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">🏛️</div>
            <h1 class="login-title">نظام إدارة الحالة المدنية</h1>
            <p class="login-subtitle">مكتب الحالة المدنية - أيير، إقليم أسفي</p>
        </div>

        <div id="alertContainer"></div>

        <div class="default-credentials">
            <strong>بيانات الدخول الافتراضية:</strong>
            اسم المستخدم: <strong>admin</strong><br>
            كلمة المرور: <strong>admin123</strong>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label">اسم المستخدم</label>
                <input type="text" id="username" name="username" class="form-input" 
                       placeholder="أدخل اسم المستخدم" required autocomplete="username">
            </div>

            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور</label>
                <div style="position: relative;">
                    <input type="password" id="password" name="password" class="form-input" 
                           placeholder="أدخل كلمة المرور" required autocomplete="current-password">
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        👁️
                    </button>
                </div>
            </div>

            <div class="remember-me">
                <input type="checkbox" id="rememberMe" name="rememberMe">
                <label for="rememberMe">تذكرني</label>
            </div>

            <button type="submit" class="login-button" id="loginButton">
                <span id="loginText">تسجيل الدخول</span>
                <div class="loading-spinner" id="loadingSpinner"></div>
            </button>
        </form>

        <div class="footer-info">
            <p>© 2024 مكتب الحالة المدنية - أيير</p>
            <p>جميع الحقوق محفوظة</p>
        </div>
    </div>

    <!-- تضمين نظام إدارة المستخدمين -->
    <script src="user-management.js"></script>

    <script>
        // متغيرات عامة
        let isLoading = false;

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // فحص إذا كان المستخدم مسجل دخول بالفعل
                await userManager.init();
                
                if (userManager.isLoggedIn()) {
                    showAlert('أنت مسجل دخول بالفعل، سيتم توجيهك للصفحة الرئيسية...', 'info');
                    setTimeout(() => {
                        window.location.href = 'main-dashboard.html';
                    }, 2000);
                    return;
                }

                // تركيز على حقل اسم المستخدم
                document.getElementById('username').focus();

            } catch (error) {
                console.error('خطأ في تهيئة النظام:', error);
                showAlert('خطأ في تهيئة النظام. يرجى إعادة تحميل الصفحة.', 'error');
            }
        });

        // معالج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (isLoading) return;

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // التحقق من صحة البيانات
            if (!username || !password) {
                showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }

            try {
                setLoading(true);
                
                // محاولة تسجيل الدخول
                const result = await userManager.login(username, password);
                
                if (result.success) {
                    showAlert(`مرحباً ${result.user.fullName}! سيتم توجيهك للصفحة الرئيسية...`, 'success');
                    
                    // تأخير قصير لإظهار رسالة النجاح
                    setTimeout(() => {
                        window.location.href = 'main-dashboard.html';
                    }, 1500);
                } else {
                    showAlert('فشل في تسجيل الدخول', 'error');
                }

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showAlert(error.message || 'خطأ في تسجيل الدخول', 'error');
            } finally {
                setLoading(false);
            }
        });

        // تبديل إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleButton = document.querySelector('.password-toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleButton.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleButton.textContent = '👁️';
            }
        }

        // تعيين حالة التحميل
        function setLoading(loading) {
            isLoading = loading;
            const button = document.getElementById('loginButton');
            const text = document.getElementById('loginText');
            const spinner = document.getElementById('loadingSpinner');
            
            if (loading) {
                button.disabled = true;
                text.textContent = 'جاري تسجيل الدخول...';
                spinner.style.display = 'inline-block';
            } else {
                button.disabled = false;
                text.textContent = 'تسجيل الدخول';
                spinner.style.display = 'none';
            }
        }

        // عرض التنبيهات
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alertContainer');
            
            // إزالة التنبيهات السابقة
            container.innerHTML = '';
            
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            container.appendChild(alert);
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // معالجة الضغط على Enter
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isLoading) {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });

        // ملء البيانات الافتراضية عند النقر على التنبيه
        document.querySelector('.default-credentials').addEventListener('click', function() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
