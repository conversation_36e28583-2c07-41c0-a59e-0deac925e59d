/* Test Environment Styles for Certificate Management System */

* {
    font-family: 'Cairo', sans-serif;
}

body {
    background-color: #f8f9fa;
    direction: rtl;
}

/* Test Environment Specific Styles */
.alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border: none;
    font-weight: 600;
}

.badge.bg-warning {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Header Styles */
header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Statistics Cards */
.stat-card {
    border: none;
    border-radius: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Function Cards */
.function-card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    overflow: hidden;
}

.function-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.function-card:hover::before {
    opacity: 1;
}

.function-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.function-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.function-card:hover .function-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Test Controls Card */
.card.border-warning {
    border-width: 2px !important;
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2);
}

/* Button Styles */
.btn {
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Test Environment Button Styles */
.btn-outline-danger:hover {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-color: #dc3545;
}

.btn-outline-info:hover {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-color: #17a2b8;
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #28a745 0%, #218838 100%);
    border-color: #28a745;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: 1px solid #e9ecef;
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Test Data Animation */
@keyframes testDataGenerate {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); background-color: #28a745; }
    100% { transform: scale(1); }
}

.generating-data {
    animation: testDataGenerate 0.5s ease-in-out;
}

/* Error Handling Styles */
.error-message {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.success-message {
    background: linear-gradient(135deg, #28a745 0%, #218838 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .function-card {
        margin-bottom: 20px;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    .function-icon {
        width: 60px;
        height: 60px;
    }
    
    .function-icon i {
        font-size: 2rem !important;
    }
    
    .badge.bg-warning {
        font-size: 0.7rem;
    }
}

/* Custom Colors with Test Environment Theme */
.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
}

.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

.bg-dark {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%) !important;
}

/* Loading Animation for Test Environment */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,193,7,.3);
    border-radius: 50%;
    border-top-color: #ffc107;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Test Environment Indicators */
.test-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: #ffc107;
    color: #212529;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 1000;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #ffc107;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #e0a800;
}
