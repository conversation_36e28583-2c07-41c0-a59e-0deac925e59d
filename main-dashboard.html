<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الحالة المدنية - أيير</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a 0%, #e74c3c 50%, #c41e3a 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.2);
            padding: 10px 0;
            font-size: 0.9em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 25px 0;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            border: 3px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .header-text h1 {
            font-size: 2em;
            margin: 0 0 8px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 5px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        .header-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            font-size: 0.9em;
        }

        .current-time {
            background: rgba(255,255,255,0.15);
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .user-info {
            background: rgba(255,255,255,0.1);
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        /* قسم أزرار الانتقال */
        .header-navigation-section {
            background: rgba(0,0,0,0.15);
            border-top: 1px solid rgba(255,255,255,0.1);
            padding: 15px 0;
        }

        .header-navigation {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 25px;
            flex-wrap: wrap;
            max-width: 800px;
            margin: 0 auto;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 14px 24px;
            border: none;
            border-radius: 35px;
            text-decoration: none;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            min-width: 160px;
            justify-content: center;
            text-align: center;
            flex: 1;
            max-width: 180px;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.35);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.25);
            border-color: rgba(255,255,255,0.5);
        }

        .nav-btn:active {
            transform: translateY(-1px);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-color: rgba(231, 76, 60, 1);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
            transform: translateY(-2px);
        }

        .nav-btn.active:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.5);
        }

        .nav-btn .icon {
            font-size: 1.1em;
        }

        .content {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .main-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.8);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e74c3c, #3498db, #2ecc71);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .action-card:hover::before {
            transform: scaleX(1);
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .action-icon {
            font-size: 3em;
            margin-bottom: 15px;
            color: #e74c3c;
            display: block;
            text-align: center;
            transition: all 0.3s ease;
            text-shadow: 2px 2px 6px rgba(231, 76, 60, 0.4), 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .action-card:hover .action-icon {
            transform: scale(1.1);
            text-shadow: 3px 3px 8px rgba(231, 76, 60, 0.5), 2px 2px 4px rgba(0, 0, 0, 0.4);
            color: #c0392b;
        }

        .action-title {
            font-size: 1.3em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 12px;
            text-align: center;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
        }

        .action-description {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.5;
            font-size: 0.95em;
            text-align: center;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 14px 20px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1em;
            text-align: center;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
            background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
        }

        .stats-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.8);
        }

        .stats-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
            font-weight: 700;
            text-align: center;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            position: relative;
        }

        .stats-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 2px;
        }

        .stat-item {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            padding: 18px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 15px;
            border-left: 5px solid #e74c3c;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .stat-item:hover::before {
            left: 100%;
        }

        .stat-item:hover {
            transform: translateX(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.2em;
            font-weight: 800;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            text-shadow: 1px 1px 2px rgba(231, 76, 60, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(231, 76, 60, 0.3));
        }

        .stat-label {
            color: #34495e;
            font-weight: 600;
            font-size: 0.9em;
        }

        .secondary-tools {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        @media (max-width: 1200px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 1.6em;
            }

            .header-navigation {
                gap: 10px;
                padding: 0 10px;
            }

            .nav-btn {
                padding: 10px 15px;
                font-size: 0.85em;
                min-width: 120px;
                flex: 1;
                max-width: 150px;
            }

            .nav-btn .icon {
                font-size: 1em;
            }

            .content {
                padding: 20px 15px;
            }

            .main-actions {
                grid-template-columns: 1fr;
            }

            .action-card {
                padding: 20px;
            }

            .morocco-emblem {
                width: 60px;
                height: 60px;
                font-size: 1.8em;
            }


        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">مكتب الحالة المدنية - أيير</div>
                            <div class="department">قسم التسجيل والتوثيق</div>
                        </div>
                    </div>
                    <div class="header-left">
                        <div class="current-time" id="currentTime"></div>
                        <div class="user-info">👨‍💼 موظف الحالة المدنية</div>
                    </div>
                </div>
            </div>

            <!-- قسم أزرار الانتقال -->
            <div class="header-navigation-section">
                <div class="header-content">
                    <div class="header-navigation">
                        <a href="main-dashboard.html" class="nav-btn active">
                            <span class="icon">🏠</span>
                            <span>الرئيسية</span>
                        </a>
                        <a href="citizens-database-indexeddb.html" class="nav-btn">
                            <span class="icon">🗃️</span>
                            <span>إدارة البيانات</span>
                        </a>
                        <a href="search-citizens.html" class="nav-btn">
                            <span class="icon">🔍</span>
                            <span>البحث</span>
                        </a>
                        <a href="death-data-entry.html" class="nav-btn">
                            <span class="icon">⚱️</span>
                            <span>بيانات الوفاة</span>
                        </a>
                         <a href="personal-id-form.html" class="nav-btn">
                            <span class="icon">🆔</span>
                            <span>البطاقة الشخصية</span>
                        </a>
                        <a href="personal-id-card.html" class="nav-btn">
                            <span class="icon">📇</span>
                            <span>بطاقة التعريف</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <div class="dashboard-grid">
                <!-- Main Actions -->
                <div class="main-actions">
                    <!-- Citizens Database -->
                    <div class="action-card">
                        <div class="action-icon">🗃️</div>
                        <div class="action-title">إدارة بيانات المواطنين</div>
                        <div class="action-description">
                            تسجيل مواطنين جدد، تعديل البيانات الموجودة، وإدارة الشهادات الكاملة بنظام IndexedDB المتطور
                        </div>
                        <a href="citizens-database-indexeddb.html" class="btn">📝 إدارة البيانات</a>
                    </div>

                    <!-- Search Citizens -->
                    <div class="action-card">
                        <div class="action-icon">🔍</div>
                        <div class="action-title">البحث في السجلات</div>
                        <div class="action-description">
                            البحث السريع في سجلات المواطنين وطباعة عقود الازدياد والشهادات الكاملة
                        </div>
                        <a href="search-citizens.html" class="btn">🔎 البحث والطباعة</a>
                    </div>

                    <!-- Birth Certificate Template -->
                    <div class="action-card">
                        <div class="action-icon">📜</div>
                        <div class="action-title">نموذج عقد الازدياد</div>
                        <div class="action-description">
                            معاينة وطباعة نموذج عقد الازدياد الرسمي باللغتين العربية والفرنسية وفق المعايير المغربية
                        </div>
                        <a href="dual-birth-certificate.html" class="btn btn-secondary">📋 معاينة النموذج</a>
                    </div>

                    <!-- Death Certificate -->
                    <div class="action-card">
                        <div class="action-icon">⚱️</div>
                        <div class="action-title">نسخة موجزة من رسم الوفاة</div>
                        <div class="action-description">
                            إدخال بيانات المتوفى وإصدار نسخة موجزة من رسم الوفاة وفق النموذج الرسمي المغربي
                        </div>
                        <a href="death-data-entry.html" class="btn">📄 إدخال بيانات الوفاة</a>
                        <a href="death-certificate.html" class="btn btn-secondary">📋 نسخة موجزة من رسم الوفاة</a>
                    </div>

                </div>

                <!-- Statistics Panel -->
                <div class="stats-panel">
                    <div class="stats-title">📈 إحصائيات النظام</div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalCitizens">0</div>
                        <div class="stat-label">إجمالي المواطنين المسجلين</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="todayRegistrations">0</div>
                        <div class="stat-label">تسجيلات اليوم</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="thisMonthRegistrations">0</div>
                        <div class="stat-label">تسجيلات هذا الشهر</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="thisYearRegistrations">0</div>
                        <div class="stat-label">تسجيلات هذا العام</div>
                    </div>

                    <!-- Refresh Button -->
                    <div style="text-align: center; margin-top: 15px;">
                        <button onclick="loadStatistics()" class="btn btn-success" style="padding: 8px 16px; font-size: 0.9em;">
                            🔄 تحديث الإحصائيات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Secondary Tools -->
            <div class="secondary-tools">
                <div class="action-card">
                    <div class="action-icon" style="filter: drop-shadow(2px 4px 8px rgba(52, 152, 219, 0.3)); text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);">🔄</div>
                    <div class="action-title">نقل البيانات بين الأجهزة</div>
                    <div class="action-description">
                        نظام متقدم لنقل ومزامنة البيانات بأمان بين الحواسيب المختلفة
                    </div>
                    <a href="data-transfer.html" class="btn">🔄 نقل البيانات</a>
                </div>

                <div class="action-card">
                    <div class="action-icon" style="filter: drop-shadow(2px 4px 8px rgba(46, 204, 113, 0.3)); text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);">🛡️</div>
                    <div class="action-title">إدارة النسخ الاحتياطية</div>
                    <div class="action-description">
                        دليل شامل لحماية البيانات وإنشاء النسخ الاحتياطية الآمنة
                    </div>
                    <a href="backup-guide.html" class="btn">📁 دليل النسخ الاحتياطي</a>
                </div>

                <div class="action-card">
                    <div class="action-icon" style="filter: drop-shadow(2px 4px 8px rgba(243, 156, 18, 0.3)); text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);">⚙️</div>
                    <div class="action-title">اختبار الأداء</div>
                    <div class="action-description">
                        مولد بيانات تجريبية لاختبار أداء النظام مع أعداد كبيرة
                    </div>
                    <a href="data-generator.html" class="btn">🔧 مولد البيانات</a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer style="background: #2c3e50; color: white; text-align: center; padding: 20px; font-size: 0.9em;">
            <p style="margin: 0;">© 2024 مكتب الحالة المدنية - أيير، إقليم أسفي</p>
        </footer>
    </div>

    <!-- Include Enhanced Database Managers -->
    <script src="cross-platform-manager.js"></script>
    <script src="data-sync-manager.js"></script>
    <script src="indexeddb-manager.js"></script>

    <script>
        // Enhanced Dashboard Manager with Cross-Platform Support
        class DashboardManager {
            constructor() {
                this.isInitialized = false;
                this.systemStatus = null;
            }

            async init() {
                try {
                    console.log('🚀 تهيئة لوحة التحكم المحسنة...');

                    // تهيئة مدير التوافق عبر المنصات
                    await crossPlatformManager.init();

                    // تهيئة قاعدة البيانات
                    await citizensDB.init();

                    // تهيئة مدير المزامنة
                    await dataSyncManager.init();

                    // تحديث الإحصائيات
                    await this.updateStatistics();

                    // عرض حالة النظام
                    this.displaySystemStatus();

                    this.isInitialized = true;
                    console.log('✅ تم تهيئة لوحة التحكم بنجاح');

                } catch (error) {
                    console.error('خطأ في تهيئة لوحة التحكم:', error);
                    this.showErrorMessage('خطأ في تهيئة النظام، سيتم استخدام الوضع الآمن');
                }
            }

            async updateStatistics() {
                try {
                    const citizens = await citizensDB.getAllCitizens();
                    const today = new Date().toISOString().split('T')[0];
                    const thisMonth = new Date().toISOString().substring(0, 7);
                    const thisYear = new Date().getFullYear().toString();

                    // إحصائيات عامة
                    document.getElementById('totalCitizens').textContent = citizens.length.toLocaleString('ar-MA');

                    // تسجيلات اليوم
                    const todayCount = citizens.filter(c =>
                        c.createdAt && c.createdAt.startsWith(today)
                    ).length;
                    document.getElementById('todayRegistrations').textContent = todayCount.toLocaleString('ar-MA');

                    // تسجيلات هذا الشهر
                    const monthCount = citizens.filter(c =>
                        c.createdAt && c.createdAt.startsWith(thisMonth)
                    ).length;
                    document.getElementById('thisMonthRegistrations').textContent = monthCount.toLocaleString('ar-MA');

                    // تسجيلات هذا العام
                    const yearCount = citizens.filter(c =>
                        c.createdAt && c.createdAt.startsWith(thisYear)
                    ).length;
                    document.getElementById('thisYearRegistrations').textContent = yearCount.toLocaleString('ar-MA');

                } catch (error) {
                    console.error('خطأ في تحديث الإحصائيات:', error);
                    // عرض قيم افتراضية في حالة الخطأ
                    document.getElementById('totalCitizens').textContent = '0';
                    document.getElementById('todayRegistrations').textContent = '0';
                    document.getElementById('thisMonthRegistrations').textContent = '0';
                    document.getElementById('thisYearRegistrations').textContent = '0';
                }
            }

            displaySystemStatus() {
                const status = crossPlatformManager.getSystemStatus();

                // إضافة مؤشر حالة النظام
                const statusIndicator = document.createElement('div');
                statusIndicator.className = 'system-status';
                statusIndicator.innerHTML = `
                    <div style="position: fixed; bottom: 20px; left: 20px; background: white; padding: 15px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); font-size: 0.9em; z-index: 1000;">
                        <div style="color: #28a745; font-weight: bold; margin-bottom: 5px;">
                            ✅ النظام يعمل بشكل طبيعي
                        </div>
                        <div style="color: #6c757d;">
                            نوع التخزين: ${status.storageType === 'indexedDB' ? 'IndexedDB' : 'التخزين المحلي'}
                        </div>
                        <div style="color: #6c757d;">
                            المنصة: ${status.capabilities.indexedDB ? 'متقدمة' : 'أساسية'}
                        </div>
                    </div>
                `;
                document.body.appendChild(statusIndicator);

                // إخفاء المؤشر بعد 5 ثوان
                setTimeout(() => {
                    statusIndicator.style.opacity = '0';
                    setTimeout(() => statusIndicator.remove(), 300);
                }, 5000);
            }

            showErrorMessage(message) {
                const errorDiv = document.createElement('div');
                errorDiv.innerHTML = `
                    <div style="position: fixed; top: 20px; right: 20px; background: #f8d7da; color: #721c24; padding: 15px; border-radius: 10px; border: 1px solid #f5c6cb; z-index: 1000; max-width: 300px;">
                        ⚠️ ${message}
                    </div>
                `;
                document.body.appendChild(errorDiv);

                setTimeout(() => {
                    errorDiv.style.opacity = '0';
                    setTimeout(() => errorDiv.remove(), 300);
                }, 8000);
            }
        }

        // إنشاء مثيل مدير لوحة التحكم
        const dashboardManager = new DashboardManager();

        // Database class for dashboard
        class DashboardDB {
            constructor() {
                this.db = null;
                this.dbName = 'CitizensDatabase'; // تطابق مع indexeddb-manager.js
                this.storeName = 'citizens';
                this.version = 2;
            }

            async init() {
                // Initialize database connection
                return new Promise((resolve, reject) => {
                    const request = indexedDB.open(this.dbName, this.version);
                    request.onsuccess = () => {
                        this.db = request.result;
                        console.log(`تم الاتصال بقاعدة البيانات: ${this.dbName}`);
                        resolve();
                    };
                    request.onerror = () => {
                        console.error('خطأ في فتح قاعدة البيانات:', request.error);
                        reject(request.error);
                    };
                    request.onupgradeneeded = (event) => {
                        const db = event.target.result;
                        console.log('تحديث قاعدة البيانات...');

                        // إنشاء مخزن المواطنين إذا لم يكن موجوداً
                        if (!db.objectStoreNames.contains(this.storeName)) {
                            const store = db.createObjectStore(this.storeName, { keyPath: 'id' });
                            // إنشاء فهارس للبحث السريع
                            store.createIndex('firstNameAr', 'firstNameAr', { unique: false });
                            store.createIndex('familyNameAr', 'familyNameAr', { unique: false });
                            store.createIndex('createdAt', 'createdAt', { unique: false });
                            console.log('تم إنشاء مخزن المواطنين');
                        }
                    };
                });
            }

            async getAllCitizens() {
                try {
                    if (!this.db) await this.init();
                    return new Promise((resolve, reject) => {
                        const transaction = this.db.transaction([this.storeName], 'readonly');
                        const store = transaction.objectStore(this.storeName);
                        const request = store.getAll();
                        request.onsuccess = () => resolve(request.result || []);
                        request.onerror = () => reject(request.error);
                    });
                } catch (error) {
                    console.error('خطأ في الوصول لقاعدة البيانات:', error);
                    return [];
                }
            }
        }

        const dashboardDB = new DashboardDB();

        // Load statistics from IndexedDB or localStorage
        async function loadStatistics() {
            try {
                console.log('🔄 بدء تحميل الإحصائيات...');

                // Try IndexedDB first
                const citizens = await dashboardDB.getAllCitizens();
                console.log(`📊 تم تحميل ${citizens.length} مواطن من IndexedDB`);

                // Debug: show sample data structure
                if (citizens.length > 0) {
                    console.log('📋 عينة من البيانات:', {
                        id: citizens[0].id,
                        firstNameAr: citizens[0].firstNameAr,
                        personalName: citizens[0].personalName,
                        createdAt: citizens[0].createdAt,
                        registrationDate: citizens[0].registrationDate,
                        timestamp: citizens[0].timestamp,
                        isDeceased: citizens[0].isDeceased
                    });
                }

                if (citizens.length > 0) {
                    updateStatisticsDisplay(citizens);
                    return;
                }

                // If IndexedDB is empty, try localStorage with multiple possible keys
                console.log('📂 IndexedDB فارغ، محاولة استخدام localStorage...');
                const possibleKeys = ['citizens', 'citizensData', 'CitizensDatabase', 'citizensDB'];
                let localCitizens = [];

                for (const key of possibleKeys) {
                    try {
                        const data = localStorage.getItem(key);
                        if (data) {
                            const parsed = JSON.parse(data);
                            if (Array.isArray(parsed) && parsed.length > 0) {
                                localCitizens = parsed;
                                console.log(`📦 تم العثور على ${localCitizens.length} مواطن في localStorage[${key}]`);
                                break;
                            }
                        }
                    } catch (e) {
                        console.warn(`⚠️ خطأ في قراءة ${key} من localStorage:`, e);
                    }
                }

                console.log(`📊 تم تحميل ${localCitizens.length} مواطن من localStorage`);
                updateStatisticsDisplay(localCitizens);

            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات من IndexedDB:', error);
                // Fallback to localStorage
                try {
                    const localCitizens = JSON.parse(localStorage.getItem('citizens') || localStorage.getItem('citizensData') || '[]');
                    console.log(`🔄 تم تحميل ${localCitizens.length} مواطن من localStorage كبديل`);
                    updateStatisticsDisplay(localCitizens);
                } catch (localError) {
                    console.error('❌ خطأ في تحميل البيانات من localStorage:', localError);
                    // Show zeros if all fails
                    updateStatisticsDisplay([]);
                }
            }
        }

        // Update statistics display
        function updateStatisticsDisplay(citizens) {
            const today = new Date().toISOString().split('T')[0];
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();

            // Total citizens
            document.getElementById('totalCitizens').textContent = citizens.length.toLocaleString('ar-MA');

            // Today's registrations - check multiple possible date fields
            const todayRegistrations = citizens.filter(citizen => {
                const createdAt = citizen.createdAt || citizen.registrationDate || citizen.timestamp;
                if (!createdAt) return false;

                // Handle different date formats
                let dateStr = '';
                if (typeof createdAt === 'string') {
                    dateStr = createdAt.split('T')[0]; // ISO format
                } else if (createdAt instanceof Date) {
                    dateStr = createdAt.toISOString().split('T')[0];
                } else {
                    return false;
                }

                return dateStr === today;
            }).length;
            document.getElementById('todayRegistrations').textContent = todayRegistrations.toLocaleString('ar-MA');

            // This month's registrations
            const thisMonthRegistrations = citizens.filter(citizen => {
                const createdAt = citizen.createdAt || citizen.registrationDate || citizen.timestamp;
                if (!createdAt) return false;

                let regDate;
                if (typeof createdAt === 'string') {
                    regDate = new Date(createdAt);
                } else if (createdAt instanceof Date) {
                    regDate = createdAt;
                } else {
                    return false;
                }

                return regDate.getMonth() === currentMonth && regDate.getFullYear() === currentYear;
            }).length;
            document.getElementById('thisMonthRegistrations').textContent = thisMonthRegistrations.toLocaleString('ar-MA');

            // This year's registrations
            const thisYearRegistrations = citizens.filter(citizen => {
                const createdAt = citizen.createdAt || citizen.registrationDate || citizen.timestamp;
                if (!createdAt) return false;

                let regDate;
                if (typeof createdAt === 'string') {
                    regDate = new Date(createdAt);
                } else if (createdAt instanceof Date) {
                    regDate = createdAt;
                } else {
                    return false;
                }

                return regDate.getFullYear() === currentYear;
            }).length;
            document.getElementById('thisYearRegistrations').textContent = thisYearRegistrations.toLocaleString('ar-MA');

            console.log('تم تحديث الإحصائيات:', {
                total: citizens.length,
                today: todayRegistrations,
                thisMonth: thisMonthRegistrations,
                thisYear: thisYearRegistrations
            });

            // Show success message
            showUpdateMessage(`تم تحديث الإحصائيات بنجاح - إجمالي ${citizens.length} مواطن`);
        }

        // Show update message
        function showUpdateMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = `
                <div style="position: fixed; top: 20px; right: 20px; background: #d4edda; color: #155724; padding: 15px; border-radius: 10px; border: 1px solid #c3e6cb; z-index: 1000; max-width: 300px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    ✅ ${message}
                </div>
            `;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.style.opacity = '0';
                setTimeout(() => messageDiv.remove(), 300);
            }, 3000);
        }

        // Update current time
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-MA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // Check and fix database conflicts
        async function checkDatabaseConflicts() {
            console.log('🔍 فحص تضارب قواعد البيانات...');

            const possibleDBNames = ['CitizensDatabase', 'CivilRegistryDB', 'citizens-db'];
            const dbInfo = [];

            for (const dbName of possibleDBNames) {
                try {
                    const request = indexedDB.open(dbName);
                    const result = await new Promise((resolve) => {
                        request.onsuccess = () => {
                            const db = request.result;
                            const stores = Array.from(db.objectStoreNames);

                            if (stores.includes('citizens')) {
                                const transaction = db.transaction(['citizens'], 'readonly');
                                const store = transaction.objectStore('citizens');
                                const countRequest = store.count();

                                countRequest.onsuccess = () => {
                                    const count = countRequest.result;
                                    db.close();
                                    resolve({ name: dbName, count: count, stores: stores });
                                };

                                countRequest.onerror = () => {
                                    db.close();
                                    resolve({ name: dbName, count: 0, stores: stores });
                                };
                            } else {
                                db.close();
                                resolve({ name: dbName, count: 0, stores: stores });
                            }
                        };

                        request.onerror = () => resolve(null);
                    });

                    if (result) {
                        dbInfo.push(result);
                        console.log(`📊 ${result.name}: ${result.count} سجل`);
                    }
                } catch (error) {
                    console.warn(`⚠️ خطأ في فحص ${dbName}:`, error);
                }
            }

            // Find the database with most records
            const mainDB = dbInfo.reduce((max, current) =>
                current.count > max.count ? current : max,
                { name: 'CitizensDatabase', count: 0 }
            );

            console.log(`🎯 قاعدة البيانات الرئيسية: ${mainDB.name} (${mainDB.count} سجل)`);
            return mainDB;
        }

        // تفعيل الزر النشط في أزرار الانتقال
        function setActiveNavButton() {
            const currentPage = window.location.pathname.split('/').pop() || 'main-dashboard.html';
            const navButtons = document.querySelectorAll('.nav-btn');

            navButtons.forEach(btn => {
                btn.classList.remove('active');
                const href = btn.getAttribute('href');
                if (href === currentPage || (currentPage === '' && href === 'main-dashboard.html')) {
                    btn.classList.add('active');
                }
            });
        }

        // Load statistics when page loads
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // تفعيل الزر النشط
                setActiveNavButton();

                // Check for database conflicts first
                await checkDatabaseConflicts();

                // Load statistics
                await loadStatistics();
                updateCurrentTime();

                // Update time every minute
                setInterval(updateCurrentTime, 60000);

                console.log('✅ تم تهيئة لوحة التحكم بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تهيئة لوحة التحكم:', error);
                // Still try to load basic functionality
                setActiveNavButton();
                updateCurrentTime();
                setInterval(updateCurrentTime, 60000);
            }
        });

        // Refresh statistics every 30 seconds
        setInterval(loadStatistics, 30000);

        // Refresh when page becomes visible
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                loadStatistics();
                updateCurrentTime();
            }
        });
    </script>
</body>
</html>
