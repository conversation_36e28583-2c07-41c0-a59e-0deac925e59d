<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="بطاقة شخصية للحالة المدنية - المملكة المغربية">
    <meta name="author" content="مكتب الحالة المدنية - أيير">
    <title>بطاقة شخصية للحالة المدنية - مكتب أيير</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🇲🇦</text></svg>">

    <!-- الأنماط المحسنة للبطاقة الشخصية -->
    <style>
        /* ===== إعدادات عامة ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* ألوان المملكة المغربية */
            --morocco-red: #C1272D;
            --morocco-green: #006233;
            --morocco-gold: #FFD700;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --border-color: #34495e;
            --background-light: #f8f9fa;
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);

            /* خطوط */
            --font-primary: 'Times New Roman', 'Amiri', serif;
            --font-secondary: 'Arial', 'Tahoma', sans-serif;

            /* أحجام نصف ورقة A4 (أفقي) */
            --card-width: 210mm;  /* عرض ورقة A4 كامل */
            --card-height: 148.5mm; /* نصف ارتفاع ورقة A4 */
            --border-radius: 8px;
            --spacing-xs: 2mm;
            --spacing-sm: 4mm;
            --spacing-md: 6mm;
            --spacing-lg: 8mm;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, var(--background-light) 0%, #e8f4f8 100%);
            min-height: 100vh;
            padding: var(--spacing-md);
            color: var(--text-dark);
            line-height: 1.4;
        }

        /* ===== حاوي التطبيق الرئيسي ===== */
        #app {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* ===== حاوي الورقة الموحدة ===== */
        .document {
            width: var(--card-width);
            height: var(--card-height);
            margin: 0 auto;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px var(--shadow-medium);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* ===== أنماط الهيدر ===== */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: var(--spacing-md);  /* إرجاع الحشو الأصلي */
            font-size: 11px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .header-left {
            flex: 1;
            text-align: right;
            direction: rtl;
            padding-right: 0;
            margin-right: 0;
        }

        .header-center {
            flex: 1;
            text-align: center;
            margin: 0 var(--spacing-sm);
        }

        .header-right {
            flex: 1;
            text-align: left;
            direction: ltr;
            padding-left: 0;
            margin-left: 0;
        }

        /* ===== أنماط قسم المعلومات الشخصية ===== */
        .personal-info {
            padding: 2mm;        /* حشو أقل جداً */
            flex: 1;             /* يملأ المساحة المتبقية */
            margin-top: -5mm;    /* رفع قسم المعلومات بـ 5mm (0.5 سنتيمتر) */
        }

        .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 2mm;    /* زيادة المسافة */
            padding: 1mm 0;        /* إضافة حشو */
            direction: rtl;
            text-align: right;
            line-height: 1.3;      /* زيادة تباعد الأسطر */
            height: auto;          /* ارتفاع تلقائي */
        }

        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 800;          /* زيادة سمك الخط للتسميات */
            color: var(--text-dark);
            font-size: 10px;           /* تقليص من 12px */
            margin-left: 1mm;          /* تقليص الهامش */
        }

        /* إضافة فراغ بين التسمية والخط */
        .info-row {
            gap: 2mm;
        }

        .info-row .info-label {
            min-width: auto;
            margin-left: 0;
        }

        .info-value {
            flex: 1;
            padding: 0;                      /* إزالة الحشو نهائياً */
            border-bottom: 1px dotted #333;
            min-height: 6px;                 /* ارتفاع أقل جداً */
            font-weight: 700;                /* زيادة سمك الخط العربي */
            color: var(--text-dark);
            background: transparent;
            font-size: 9px;                  /* حجم خط أصغر جداً */
            line-height: 0.6;               /* تباعد أقل */
        }

        .french-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 9px;                  /* تقليص من 11px */
            margin-right: 1mm;               /* تقليص الهامش */
            direction: ltr;
            text-align: left;
        }

        .hijri-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 9px;                  /* تقليص من 11px */
            margin-left: 1mm;                /* تقليص الهامش */
            direction: rtl;
            text-align: right;
        }

        .location-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 11px;
            margin-left: var(--spacing-xs);
            direction: rtl;
            text-align: right;
        }

        /* قص خط قسم ساكن حاليا ب */
        #currentAddress {
            width: calc(50% - 15mm);  /* العرض الأصلي - 15mm (قص إجمالي 5.5 سنتيمتر) */
            flex: none;
        }

        /* ===== أنماط البيانات الهامشية ===== */
        .marginal-x-marks {
            font-family: monospace;
            letter-spacing: 1px;
            font-weight: 500;
            color: var(--text-dark);
            margin-right: 2mm;
        }

        #marginalData {
            flex: 1;
            padding: var(--spacing-xs);
            border-bottom: 1px dotted #333;  /* تغيير الخط إلى نقاط */
            min-height: 20px;
            font-weight: 500;
            color: var(--text-dark);
            background: transparent;
        }

        .marginal-row {
            padding-bottom: var(--spacing-xs);
            margin-bottom: var(--spacing-sm);
        }

        /* ===== تقسيم الجزء الفارغ ===== */
        .bottom-section {
            display: flex;
            width: 210mm;           /* عرض محدد */
            height: 40mm;           /* ارتفاع محدد */
            position: relative;
        }

        .left-section {
            width: 105mm;           /* نصف العرض بالضبط */
            height: 40mm;
            padding: var(--spacing-xs);
            box-sizing: border-box;
        }

        .right-section {
            width: 105mm;           /* نصف العرض بالضبط */
            height: 40mm;
            padding: var(--spacing-xs);
            box-sizing: border-box;
        }

        /* خط عمودي في الوسط تماماً */
        .bottom-section::after {
            content: '';
            position: absolute;
            left: 105mm;            /* في الوسط تماماً */
            top: 0;
            width: 1px;
            height: 100%;
            background: #333;
        }

        /* خط فاصل بنفس لون الخط العمودي */
        hr {
            border: none;
            height: 1px;
            background: #333;
            margin: var(--spacing-xs) 0;
        }

        /* ===== أنماط الجزء الأيسر ===== */
        .witness-text {
            font-size: 11px;
            line-height: 1.4;
            margin-bottom: var(--spacing-sm);
            text-align: justify;
            color: var(--text-dark);
        }

        .witness-label {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 10px;
            margin-left: var(--spacing-xs);
        }

        .witness-value {
            flex: 0.5;
            padding: 1px;
            border-bottom: 1px dotted #333;
            min-height: 16px;
            font-weight: 500;
            color: var(--text-dark);
            background: transparent;
            margin-left: 2px;
        }

        .signature-text {
            font-size: 11px;
            font-weight: 600;
            text-align: center;
            margin-top: var(--spacing-sm);
            color: var(--text-dark);
        }

        .witness-paragraph {
            font-size: 10px;       /* تقليص الحجم */
            line-height: 1.1;      /* تقليص تباعد الأسطر */
            text-align: justify;
            color: var(--text-dark);
            margin-bottom: 0;
            margin-top: 0;         /* إزالة المسافة العلوية */
            font-weight: 600;
        }

        /* تقليص قليل للفقرة القانونية */
        .right-section .witness-paragraph:last-of-type {
            font-size: 8px;        /* تقليص من 9px إلى 8px */
            font-weight: 500;
            line-height: 1.1;      /* تقليص من 1.2 إلى 1.1 */
            margin-bottom: 1mm;     /* تقليص من 2mm إلى 1mm */
            margin-top: 1mm;       /* تقليص من 2mm إلى 1mm */
        }

        .inline-field {
            display: inline-block;
            min-width: 25mm;
            border-bottom: 1px dotted #333;
            height: 16px;
            margin: 0 2px;
        }

        /* تقليص المسافات قليلاً في الجزء الأيمن */
        .right-section .info-row,
        .right-section .witness-paragraph,
        .right-section .witness-label {
            margin-bottom: 1mm;    /* تقليص من 2mm إلى 1mm */
            padding: 0.5mm 0;      /* تقليص من 1mm إلى 0.5mm */
            line-height: 1.2;      /* تقليص من 1.3 إلى 1.2 */
        }



        .date-signature-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: var(--spacing-sm);
        }

        .date-section {
            display: flex;
            align-items: center;
        }

        .signature-section-left {
            font-size: 11px;
            font-weight: 600;
            color: var(--text-dark);
        }

        /* ===== أنماط أزرار التحكم ===== */
        .control-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .print-btn, .home-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .print-btn {
            background: var(--morocco-green);
            color: white;
        }

        .print-btn:hover {
            background: #004d2a;
            transform: translateY(-2px);
        }

        .home-btn {
            background: var(--morocco-red);
            color: white;
        }

        .home-btn:hover {
            background: #a01e23;
            transform: translateY(-2px);
        }

        /* إخفاء عناصر عند الطباعة */
        @media print {
            .control-buttons {
                display: none;
            }

            /* إزالة هوامش وعناوين الطباعة */
            @page {
                margin: 0;
                size: A4;
                /* إخفاء header و footer المتصفح */
                @top-left { content: ""; }
                @top-center { content: ""; }
                @top-right { content: ""; }
                @bottom-left { content: ""; }
                @bottom-center { content: ""; }
                @bottom-right { content: ""; }
            }

            body {
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            /* إخفاء عنوان الصفحة والوقت */
            html {
                -webkit-print-color-adjust: exact;
            }
        }

        /* ===== أنماط البطاقة الرئيسية ===== */
        .id-card {
            width: var(--card-width);
            height: var(--card-height);
            margin: 0 auto;
            background: white;
            border-radius: var(--border-radius);
            box-shadow:
                0 10px 30px var(--shadow-medium),
                0 0 0 1px var(--border-color);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* ===== ترويسة البطاقة ===== */
        .card-header {
            background: linear-gradient(135deg, var(--morocco-red) 0%, #a91e22 100%);
            color: white;
            padding: var(--spacing-sm);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255, 255, 255, 0.05) 10px,
                rgba(255, 255, 255, 0.05) 20px
            );
            animation: shimmer 20s linear infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .kingdom-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: var(--spacing-xs);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .ministry-info {
            font-size: 11px;
            opacity: 0.9;
            line-height: 1.3;
        }

        /* ===== محتوى البطاقة ===== */
        .card-content {
            flex: 1;
            padding: var(--spacing-md);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        /* ===== أنماط الحقول ===== */
        .field-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-xs) 0;
            border-bottom: 1px solid #ecf0f1;
            transition: all 0.3s ease;
        }

        .field-row:hover {
            background: rgba(52, 152, 219, 0.05);
            border-radius: 4px;
            padding-left: var(--spacing-xs);
            padding-right: var(--spacing-xs);
        }

        .field-arabic, .field-french {
            flex: 1;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .field-arabic {
            direction: rtl;
            text-align: right;
        }

        .field-french {
            direction: ltr;
            text-align: left;
        }

        .field-label {
            font-weight: 600;
            color: var(--text-dark);
            min-width: 80px;
            font-size: 11px;
        }

        .field-value {
            flex: 1;
            padding: 2px var(--spacing-xs);
            border-bottom: 2px solid var(--morocco-green);
            min-height: 20px;
            font-weight: 700;
            color: var(--text-dark);
            background: rgba(0, 98, 51, 0.05);
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .field-value:hover {
            background: rgba(0, 98, 51, 0.1);
            transform: translateY(-1px);
        }

        /* ===== أزرار التحكم ===== */
        .control-buttons {
            text-align: center;
            margin-top: var(--spacing-lg);
            padding: var(--spacing-md);
            background: var(--background-light);
            border-radius: var(--border-radius);
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 var(--spacing-xs);
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: all 0.6s ease;
            transform: translate(-50%, -50%);
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--morocco-red) 0%, #a91e22 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(193, 39, 45, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--morocco-green) 0%, #004d28 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 98, 51, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* ===== أنماط الطباعة لنصف ورقة A4 ===== */
        @media print {
            @page {
                size: A4;
                margin: 10mm;
            }

            body {
                background: white;
                padding: 0;
                margin: 0;
                font-size: 12pt;
            }

            .control-buttons {
                display: none !important;
            }

            .id-card {
                width: 210mm !important;
                height: 148.5mm !important;
                box-shadow: none;
                border: 2px solid #000;
                margin: 0 auto;
                page-break-inside: avoid;
                border-radius: 0;
            }

            .card-header::before {
                display: none;
            }

            .field-value {
                border-bottom: 1px solid #000 !important;
                background: transparent !important;
            }

            .field-label {
                font-weight: bold !important;
            }
        }

        /* ===== أنماط الاستجابة ===== */
        @media (max-width: 768px) {
            .id-card {
                width: 95vw;
                height: auto;
                min-height: 60vh;
            }

            .field-row {
                flex-direction: column;
                gap: var(--spacing-xs);
            }

            .field-arabic, .field-french {
                width: 100%;
            }
        }

        /* ===== حالة التحميل ===== */
        .loading {
            text-align: center;
            padding: var(--spacing-lg);
            color: var(--text-light);
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--morocco-red);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: var(--spacing-sm);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- الورقة الموحدة -->
    <div class="document">
        <!-- Header -->
        <div class="header">
            <!-- القسم الأول - اليسار -->
            <div class="header-left">
                <div>المملكة المغربية</div>
                <div>وزارة الداخلية</div>
                <div>إقليم أسفي</div>
                <div>جماعة أيير</div>
                <div>مكتب الحالة المدنية أيير</div>
            </div>

            <!-- القسم الثاني - الوسط -->
            <div class="header-center">
                <div>بطاقة شخصية للحالة المدنية</div>
                <div>تقوم مقام موجز عقد الولادة، طبقا للظهير الشريف</div>
                <div>الصادر في 03 أكتوبر 2002</div>
                <div>بإحداث نظام الحالة المدنية</div>
            </div>

            <!-- القسم الثالث - اليمين -->
            <div class="header-right">
                <div>Royaume du Maroc</div>
                <div>Ministère de l'Intérieur</div>
                <div>Région Marrakech - Safi</div>
                <div>Province de Safi</div>
                <div>Commune Ayir</div>
                <div>Bureau Etat Civil</div>
            </div>
        </div>

        <!-- قسم المعلومات الشخصية -->
    <div class="personal-info">
        <!-- الإسم الشخصي -->
        <div class="info-row">
            <span class="info-label">الإسم الشخصي:</span> <span class="info-value" id="firstName"></span>
            <span class="french-label">Prénom: <span id="firstNameFr"></span></span>
        </div>

        <!-- الاسم العائلي -->
        <div class="info-row">
            <span class="info-label">الاسم العائلي:</span> <span class="info-value" id="familyName"></span>
            <span class="french-label">Nom: <span id="familyNameFr"></span></span>
        </div>

        <!-- تاريخ الولادة -->
        <div class="info-row">
            <span class="info-label">تاريخ الولادة:</span> <span class="info-value" id="birthDate"></span>
            <span class="hijri-label">هجري</span>
        </div>

        <!-- موافق -->
        <div class="info-row">
            <span class="info-label">موافق:</span> <span class="info-value" id="corresponding"></span>
            <span class="hijri-label">ميلادي</span>
        </div>

        <!-- مكان الولادة -->
        <div class="info-row">
            <span class="info-label">مكان الولادة:</span> <span class="info-value" id="birthPlace"></span>
        </div>

        <!-- والده(ا) -->
        <div class="info-row">
            <span class="info-label">والده(ا):</span> <span class="info-value" id="father"></span>
        </div>

        <!-- والدته(ا) -->
        <div class="info-row">
            <span class="info-label">والدته(ا):</span> <span class="info-value" id="mother"></span>
        </div>

        <!-- ساكن حاليا ب -->
        <div class="info-row">
            <span class="info-label">ساكن حاليا ب:</span> <span class="info-value" id="currentAddress"></span>
            <span class="location-label">جماعة أيير، إقليم آسفي</span>
        </div>

        <!-- البيانات الهامشية -->
        <div class="info-row marginal-row">
            <span class="info-label">البيانات الهامشية:</span> <span class="marginal-x-marks">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</span> <span class="info-value" id="marginalData"></span>
        </div>

        <!-- سطر فاصل عادي -->
        <hr>

        <!-- الجزء الفارغ مقسم إلى جزأين -->
        <div class="bottom-section">
            <div class="left-section">
                <div class="witness-paragraph">
                    يشهد ضابط الحالة المدنية لجماعة أيير الموقع أسفله بمطابقة المعلومات الواردة في هذه البطاقة لكناش التعريف والحالة المدنية أو الدفتر العائلي أو بوت. موجز عقد الولادة رقم <span class="inline-field" id="actNumber"></span> سنة <span class="inline-field" id="actYear"></span> المسلم من مكتب الحالة المدنية لجماعة <span class="inline-field" id="issuingCommune"></span><br>
                    بتاريخ <span class="inline-field" id="issueDate"></span>
                </div>

                <div class="info-row" style="margin-left: 20mm;">
                    <span class="witness-label">أيير في:</span> <span class="info-value witness-value" id="certificationDate"></span>
                </div>

                <div class="signature-text">
                    الإمضاء وطابع المكتب
                </div>
            </div>
            <div class="right-section">
                <div class="info-row">
                    <span class="witness-label">أنا الموقع أسفله:</span> <span class="info-value witness-value" id="witnessName1"></span>
                </div>
                <div class="info-row">
                    <span class="witness-label">الساكن حاليا ب:</span> <span class="info-value witness-value" id="witnessAddress1"></span>
                </div>
                <div class="witness-paragraph">
                    أشهد بصحة المعلومات الواردة في هذه البطاقة.
                </div>
                <div class="witness-label">
                    إمضاء أو بصمة صاحب الطلب
                </div>

                <div class="witness-paragraph">
                    يعاقب بناء علي الفصل 663 من القانون الجنائي بالحبس من 6 أشهر إلي عامين وبغرامة من 120 إلي 1000 درهم أو بإحدي هاتين العقوبتين فقط من صنع عن علم شهادة تتضمن وقائع غير صحيحة أو زور أو عدل بأية وسيلة كانت، شهادة صحيحة الأصل ما لم يكن الفعل جريمة أشد
                </div>
            </div>
        </div>
    </div>
    <!-- إغلاق الورقة الموحدة -->

    <!-- أزرار التحكم -->
    <div class="control-buttons">
        <button onclick="printPage()" class="print-btn">
            🖨️ طباعة
        </button>
        <button onclick="window.location.href='index.html'" class="home-btn">
            🏠 الصفحة الرئيسية
        </button>
    </div>

    <script>
        function printPage() {
            // حفظ العنوان الأصلي
            const originalTitle = document.title;

            // تغيير العنوان إلى فارغ أثناء الطباعة
            document.title = '';

            // طباعة الصفحة
            window.print();

            // إعادة العنوان الأصلي بعد الطباعة
            setTimeout(() => {
                document.title = originalTitle;
            }, 1000);
        }

        // دالة تحويل الأرقام إلى عربية
        function convertToArabicNumbers(str) {
            if (!str) return str;
            const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            return str.toString().replace(/[0-9]/g, function(match) {
                return arabicNumbers[parseInt(match)];
            });
        }

        // ملء النموذج بالبيانات من URL أو بيانات تجريبية
        window.onload = function() {
            // استخراج البيانات من URL
            const urlParams = new URLSearchParams(window.location.search);

            // إذا وجدت بيانات في URL
            if (urlParams.has('firstNameAr')) {
                // ملء البيانات من URL
                document.getElementById('firstName').innerHTML = urlParams.get('firstNameAr') || '';
                document.getElementById('firstNameFr').innerHTML = urlParams.get('firstNameFr') || '';
                document.getElementById('familyName').innerHTML = urlParams.get('familyNameAr') || '';
                document.getElementById('familyNameFr').innerHTML = urlParams.get('familyNameFr') || '';

                // تاريخ الولادة: هجري أولاً
                document.getElementById('birthDate').innerHTML = urlParams.get('birthDateHijri') || '';

                // موافق: ميلادي ثانياً
                const gregorianDate = urlParams.get('birthDateGregorian');
                if (gregorianDate) {
                    // تحويل من YYYY-MM-DD إلى DD/MM/YYYY
                    const dateParts = gregorianDate.split('-');
                    if (dateParts.length === 3) {
                        document.getElementById('corresponding').innerHTML = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
                    } else {
                        document.getElementById('corresponding').innerHTML = gregorianDate;
                    }
                } else {
                    document.getElementById('corresponding').innerHTML = '';
                }

                document.getElementById('birthPlace').innerHTML = (urlParams.get('birthPlaceAr') || '') + ' / ' + (urlParams.get('birthPlaceFr') || '');
                document.getElementById('father').innerHTML = urlParams.get('fatherName') || '';
                document.getElementById('mother').innerHTML = urlParams.get('motherName') || '';
                document.getElementById('currentAddress').innerHTML = urlParams.get('currentAddressAr') || '';
                document.getElementById('marginalData').innerHTML = urlParams.get('marginalData') || 'لا شيء';

                // ملء بيانات القسم السفلي بأرقام فرنسية
                document.getElementById('actNumber').innerHTML = urlParams.get('actNumber') || '';
                document.getElementById('actYear').innerHTML = urlParams.get('actYear') || new Date().getFullYear();
                document.getElementById('issuingCommune').innerHTML = urlParams.get('issuingCommune') || 'أيير';
                document.getElementById('issueDate').innerHTML = urlParams.get('issueDate') || new Date().toLocaleDateString('en-US');
                document.getElementById('certificationDate').innerHTML = urlParams.get('certificationDate') || new Date().toLocaleDateString('en-US');

                // بيانات الشاهد
                document.getElementById('witnessName1').innerHTML = urlParams.get('witnessName') || '';
                document.getElementById('witnessAddress1').innerHTML = urlParams.get('witnessAddress') || '';
            } else {
                // بيانات تجريبية إذا لم توجد بيانات في URL
                document.getElementById('firstName').innerHTML = 'محمد';
                document.getElementById('firstNameFr').innerHTML = 'Mohamed';
                document.getElementById('familyName').innerHTML = 'العلوي';
                document.getElementById('familyNameFr').innerHTML = 'Alaoui';
                document.getElementById('birthDate').innerHTML = '25/06/1411'; // هجري
                document.getElementById('corresponding').innerHTML = '15/03/1990'; // ميلادي
                document.getElementById('birthPlace').innerHTML = 'أيير / Ayir';
                document.getElementById('father').innerHTML = 'عبد الله العلوي';
                document.getElementById('mother').innerHTML = 'فاطمة الزهراني';
                document.getElementById('currentAddress').innerHTML = 'حي السلام';
                document.getElementById('marginalData').innerHTML = 'لا شيء';

                // بيانات تجريبية للقسم السفلي بأرقام فرنسية
                document.getElementById('actNumber').innerHTML = '15/2024';
                document.getElementById('actYear').innerHTML = '2024';
                document.getElementById('issuingCommune').innerHTML = 'أيير';
                document.getElementById('issueDate').innerHTML = new Date().toLocaleDateString('en-US');
                document.getElementById('certificationDate').innerHTML = new Date().toLocaleDateString('en-US');

                // بيانات تجريبية للشاهد
                document.getElementById('witnessName1').innerHTML = 'عبد الرحمن العلوي';
                document.getElementById('witnessAddress1').innerHTML = 'حي النهضة أيير';
            }
        }
    </script>

</body>
</html>