// نظام حماية الصفحات والتحقق من المصادقة
class AuthGuard {
    constructor() {
        this.userManager = null;
        this.currentPage = window.location.pathname.split('/').pop() || 'index.html';
        this.publicPages = ['login.html', 'index.html']; // الصفحات التي لا تحتاج تسجيل دخول
        this.adminPages = ['user-management.html', 'system-settings.html']; // صفحات المدير فقط
    }

    // تهيئة نظام الحماية
    async init(userManagerInstance) {
        this.userManager = userManagerInstance;
        
        try {
            // تهيئة نظام المستخدمين
            await this.userManager.init();
            
            // فحص الصفحة الحالية
            await this.checkPageAccess();
            
            // إضافة معلومات المستخدم للصفحة
            this.addUserInfo();
            
            // إضافة زر تسجيل الخروج
            this.addLogoutButton();
            
            console.log('تم تهيئة نظام الحماية بنجاح');
            
        } catch (error) {
            console.error('خطأ في تهيئة نظام الحماية:', error);
            this.redirectToLogin();
        }
    }

    // فحص صلاحية الوصول للصفحة
    async checkPageAccess() {
        // إذا كانت الصفحة عامة، لا حاجة للفحص
        if (this.isPublicPage()) {
            // إذا كان المستخدم مسجل دخول وفي صفحة تسجيل الدخول، توجيهه للرئيسية
            if (this.currentPage === 'login.html' && this.userManager.isLoggedIn()) {
                window.location.href = 'main-dashboard.html';
            }
            return;
        }

        // فحص تسجيل الدخول
        if (!this.userManager.isLoggedIn()) {
            this.redirectToLogin();
            return;
        }

        // فحص صلاحيات المدير للصفحات الخاصة
        if (this.isAdminPage() && !this.userManager.hasPermission('manage_users')) {
            this.showAccessDenied();
            return;
        }

        // تسجيل دخول المستخدم للصفحة
        await this.logPageAccess();
    }

    // فحص إذا كانت الصفحة عامة
    isPublicPage() {
        return this.publicPages.includes(this.currentPage);
    }

    // فحص إذا كانت صفحة مدير
    isAdminPage() {
        return this.adminPages.includes(this.currentPage);
    }

    // توجيه لصفحة تسجيل الدخول
    redirectToLogin() {
        // حفظ الصفحة المطلوبة للعودة إليها بعد تسجيل الدخول
        if (!this.isPublicPage()) {
            localStorage.setItem('redirectAfterLogin', this.currentPage);
        }
        
        window.location.href = 'login.html';
    }

    // عرض رسالة عدم وجود صلاحية
    showAccessDenied() {
        document.body.innerHTML = `
            <div style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                font-family: 'Cairo', sans-serif;
                color: white;
                text-align: center;
                padding: 20px;
            ">
                <div style="
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(20px);
                    border-radius: 20px;
                    padding: 40px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    max-width: 500px;
                ">
                    <div style="font-size: 4em; margin-bottom: 20px;">🚫</div>
                    <h1 style="font-size: 2em; margin-bottom: 15px;">ليس لديك صلاحية</h1>
                    <p style="font-size: 1.2em; margin-bottom: 30px; opacity: 0.9;">
                        عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة
                    </p>
                    <button onclick="window.location.href='main-dashboard.html'" style="
                        background: rgba(255, 255, 255, 0.2);
                        color: white;
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        padding: 12px 25px;
                        border-radius: 10px;
                        font-family: 'Cairo', sans-serif;
                        font-size: 1em;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='rgba(255,255,255,0.3)'" 
                       onmouseout="this.style.background='rgba(255,255,255,0.2)'">
                        🏠 العودة للصفحة الرئيسية
                    </button>
                </div>
            </div>
        `;
    }

    // تسجيل دخول المستخدم للصفحة
    async logPageAccess() {
        try {
            const user = this.userManager.getCurrentUser();
            if (user) {
                // يمكن إضافة تسجيل دخول الصفحات هنا إذا لزم الأمر
                console.log(`المستخدم ${user.username} دخل صفحة ${this.currentPage}`);
            }
        } catch (error) {
            console.error('خطأ في تسجيل دخول الصفحة:', error);
        }
    }

    // إضافة معلومات المستخدم للصفحة
    addUserInfo() {
        if (!this.userManager.isLoggedIn() || this.isPublicPage()) return;

        const user = this.userManager.getCurrentUser();
        if (!user) return;

        // البحث عن مكان لإضافة معلومات المستخدم
        const header = document.querySelector('header, .header, .header-main');
        if (header) {
            // إنشاء عنصر معلومات المستخدم
            const userInfo = document.createElement('div');
            userInfo.className = 'user-info';
            userInfo.style.cssText = `
                position: absolute;
                top: 15px;
                left: 20px;
                background: rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(10px);
                border-radius: 25px;
                padding: 8px 15px;
                color: white;
                font-size: 0.9em;
                border: 1px solid rgba(255, 255, 255, 0.3);
                display: flex;
                align-items: center;
                gap: 8px;
                z-index: 1000;
            `;

            userInfo.innerHTML = `
                <span style="font-size: 1.2em;">${this.getUserRoleIcon(user.role)}</span>
                <span>${user.fullName}</span>
                <span style="opacity: 0.7; font-size: 0.8em;">(${this.getUserRoleText(user.role)})</span>
            `;

            header.style.position = 'relative';
            header.appendChild(userInfo);
        }
    }

    // إضافة زر تسجيل الخروج
    addLogoutButton() {
        if (!this.userManager.isLoggedIn() || this.isPublicPage()) return;

        // البحث عن مكان لإضافة زر تسجيل الخروج
        const header = document.querySelector('header, .header, .header-main');
        if (header) {
            const logoutButton = document.createElement('button');
            logoutButton.className = 'logout-button';
            logoutButton.style.cssText = `
                position: absolute;
                top: 15px;
                right: 20px;
                background: rgba(231, 76, 60, 0.8);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 20px;
                padding: 8px 15px;
                color: white;
                font-size: 0.9em;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 5px;
                z-index: 1000;
                font-family: 'Cairo', sans-serif;
            `;

            logoutButton.innerHTML = `
                <span>🚪</span>
                <span>تسجيل الخروج</span>
            `;

            logoutButton.addEventListener('click', () => this.logout());
            
            logoutButton.addEventListener('mouseenter', () => {
                logoutButton.style.background = 'rgba(231, 76, 60, 1)';
                logoutButton.style.transform = 'translateY(-2px)';
            });

            logoutButton.addEventListener('mouseleave', () => {
                logoutButton.style.background = 'rgba(231, 76, 60, 0.8)';
                logoutButton.style.transform = 'translateY(0)';
            });

            header.appendChild(logoutButton);
        }
    }

    // الحصول على أيقونة الدور
    getUserRoleIcon(role) {
        const icons = {
            'admin': '👑',
            'manager': '👨‍💼',
            'employee': '👤',
            'viewer': '👁️'
        };
        return icons[role] || '👤';
    }

    // الحصول على نص الدور
    getUserRoleText(role) {
        const roles = {
            'admin': 'مدير النظام',
            'manager': 'مدير',
            'employee': 'موظف',
            'viewer': 'مشاهد'
        };
        return roles[role] || 'مستخدم';
    }

    // تسجيل الخروج
    async logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            try {
                await this.userManager.logout();
                window.location.href = 'login.html';
            } catch (error) {
                console.error('خطأ في تسجيل الخروج:', error);
                // حتى لو فشل، توجيه للصفحة الرئيسية
                window.location.href = 'login.html';
            }
        }
    }

    // فحص الصلاحيات
    hasPermission(permission) {
        return this.userManager && this.userManager.hasPermission(permission);
    }

    // إخفاء العناصر بناءً على الصلاحيات
    hideElementsByPermission() {
        if (!this.userManager.isLoggedIn()) return;

        // إخفاء الروابط التي تحتاج صلاحيات خاصة
        const adminLinks = document.querySelectorAll('[data-permission="admin"]');
        adminLinks.forEach(link => {
            if (!this.hasPermission('manage_users')) {
                link.style.display = 'none';
            }
        });

        // إخفاء الأزرار التي تحتاج صلاحيات خاصة
        const managerButtons = document.querySelectorAll('[data-permission="manager"]');
        managerButtons.forEach(button => {
            if (!this.hasPermission('manage_data')) {
                button.style.display = 'none';
            }
        });
    }

    // إضافة تنبيه انتهاء الجلسة
    addSessionWarning() {
        if (!this.userManager.isLoggedIn()) return;

        // فحص انتهاء الجلسة كل دقيقة
        setInterval(() => {
            const sessionData = localStorage.getItem('userSession');
            if (sessionData) {
                const session = JSON.parse(sessionData);
                const expiresAt = new Date(session.expiresAt);
                const now = new Date();
                const timeLeft = expiresAt - now;

                // تحذير قبل 15 دقيقة من انتهاء الجلسة
                if (timeLeft > 0 && timeLeft < 15 * 60 * 1000) {
                    const minutes = Math.floor(timeLeft / (60 * 1000));
                    this.showSessionWarning(minutes);
                } else if (timeLeft <= 0) {
                    this.sessionExpired();
                }
            }
        }, 60000); // فحص كل دقيقة
    }

    // عرض تحذير انتهاء الجلسة
    showSessionWarning(minutes) {
        // تجنب عرض تحذيرات متعددة
        if (document.getElementById('sessionWarning')) return;

        const warning = document.createElement('div');
        warning.id = 'sessionWarning';
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            font-family: 'Cairo', sans-serif;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;

        warning.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 1.5em;">⏰</span>
                <div>
                    <div style="font-weight: bold; margin-bottom: 5px;">تحذير انتهاء الجلسة</div>
                    <div style="font-size: 0.9em;">ستنتهي جلستك خلال ${minutes} دقيقة</div>
                </div>
            </div>
        `;

        document.body.appendChild(warning);

        // إزالة التحذير بعد 10 ثوان
        setTimeout(() => {
            if (warning.parentNode) {
                warning.remove();
            }
        }, 10000);
    }

    // انتهاء الجلسة
    async sessionExpired() {
        await this.userManager.logout();
        
        alert('انتهت جلستك. سيتم توجيهك لصفحة تسجيل الدخول.');
        window.location.href = 'login.html';
    }
}

// إنشاء مثيل عام لنظام الحماية
const authGuard = new AuthGuard();

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    // التأكد من وجود userManager
    if (typeof userManager !== 'undefined') {
        try {
            await authGuard.init(userManager);
            authGuard.hideElementsByPermission();
            authGuard.addSessionWarning();
        } catch (error) {
            console.error('خطأ في تهيئة نظام الحماية:', error);
        }
    }
});

// إضافة أنماط CSS للتحذيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
`;
document.head.appendChild(style);
