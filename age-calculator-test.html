<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حساب العمر</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input, button {
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.9);
            color: #333;
            font-size: 14px;
        }
        button {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        .result {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-family: monospace;
            font-size: 16px;
            border-left: 4px solid #2ecc71;
        }
        .error {
            border-left-color: #e74c3c;
            background: rgba(231, 76, 60, 0.2);
        }
        .test-case {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 اختبار حساب العمر عند الوفاة</h1>
        <p>هذا الاختبار يتحقق من دقة حساب العمر من تاريخ الازدياد إلى تاريخ الوفاة</p>

        <div class="test-section">
            <h2>📅 حساب العمر التفاعلي</h2>
            <div class="form-group">
                <label for="birthDate">تاريخ الازدياد:</label>
                <input type="date" id="birthDate" onchange="calculateInteractiveAge()">
            </div>
            <div class="form-group">
                <label for="deathDate">تاريخ الوفاة:</label>
                <input type="date" id="deathDate" onchange="calculateInteractiveAge()">
            </div>
            <div class="form-group">
                <label>العمر المحسوب:</label>
                <div id="interactiveResult" class="result">أدخل التواريخ لحساب العمر</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 اختبارات تلقائية</h2>
            <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>⚠️ اختبار حالات الخطأ</h2>
            <button onclick="testErrorCases()">اختبار حالات الخطأ</button>
            <div id="errorResults"></div>
        </div>
    </div>

    <script>
        // نسخة من وظيفة حساب العمر من صفحة الوفيات
        function calculateAge(birthDate, deathDate) {
            if (!birthDate || !deathDate) {
                return null;
            }

            try {
                const birth = new Date(birthDate);
                const death = new Date(deathDate);

                // التحقق من صحة التواريخ
                if (isNaN(birth.getTime()) || isNaN(death.getTime())) {
                    console.warn('⚠️ تواريخ غير صحيحة');
                    return null;
                }

                // التحقق من أن تاريخ الوفاة بعد تاريخ الازدياد
                if (death < birth) {
                    console.warn('⚠️ تاريخ الوفاة قبل تاريخ الازدياد');
                    return null;
                }

                // حساب العمر بالسنوات فقط
                let years = death.getFullYear() - birth.getFullYear();
                const birthThisYear = new Date(death.getFullYear(), birth.getMonth(), birth.getDate());

                // إذا لم يحن موعد عيد الميلاد بعد في سنة الوفاة، نقص سنة
                if (death < birthThisYear) {
                    years--;
                }

                // تنسيق العمر (السنوات فقط)
                let ageString = '';
                if (years > 0) {
                    ageString = `${years} سنة`;
                } else {
                    ageString = 'أقل من سنة';
                }

                return ageString;

            } catch (error) {
                console.error('❌ خطأ في حساب العمر:', error);
                return null;
            }
        }

        // حساب العمر التفاعلي
        function calculateInteractiveAge() {
            const birthDate = document.getElementById('birthDate').value;
            const deathDate = document.getElementById('deathDate').value;
            const resultDiv = document.getElementById('interactiveResult');

            if (birthDate && deathDate) {
                const age = calculateAge(birthDate, deathDate);
                if (age) {
                    resultDiv.textContent = `العمر: ${age}`;
                    resultDiv.className = 'result';
                } else {
                    resultDiv.textContent = 'خطأ في الحساب - تحقق من التواريخ';
                    resultDiv.className = 'result error';
                }
            } else {
                resultDiv.textContent = 'أدخل التواريخ لحساب العمر';
                resultDiv.className = 'result';
            }
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>نتائج الاختبارات:</h3>';

            const testCases = [
                {
                    name: 'طفل رضيع (أقل من سنة)',
                    birth: '2024-01-15',
                    death: '2024-03-15',
                    expected: 'أقل من سنة'
                },
                {
                    name: 'طفل صغير (سنة واحدة)',
                    birth: '2022-06-10',
                    death: '2023-12-10',
                    expected: '1 سنة'
                },
                {
                    name: 'شاب (25 سنة)',
                    birth: '1998-03-20',
                    death: '2023-03-20',
                    expected: '25 سنة'
                },
                {
                    name: 'كبير السن (78 سنة)',
                    birth: '1945-01-01',
                    death: '2023-04-01',
                    expected: '78 سنة'
                },
                {
                    name: 'نفس اليوم (أقل من سنة)',
                    birth: '2024-01-01',
                    death: '2024-01-01',
                    expected: 'أقل من سنة'
                },
                {
                    name: 'سنة كاملة بالضبط',
                    birth: '2022-12-25',
                    death: '2023-12-25',
                    expected: '1 سنة'
                },
                {
                    name: 'قبل عيد الميلاد (أقل من سنة)',
                    birth: '2023-06-15',
                    death: '2024-02-15',
                    expected: 'أقل من سنة'
                },
                {
                    name: 'عمر 45 سنة',
                    birth: '1978-05-12',
                    death: '2023-12-25',
                    expected: '45 سنة'
                },
                {
                    name: 'عمر قبل عيد الميلاد (44 سنة)',
                    birth: '1978-12-25',
                    death: '2023-05-12',
                    expected: '44 سنة'
                }
            ];

            testCases.forEach((testCase, index) => {
                const result = calculateAge(testCase.birth, testCase.death);
                const isCorrect = result === testCase.expected;

                resultsDiv.innerHTML += `
                    <div class="test-case">
                        <h4>اختبار ${index + 1}: ${testCase.name}</h4>
                        <p><strong>من:</strong> ${testCase.birth} <strong>إلى:</strong> ${testCase.death}</p>
                        <p><strong>النتيجة:</strong> ${result || 'فشل'}</p>
                        <p><strong>المتوقع:</strong> ${testCase.expected}</p>
                        <p><strong>الحالة:</strong> ${isCorrect ? '✅ نجح' : '❌ فشل'}</p>
                    </div>
                `;
            });
        }

        // اختبار حالات الخطأ
        function testErrorCases() {
            const resultsDiv = document.getElementById('errorResults');
            resultsDiv.innerHTML = '<h3>نتائج اختبار حالات الخطأ:</h3>';

            const errorCases = [
                {
                    name: 'تاريخ وفاة قبل تاريخ الازدياد',
                    birth: '2023-01-01',
                    death: '2022-01-01',
                    expectedResult: null
                },
                {
                    name: 'تاريخ ازدياد فارغ',
                    birth: '',
                    death: '2023-01-01',
                    expectedResult: null
                },
                {
                    name: 'تاريخ وفاة فارغ',
                    birth: '2022-01-01',
                    death: '',
                    expectedResult: null
                },
                {
                    name: 'تاريخ غير صحيح',
                    birth: '2022-13-01',
                    death: '2023-01-01',
                    expectedResult: null
                },
                {
                    name: 'تاريخ مستحيل',
                    birth: '2022-02-30',
                    death: '2023-01-01',
                    expectedResult: null
                }
            ];

            errorCases.forEach((errorCase, index) => {
                const result = calculateAge(errorCase.birth, errorCase.death);
                const isCorrect = result === errorCase.expectedResult;

                resultsDiv.innerHTML += `
                    <div class="test-case">
                        <h4>اختبار خطأ ${index + 1}: ${errorCase.name}</h4>
                        <p><strong>من:</strong> "${errorCase.birth}" <strong>إلى:</strong> "${errorCase.death}"</p>
                        <p><strong>النتيجة:</strong> ${result || 'null'}</p>
                        <p><strong>المتوقع:</strong> ${errorCase.expectedResult || 'null'}</p>
                        <p><strong>الحالة:</strong> ${isCorrect ? '✅ تم التعامل مع الخطأ بشكل صحيح' : '❌ لم يتم التعامل مع الخطأ'}</p>
                    </div>
                `;
            });
        }

        // تشغيل اختبار سريع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧮 صفحة اختبار حساب العمر جاهزة');

            // ملء تواريخ تجريبية
            document.getElementById('birthDate').value = '1990-05-15';
            document.getElementById('deathDate').value = '2023-08-20';
            calculateInteractiveAge();
        });
    </script>
</body>
</html>
