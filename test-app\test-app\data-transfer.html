<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقل البيانات بين الأجهزة - مكتب الحالة المدنية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', '<PERSON><PERSON>', 'Times New Roman', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #006233 0%, #2c3e50 50%, #006233 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 25px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
            letter-spacing: 1px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.95;
            margin-bottom: 20px;
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.15);
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
        }

        .main-content {
            padding: 30px;
        }

        .transfer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .transfer-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .section-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            width: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            box-shadow: 0 4px 15px rgba(44, 62, 80, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .file-input {
            width: 100%;
            padding: 12px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .file-input:hover {
            border-color: #2c3e50;
            background: #f8f9fa;
        }

        .status-panel {
            background: white;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
            animation: slideIn 0.3s ease;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        .system-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .transfer-grid {
                grid-template-columns: 1fr;
            }

            .nav-links {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <h1>🔄 نقل البيانات بين الأجهزة</h1>
                <p>نظام متقدم لنقل ومزامنة بيانات الحالة المدنية بأمان</p>

                <div class="nav-links">
                    <a href="main-dashboard.html" class="nav-link">🏠 الصفحة الرئيسية</a>
                    <a href="citizens-database-indexeddb.html" class="nav-link">🗃️ إدارة البيانات</a>
                    <a href="search-citizens.html" class="nav-link">🔍 البحث في السجلات</a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- System Status -->
            <div class="status-panel">
                <h2 class="section-title">📊 حالة النظام</h2>
                <div id="systemInfo" class="system-info">
                    جاري فحص النظام...
                </div>
                <div id="alertContainer"></div>
            </div>

            <!-- Transfer Grid -->
            <div class="transfer-grid">
                <!-- Export Section -->
                <div class="transfer-section">
                    <h2 class="section-title">📤 تصدير البيانات</h2>
                    
                    <p style="margin-bottom: 20px; color: #6c757d;">
                        تصدير جميع بيانات المواطنين لنقلها إلى جهاز آخر
                    </p>

                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" id="includeImages" checked>
                            تضمين صور الشهادات (يزيد حجم الملف)
                        </label>
                    </div>

                    <button class="btn btn-primary" onclick="exportData()">
                        📦 تصدير البيانات
                    </button>

                    <div id="exportProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="exportProgressFill"></div>
                        </div>
                        <div id="exportStatus">جاري التصدير...</div>
                    </div>
                </div>

                <!-- Import Section -->
                <div class="transfer-section">
                    <h2 class="section-title">📥 استيراد البيانات</h2>
                    
                    <p style="margin-bottom: 20px; color: #6c757d;">
                        استيراد بيانات من جهاز آخر أو نسخة احتياطية
                    </p>

                    <input type="file" id="importFile" accept=".json" class="file-input" 
                           onchange="handleFileSelect(this)">

                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" id="clearExisting">
                            مسح البيانات الموجودة قبل الاستيراد
                        </label>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" id="createBackup" checked>
                            إنشاء نسخة احتياطية قبل الاستيراد
                        </label>
                    </div>

                    <button class="btn btn-success" onclick="importData()" id="importBtn" disabled>
                        📥 استيراد البيانات
                    </button>

                    <div id="importProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="importProgressFill"></div>
                        </div>
                        <div id="importStatus">جاري الاستيراد...</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="transfer-section">
                <h2 class="section-title">⚡ إجراءات سريعة</h2>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="btn btn-warning" onclick="createEmergencyBackup()">
                        🆘 نسخة احتياطية طارئة
                    </button>
                    
                    <button class="btn btn-primary" onclick="validateDatabase()">
                        🔍 فحص سلامة البيانات
                    </button>
                    
                    <button class="btn btn-success" onclick="showSyncHistory()">
                        📋 تاريخ المزامنة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Enhanced Database Managers -->
    <script src="cross-platform-manager.js"></script>
    <script src="data-sync-manager.js"></script>
    <script src="indexeddb-manager.js"></script>

    <script>
        // Global variables
        let selectedFile = null;
        let isProcessing = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // Initialize managers
                await crossPlatformManager.init();
                await citizensDB.init();
                await dataSyncManager.init();

                // Display system information
                displaySystemInfo();

                showAlert('✅ تم تهيئة النظام بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);
                showAlert('❌ خطأ في تهيئة النظام: ' + error.message, 'error');
            }
        });

        // Display system information
        function displaySystemInfo() {
            const status = crossPlatformManager.getSystemStatus();
            const lastSync = dataSyncManager.getLastSyncTime();

            const systemInfoDiv = document.getElementById('systemInfo');
            systemInfoDiv.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div>
                        <strong>نوع التخزين:</strong><br>
                        ${status.storageType === 'indexedDB' ? '🗄️ IndexedDB (متقدم)' : '💾 التخزين المحلي (أساسي)'}
                    </div>
                    <div>
                        <strong>حالة المنصة:</strong><br>
                        ${status.capabilities.indexedDB ? '✅ متقدمة' : '⚠️ أساسية'}
                    </div>
                    <div>
                        <strong>آخر مزامنة:</strong><br>
                        ${lastSync ? new Date(lastSync).toLocaleString('ar-MA') : 'لم تتم مزامنة بعد'}
                    </div>
                    <div>
                        <strong>دعم الملفات:</strong><br>
                        ${status.capabilities.fileAPI ? '✅ مدعوم' : '❌ غير مدعوم'}
                    </div>
                </div>
            `;
        }

        // Export data
        async function exportData() {
            if (isProcessing) {
                showAlert('⚠️ عملية أخرى قيد التنفيذ', 'error');
                return;
            }

            try {
                isProcessing = true;
                const includeImages = document.getElementById('includeImages').checked;

                showProgress('export', 0, 'بدء التصدير...');

                // Get data count for progress
                const citizens = await citizensDB.getAllCitizens();
                if (citizens.length === 0) {
                    showAlert('⚠️ لا توجد بيانات للتصدير', 'error');
                    return;
                }

                showProgress('export', 25, `جاري تصدير ${citizens.length} سجل...`);

                // Export using data sync manager
                await dataSyncManager.exportForTransfer(includeImages);

                showProgress('export', 100, 'تم التصدير بنجاح!');
                showAlert(`✅ تم تصدير ${citizens.length} سجل بنجاح`, 'success');

                setTimeout(() => hideProgress('export'), 2000);

            } catch (error) {
                console.error('خطأ في التصدير:', error);
                showAlert('❌ خطأ في التصدير: ' + error.message, 'error');
                hideProgress('export');
            } finally {
                isProcessing = false;
            }
        }

        // Handle file selection
        function handleFileSelect(input) {
            selectedFile = input.files[0];
            const importBtn = document.getElementById('importBtn');

            if (selectedFile) {
                if (selectedFile.type === 'application/json' || selectedFile.name.endsWith('.json')) {
                    importBtn.disabled = false;
                    showAlert(`✅ تم اختيار الملف: ${selectedFile.name}`, 'info');
                } else {
                    importBtn.disabled = true;
                    showAlert('❌ يرجى اختيار ملف JSON صحيح', 'error');
                    selectedFile = null;
                }
            } else {
                importBtn.disabled = true;
            }
        }

        // Import data
        async function importData() {
            if (isProcessing) {
                showAlert('⚠️ عملية أخرى قيد التنفيذ', 'error');
                return;
            }

            if (!selectedFile) {
                showAlert('❌ يرجى اختيار ملف للاستيراد', 'error');
                return;
            }

            try {
                isProcessing = true;
                const clearExisting = document.getElementById('clearExisting').checked;
                const createBackup = document.getElementById('createBackup').checked;

                showProgress('import', 0, 'بدء الاستيراد...');

                // Confirm if clearing existing data
                if (clearExisting) {
                    const confirmed = confirm('⚠️ تحذير: سيتم مسح جميع البيانات الموجودة!\nهل أنت متأكد من المتابعة؟');
                    if (!confirmed) {
                        isProcessing = false;
                        hideProgress('import');
                        return;
                    }
                }

                showProgress('import', 20, 'قراءة الملف...');

                // Import using data sync manager
                const result = await dataSyncManager.importFromTransfer(selectedFile, {
                    clearExisting,
                    createBackup,
                    skipDuplicates: true
                });

                showProgress('import', 100, 'تم الاستيراد بنجاح!');

                const message = `✅ تم الاستيراد بنجاح!\n` +
                              `📥 تم استيراد: ${result.imported} سجل\n` +
                              `⏭️ تم تخطي: ${result.skipped} سجل\n` +
                              `❌ أخطاء: ${result.errors} سجل`;

                showAlert(message, 'success');

                // Reset file input
                document.getElementById('importFile').value = '';
                document.getElementById('importBtn').disabled = true;
                selectedFile = null;

                setTimeout(() => hideProgress('import'), 2000);

            } catch (error) {
                console.error('خطأ في الاستيراد:', error);
                showAlert('❌ خطأ في الاستيراد: ' + error.message, 'error');
                hideProgress('import');
            } finally {
                isProcessing = false;
            }
        }

        // Create emergency backup
        async function createEmergencyBackup() {
            try {
                const citizens = await citizensDB.getAllCitizens();
                const success = await crossPlatformManager.createAutoBackup(citizens);

                if (success) {
                    showAlert('✅ تم إنشاء نسخة احتياطية طارئة', 'success');
                } else {
                    showAlert('⚠️ فشل في إنشاء النسخة الاحتياطية', 'error');
                }
            } catch (error) {
                console.error('خطأ في النسخة الاحتياطية:', error);
                showAlert('❌ خطأ في إنشاء النسخة الاحتياطية', 'error');
            }
        }

        // Validate database
        async function validateDatabase() {
            try {
                showAlert('🔍 جاري فحص سلامة البيانات...', 'info');

                const validation = await citizensDB.validateDatabase();

                if (validation.isValid) {
                    showAlert(`✅ البيانات سليمة! تم فحص ${validation.totalRecords} سجل`, 'success');
                } else {
                    const issues = validation.issues.slice(0, 5).join('\n');
                    showAlert(`⚠️ تم العثور على ${validation.issues.length} مشكلة:\n${issues}`, 'error');
                }
            } catch (error) {
                console.error('خطأ في فحص البيانات:', error);
                showAlert('❌ خطأ في فحص البيانات', 'error');
            }
        }

        // Show sync history
        function showSyncHistory() {
            const history = dataSyncManager.getSyncHistory();

            if (history.length === 0) {
                showAlert('📋 لا يوجد تاريخ مزامنة', 'info');
                return;
            }

            const historyText = history.slice(-5).map(op =>
                `${op.type === 'export' ? '📤' : '📥'} ${op.type === 'export' ? 'تصدير' : 'استيراد'} - ${new Date(op.timestamp).toLocaleString('ar-MA')}`
            ).join('\n');

            showAlert(`📋 آخر 5 عمليات:\n${historyText}`, 'info');
        }

        // Show progress
        function showProgress(type, percentage, message) {
            const progressDiv = document.getElementById(`${type}Progress`);
            const progressFill = document.getElementById(`${type}ProgressFill`);
            const statusDiv = document.getElementById(`${type}Status`);

            progressDiv.style.display = 'block';
            progressFill.style.width = percentage + '%';
            statusDiv.textContent = message;
        }

        // Hide progress
        function hideProgress(type) {
            const progressDiv = document.getElementById(`${type}Progress`);
            progressDiv.style.display = 'none';
        }

        // Show alert
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;

            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 8000);
        }
    </script>
</body>
</html>
