<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخة موجزة من رسم الوفاة</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Cairo', 'Times New Roman', serif;
            margin: 0;
            padding: 10mm; /* هوامش خارجية محسنة */
            font-size: 11px; /* زيادة لملء المساحة بشكل أفضل */
            font-weight: 500;
            line-height: 1.3; /* زيادة لتحسين القراءة */
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .document {
            width: 210mm; /* عرض نصف ورقة A4 أفقي */
            height: 148.5mm; /* ارتفاع نصف ورقة A4 أفقي */
            margin: 20px auto; /* هامش علوي للفصل عن الأزرار */
            padding: 3mm; /* هوامش داخلية مناسبة للتخطيط الأفقي */
            background: white;
            display: flex;
            flex-direction: column;
            border: 2px solid #ddd; /* حدود واضحة للوثيقة */
            box-shadow: 0 5px 20px rgba(0,0,0,0.15); /* ظل أقوى */
            box-sizing: border-box;
            overflow: hidden; /* منع تجاوز المحتوى */
            position: relative;
            border-radius: 5px; /* زوايا مستديرة */
        }

        /* Header - محسن لملء المساحة */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2mm; /* زيادة لتحسين التنظيم */
            padding-bottom: 1mm; /* زيادة لتحسين المظهر */
            font-size: 10px; /* زيادة لملء المساحة وتحسين القراءة */
            font-weight: bold;
        }
        .header-right { text-align: left; direction: ltr; }
        .header-center { text-align: center; flex: 1; margin: 0 5mm; }
        .header-left { text-align: right; direction: rtl; }

        /* Unified content layout */
        .content {
            height: calc(100% - 12mm); /* تقليل الهامش العلوي */
            padding: 1mm; /* تقليل الـ padding */
            display: flex;
            flex-direction: column;
        }

        /* Content lines - محسن لملء المساحة */
        .merged-line {
            margin: 0.5mm 0; /* زيادة لتحسين التباعد */
            font-size: 10px; /* زيادة لملء المساحة وتحسين القراءة */
            font-weight: 500;
            display: flex;
            align-items: baseline;
            justify-content: space-between;
        }
        .french-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: ltr;
            text-align: left;
        }
        .arabic-part {
            flex: 1;
            display: flex;
            align-items: baseline;
            direction: rtl;
            text-align: right;
        }
        .label {
            font-weight: 500;
            margin: 0 2mm;
            white-space: nowrap;
            min-width: 15mm;
            color: #333;
        }
        .underline {
            border-bottom: none;
            min-width: 15mm;
            padding: 0 1mm;
            flex: 1;
            min-height: 2mm;
            font-weight: 700;
            color: #000;
        }

        /* Test controls */
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,123,255,0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        .controls button {
            background: white;
            color: #007bff;
            border: none;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
        }

        /* تحسينات الأزرار */
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .load-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(17, 153, 142, 0.4);
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(252, 182, 159, 0.4);
        }

        @media print {
            body {
                margin: 0;
                padding: 0; /* إزالة جميع الهوامش عند الطباعة */
                background: white !important;
                font-size: 8px; /* تقليل حجم الخط عند الطباعة */
            }

            .document {
                width: 210mm; /* عرض نصف ورقة A4 أفقي */
                height: 148.5mm; /* ارتفاع نصف ورقة A4 أفقي */
                margin: 0; /* إزالة الهوامش الخارجية */
                padding: 3mm; /* هوامش داخلية مناسبة للتخطيط الأفقي */
                max-width: none;
                box-shadow: none;
                border: none;
                page-break-inside: avoid;
                box-sizing: border-box;
                overflow: hidden; /* منع تجاوز المحتوى عند الطباعة */
                display: flex;
                flex-direction: column;
            }

            .controls {
                display: none !important; /* إخفاء أزرار التحكم */
            }
            .print-controls {
                display: none !important;
            }
            .controls-section {
                display: none !important; /* إخفاء قسم التحكم الجديد */
            }

            /* إعدادات الطباعة المحسنة لنصف ورقة A4 أفقي */
            @page {
                size: 210mm 148.5mm; /* نصف ورقة A4 أفقي (عرض × ارتفاع) */
                margin: 0; /* إزالة جميع هوامش الطابعة */
            }

            /* تحسين الخطوط عند الطباعة لملء المساحة */
            .header {
                font-size: 8px; /* خط مناسب للهيدر عند الطباعة */
                margin-bottom: 1mm;
                padding-bottom: 0.5mm;
            }

            .merged-line {
                font-size: 8px; /* خط مناسب للمحتوى عند الطباعة */
                margin: 0.3mm 0;
            }

            /* تحسين أحجام الخطوط في المحتوى */
            body {
                font-size: 9px; /* تقليل قليل عند الطباعة */
            }

            /* تحسين المسافات عند الطباعة */
            .content {
                padding: 0.5mm;
                height: calc(100% - 8mm);
            }

            /* تحسين العنوان */
            .document h1, .document h2, .document h3 {
                font-size: 8px;
                margin: 0.5mm 0;
            }

            /* تحسينات إضافية للطباعة */
            .printing {
                background: white !important;
            }

            .printing * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* إخفاء عناصر إضافية */
            .no-print {
                display: none !important;
            }

            /* تحسين جودة النص */
            .document {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                text-rendering: optimizeLegibility;
            }
        }

        /* تأثيرات بصرية للأزرار والوثيقة */
        .controls-section button:hover,
        .controls-section a:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
        }

        .controls-section {
            animation: fadeInUp 0.6s ease-out;
        }

        .document {
            animation: slideInDown 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسين مظهر الصفحة */
        body {
            min-height: 100vh;
            padding: 20px 0;
        }
    </style>
</head>
<body>
    <!-- أزرار التحكم خارج قسم الوثيقة -->
    <div class="controls-section" style="
        text-align: center;
        margin: 20px auto;
        padding: 20px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        max-width: 900px;
        direction: rtl;
    ">
        <h3 style="margin-bottom: 15px; color: #2c3e50; font-size: 1.3em;">
            📋 نسخة موجزة من رسم الوفاة
        </h3>

        <!-- أزرار الطباعة -->
        <div style="margin-bottom: 15px;">
            <button onclick="printCertificate()" class="print-btn" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                transition: all 0.3s ease;
                margin: 0 5px;
            ">
                🖨️ طباعة عادية
            </button>

            <button onclick="quickPrint()" class="print-btn" style="
                background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
                transition: all 0.3s ease;
                margin: 0 5px;
            ">
                ⚡ طباعة سريعة
            </button>

            <button onclick="printWithPreview()" class="print-btn" style="
                background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
                transition: all 0.3s ease;
                margin: 0 5px;
            ">
                🔍 طباعة + معاينة
            </button>
        </div>

        <!-- أزرار إضافية -->
        <div>
            <button onclick="loadFromDatabase()" style="
                background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                margin: 0 5px;
                transition: all 0.3s ease;
            ">
                📊 تحميل من قاعدة البيانات
            </button>

            <button onclick="fillTestData()" style="
                background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                margin: 0 5px;
                transition: all 0.3s ease;
            ">
                📝 بيانات تجريبية
            </button>

            <button onclick="clearAllFields()" style="
                background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                margin: 0 5px;
                transition: all 0.3s ease;
            ">
                🗑️ مسح البيانات
            </button>

            <a href="index.html" style="
                background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: bold;
                text-decoration: none;
                margin: 0 5px;
                transition: all 0.3s ease;
                display: inline-block;
            ">
                🏠 الصفحة الرئيسية
            </a>
        </div>
    </div>

    <!-- قسم الوثيقة المحسن -->
    <div class="document">
        <!-- Header -->
        <div class="header">
            <div class="header-right">
            </div>
            <div class="header-center">
            </div>
            <div class="header-left">
                <div>المملكة المغربية</div>
                <div>وزارة الداخلية</div>
                <div>إقليم أسفي</div>
                <div>جماعة أيير</div>
                <div>مكتب الحالة المدنية أيير</div>
                <div>عقد رقم : ......../...........</div>
            </div>
        </div>

        <!-- Content Section - محسن لمنع التجاوز -->
        <div class="content" style="
            margin: 0.5mm 0;
            font-size: 9px;
            width: 100%;
            direction: rtl;
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        ">

            <div style="text-align: center; margin-bottom: 1.5mm;"> <!-- زيادة لتحسين التنظيم -->
                <div style="font-size: 14px; font-weight: bold; color: #2c3e50;"> <!-- زيادة كبيرة للعنوان ليكون بارزاً -->
                    نسخة موجزة من رسم الوفاة
                </div>
            </div>

            <div style="
                display: grid;
                grid-template-columns: 30% 70%;
                gap: 1mm;
                direction: rtl;
                margin: 1mm 0;
                flex: 1;
                align-content: start;
                font-size: 10px;
            ">

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">توفي(ت) بـ:</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="deathPlaceAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">في:</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="deathDateAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">الاسم الشخصي:</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="firstNameAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">الاسم العائلي:</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="familyNameAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">تاريخ الولادة:</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="birthDateAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">مكان الولادة:</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="birthPlaceAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">مهنته(ها):</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="professionAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">الساكن(ة):</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="residenceAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">والده(ا):</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="fatherNameAr"></span>
                </div>

                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="font-weight: 600;">والدته(ا):</span>
                </div>
                <div style="padding: 0.8mm 0; align-self: start;">
                    <span style="border-bottom: 1.5px solid #000; min-width: 100mm; display: inline-block; padding-bottom: 0.5mm; font-size: 10px;" id="motherNameAr"></span>
                </div>

            </div>

            <!-- قسم الشهادة والتوقيع - مرفوع إلى أعلى -->
            <div style="
                margin-top: 10mm;
                padding-top: 0mm;
                flex-shrink: 0;
                border-top: 1px solid #ddd;
            ">
                <!-- قسم الشهادة مع الخط في نفس السطر -->
                <div style="padding: 1mm 0; direction: rtl; font-size: 10px;">
                    <span style="font-weight: 600;">نشهد بصفتنا ضابطا الحالة المدنية نحن: </span>
                    <span style="border-bottom: 1px solid #000; min-width: 60mm; display: inline-block; padding-bottom: 0.3mm; margin: 0 5mm;"></span>
                </div>

                <div style="padding: 1mm 0; direction: rtl; font-size: 10px;">
                    <span>بمطابقة هذه النسخة لما هو مضمن في سجلات الحالة المدنية بالمكتب المذكور</span>
                </div>

                <!-- Date and Place Section -->
                <div style="display: flex; justify-content: space-between; margin-top: 3mm; direction: rtl; font-size: 10px;">
                    <div style="text-align: left;">
                        <!-- مساحة فارغة -->
                    </div>
                    <div style="text-align: right;">
                        <span style="font-weight: 600;">أيير في: </span>
                        <span id="currentDate" style="font-weight: bold;"></span>
                    </div>
                </div>

                <!-- Signature Section -->
                <div style="display: flex; justify-content: space-between; margin-top: 2mm; direction: rtl; font-size: 10px;">
                    <div style="text-align: left;">
                        <div style="font-weight: 600;">طابع مكتب الحالة المدنية</div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-weight: 600;">ضابط الحالة المدنية</div>
                    </div>
                </div>
            </div>

        </div>

        <!-- تم نقل الأزرار خارج قسم document -->

    </div>

    <script>
        // متغير لتتبع حالة تحميل قاعدة البيانات
        let dbReady = false;
        let pendingDataLoad = null;

        // استخدام نفس مدير قاعدة البيانات المستخدم في صفحة البحث
        // تحميل مدير قاعدة البيانات
        const script = document.createElement('script');
        script.src = 'indexeddb-manager.js';
        document.head.appendChild(script);

        // انتظار تحميل المدير ثم تهيئة قاعدة البيانات
        script.onload = async function() {
            try {
                await citizensDB.init();
                console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
                dbReady = true;

                // إذا كان هناك طلب معلق لتحميل البيانات، قم بتنفيذه الآن
                if (pendingDataLoad) {
                    console.log('🔄 تنفيذ طلب تحميل البيانات المعلق...');
                    await pendingDataLoad();
                    pendingDataLoad = null;
                }
            } catch (error) {
                console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
            }
        };

        // ملء بيانات تجريبية
        function fillTestData() {
            // مسح جميع الحقول - النموذج يبقى فارغ
            clearAllFields();
            console.log('📋 النموذج فارغ - جاهز للملء اليدوي');
        }

        // وظيفة مسح جميع الحقول (جعل النموذج ورقة بيضاء)
        function clearAllFields() {
            // مسح الحقول الجديدة في النموذج المحدث
            const fieldsToClean = [
                'deathPlaceAr', 'deathDateAr', 'firstNameAr', 'familyNameAr',
                'birthDateAr', 'birthPlaceAr', 'professionAr', 'residenceAr',
                'fatherNameAr', 'motherNameAr', 'certificationAr'
            ];

            fieldsToClean.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.textContent = '';
                }
            });

            console.log('🗑️ تم مسح جميع الحقول - النموذج ورقة بيضاء');
        }

        // تحميل البيانات من قاعدة البيانات
        async function loadFromDatabase() {
            try {
                // التحقق من جاهزية قاعدة البيانات
                if (!dbReady) {
                    alert('⏳ قاعدة البيانات لم تكتمل تهيئتها بعد، يرجى المحاولة مرة أخرى');
                    return;
                }

                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await citizensDB.init();
                }

                // البحث عن أول متوفى في قاعدة البيانات
                const allCitizens = await citizensDB.getAllCitizens();
                const deceasedRecords = allCitizens.filter(record => record.isDeceased === true);

                if (deceasedRecords.length > 0) {
                    const record = deceasedRecords[0]; // أول سجل متوفى
                    fillDataFromRecord(record);
                    alert(`✅ تم تحميل بيانات: ${record.personalName || record.firstNameAr} ${record.familyName || record.familyNameAr}`);
                } else {
                    alert('❌ لا توجد سجلات متوفين في قاعدة البيانات');
                }

            } catch (error) {
                console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
                alert('❌ خطأ في الاتصال بقاعدة البيانات: ' + error.message);
            }
        }

        // ملء البيانات من سجل قاعدة البيانات
        function fillDataFromRecord(record) {
            console.log('🚀 [الخطوة 1] بدء نقل البيانات من قاعدة البيانات إلى النموذج...');
            console.log('📊 البيانات المستلمة:', record);

            let successCount = 0;
            let failureCount = 0;
            const results = [];

            try {
                // بيانات الوفاة
                const deathInfo = record.deathInfo || {};

                // ملء الحقول في النموذج مع تتبع النجاح/الفشل

                // 1. مكان الوفاة
                try {
                    const deathPlace = deathInfo.deathPlace || '';
                    document.getElementById('deathPlaceAr').textContent = deathPlace;
                    results.push(`✅ مكان الوفاة: "${deathPlace}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ مكان الوفاة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 2. تاريخ الوفاة
                try {
                    const deathDate = formatArabicDate(deathInfo.deathDate) || '';
                    document.getElementById('deathDateAr').textContent = deathDate;
                    results.push(`✅ تاريخ الوفاة: "${deathDate}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ تاريخ الوفاة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 3. الاسم الشخصي
                try {
                    const firstName = record.personalName || record.firstNameAr || '';
                    document.getElementById('firstNameAr').textContent = firstName;
                    results.push(`✅ الاسم الشخصي: "${firstName}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ الاسم الشخصي: فشل - ${e.message}`);
                    failureCount++;
                }

                // 4. الاسم العائلي
                try {
                    const familyName = record.familyName || record.familyNameAr || '';
                    document.getElementById('familyNameAr').textContent = familyName;
                    results.push(`✅ الاسم العائلي: "${familyName}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ الاسم العائلي: فشل - ${e.message}`);
                    failureCount++;
                }

                // 5. تاريخ الولادة
                try {
                    const birthDate = formatArabicDate(record.birthDate) || '';
                    document.getElementById('birthDateAr').textContent = birthDate;
                    results.push(`✅ تاريخ الولادة: "${birthDate}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ تاريخ الولادة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 6. مكان الولادة
                try {
                    const birthPlace = record.birthPlace || record.birthPlaceAr || '';
                    document.getElementById('birthPlaceAr').textContent = birthPlace;
                    results.push(`✅ مكان الولادة: "${birthPlace}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ مكان الولادة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 7. المهنة
                try {
                    const profession = record.profession || '';
                    document.getElementById('professionAr').textContent = profession;
                    results.push(`✅ المهنة: "${profession}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ المهنة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 8. الساكن
                try {
                    const residenceElement = document.getElementById('residenceAr');
                    if (residenceElement) {
                        const residence = record.residence || record.birthPlace || record.birthPlaceAr || '';
                        residenceElement.textContent = residence;
                        results.push(`✅ الساكن: "${residence}"`);
                        successCount++;
                    } else {
                        results.push(`❌ الساكن: عنصر غير موجود`);
                        failureCount++;
                    }
                } catch (e) {
                    results.push(`❌ الساكن: فشل - ${e.message}`);
                    failureCount++;
                }

                // 9. اسم الوالد
                try {
                    const fatherName = record.fatherName || record.fatherNameAr || '';
                    document.getElementById('fatherNameAr').textContent = fatherName;
                    results.push(`✅ اسم الوالد: "${fatherName}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ اسم الوالد: فشل - ${e.message}`);
                    failureCount++;
                }

                // 10. اسم الوالدة
                try {
                    const motherName = record.motherName || record.motherNameAr || '';
                    document.getElementById('motherNameAr').textContent = motherName;
                    results.push(`✅ اسم الوالدة: "${motherName}"`);
                    successCount++;
                } catch (e) {
                    results.push(`❌ اسم الوالدة: فشل - ${e.message}`);
                    failureCount++;
                }

                // 11. التصديق
                try {
                    const certificationElement = document.getElementById('certificationAr');
                    if (certificationElement) {
                        certificationElement.textContent = 'ضابط الحالة المدنية - أيير';
                        results.push(`✅ التصديق: "ضابط الحالة المدنية - أيير"`);
                        successCount++;
                    } else {
                        results.push(`❌ التصديق: عنصر غير موجود`);
                        failureCount++;
                    }
                } catch (e) {
                    results.push(`❌ التصديق: فشل - ${e.message}`);
                    failureCount++;
                }

                // عرض النتائج
                console.log('📊 [الخطوة 1] تفاصيل نقل البيانات:');
                results.forEach(result => console.log(result));

                const totalFields = successCount + failureCount;
                const successRate = ((successCount / totalFields) * 100).toFixed(1);

                if (failureCount === 0) {
                    console.log(`🎉 [الخطوة 1] نجح بالكامل! تم نقل جميع البيانات (${successCount}/${totalFields}) بنسبة ${successRate}%`);
                    alert(`🎉 نجح نقل البيانات بالكامل!\n\n✅ تم نقل ${successCount} حقل بنجاح\n📊 نسبة النجاح: ${successRate}%\n\n🎯 جميع البيانات منقولة بسلاسة إلى النموذج!`);
                } else {
                    console.log(`⚠️ [الخطوة 1] نجح جزئياً: ${successCount} نجح، ${failureCount} فشل من أصل ${totalFields}`);
                    alert(`⚠️ نقل البيانات نجح جزئياً\n\n✅ نجح: ${successCount} حقل\n❌ فشل: ${failureCount} حقل\n📊 نسبة النجاح: ${successRate}%\n\nتحقق من وحدة التحكم للتفاصيل`);
                }

            } catch (error) {
                console.error('❌ [الخطوة 1] فشل كامل في نقل البيانات:', error);
                alert(`❌ فشل كامل في نقل البيانات!\n\nخطأ: ${error.message}\n\nتحقق من وحدة التحكم للتفاصيل`);
            }
        }

        // وظائف مساعدة للتنسيق والترجمة

        // حساب العمر
        function calculateAge(birthDate, deathDate) {
            if (!birthDate || !deathDate) return null;

            try {
                const birth = new Date(birthDate);
                const death = new Date(deathDate);

                if (isNaN(birth.getTime()) || isNaN(death.getTime())) return null;

                let age = death.getFullYear() - birth.getFullYear();
                const monthDiff = death.getMonth() - birth.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && death.getDate() < birth.getDate())) {
                    age--;
                }

                return age >= 0 ? age : null;
            } catch (error) {
                console.error('خطأ في حساب العمر:', error);
                return null;
            }
        }

        // تنسيق التاريخ بالعربية
        function formatArabicDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };

                return date.toLocaleDateString('ar-MA', options);
            } catch (error) {
                console.error('خطأ في تنسيق التاريخ العربي:', error);
                return dateString;
            }
        }

        // تنسيق التاريخ بالفرنسية
        function formatFrenchDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };

                return date.toLocaleDateString('fr-FR', options);
            } catch (error) {
                console.error('خطأ في تنسيق التاريخ الفرنسي:', error);
                return dateString;
            }
        }

        // ترجمة الجنس
        function translateGender(gender) {
            const translations = {
                'ذكر': 'Masculin',
                'أنثى': 'Féminin',
                'male': 'Masculin',
                'female': 'Féminin'
            };
            return translations[gender] || gender;
        }

        // ترجمة الحالة العائلية
        function translateMaritalStatus(status) {
            const translations = {
                'متزوج': 'Marié',
                'متزوجة': 'Mariée',
                'أعزب': 'Célibataire',
                'عزباء': 'Célibataire',
                'مطلق': 'Divorcé',
                'مطلقة': 'Divorcée',
                'أرمل': 'Veuf',
                'أرملة': 'Veuve'
            };
            return translations[status] || status;
        }

        // ترجمة عامة للفرنسية
        function translateToFrench(text) {
            if (!text) return '';

            const translations = {
                // الأماكن
                'الرباط': 'Rabat',
                'الدار البيضاء': 'Casablanca',
                'فاس': 'Fès',
                'مراكش': 'Marrakech',
                'أيير': 'Ayir',
                'أسفي': 'Safi',

                // المهن
                'مزارع': 'Agriculteur',
                'معلم': 'Enseignant',
                'طبيب': 'Médecin',
                'مهندس': 'Ingénieur',
                'تاجر': 'Commerçant',
                'عامل': 'Ouvrier',
                'موظف': 'Employé',
                'متقاعد': 'Retraité',

                // أسباب الوفاة
                'مرض طبيعي': 'Maladie naturelle',
                'حادث': 'Accident',
                'مرض مزمن': 'Maladie chronique',
                'شيخوخة': 'Vieillesse',
                'قلب': 'Cardiaque',
                'سرطان': 'Cancer'
            };

            return translations[text] || text;
        }

        // تحميل البيانات من URL عند تحميل الصفحة
        async function loadDataFromURL() {
            try {
                const urlParams = new URLSearchParams(window.location.search);

                // التحقق من وجود معرف المواطن
                if (urlParams.has('id')) {
                    const citizenId = decodeURIComponent(urlParams.get('id'));

                    // التحقق من جاهزية قاعدة البيانات
                    if (dbReady) {
                        console.log('📊 قاعدة البيانات جاهزة، تحميل البيانات مباشرة...');
                        await loadCitizenDataById(citizenId);
                    } else {
                        console.log('⏳ قاعدة البيانات غير جاهزة، إضافة الطلب للقائمة المعلقة...');
                        pendingDataLoad = async () => {
                            await loadCitizenDataById(citizenId);
                        };
                    }
                    return;
                }

                // تحميل البيانات من المعاملات المباشرة
                if (urlParams.has('firstNameAr') || urlParams.has('personalName')) {
                    fillDataFromURLParams(urlParams);
                }

            } catch (error) {
                console.error('خطأ في تحميل البيانات من URL:', error);
            }
        }

        // ملء البيانات من معاملات URL
        function fillDataFromURLParams(urlParams) {
            // البيانات العربية
            document.getElementById('firstNameAr').textContent = urlParams.get('firstNameAr') || urlParams.get('personalName') || '';
            document.getElementById('familyNameAr').textContent = urlParams.get('familyNameAr') || urlParams.get('familyName') || '';
            document.getElementById('birthDateAr').textContent = formatArabicDate(urlParams.get('birthDate')) || '';
            document.getElementById('birthPlaceAr').textContent = urlParams.get('birthPlaceAr') || urlParams.get('birthPlace') || '';
            document.getElementById('genderAr').textContent = urlParams.get('gender') || '';
            document.getElementById('fatherNameAr').textContent = urlParams.get('fatherNameAr') || urlParams.get('fatherName') || '';
            document.getElementById('motherNameAr').textContent = urlParams.get('motherNameAr') || urlParams.get('motherName') || '';
            document.getElementById('actNumberAr').textContent = urlParams.get('actNumber') || '';

            // بيانات الوفاة
            document.getElementById('deathDateAr').textContent = formatArabicDate(urlParams.get('deathDate')) || '';
            document.getElementById('deathPlaceAr').textContent = urlParams.get('deathPlace') || '';
            document.getElementById('ageAr').textContent = urlParams.get('age') || '';
            document.getElementById('professionAr').textContent = urlParams.get('profession') || '';
            document.getElementById('maritalStatusAr').textContent = urlParams.get('maritalStatus') || '';
            document.getElementById('deathCauseAr').textContent = urlParams.get('deathCause') || '';

            // البيانات الفرنسية
            document.getElementById('firstNameFr').textContent = urlParams.get('firstNameFr') || urlParams.get('personalName') || '';
            document.getElementById('familyNameFr').textContent = urlParams.get('familyNameFr') || urlParams.get('familyName') || '';
            document.getElementById('birthDateFr').textContent = formatFrenchDate(urlParams.get('birthDate')) || '';
            document.getElementById('birthPlaceFr').textContent = urlParams.get('birthPlaceFr') || translateToFrench(urlParams.get('birthPlace')) || '';
            document.getElementById('genderFr').textContent = translateGender(urlParams.get('gender')) || '';
            document.getElementById('fatherNameFr').textContent = urlParams.get('fatherNameFr') || urlParams.get('fatherName') || '';
            document.getElementById('motherNameFr').textContent = urlParams.get('motherNameFr') || urlParams.get('motherName') || '';
            document.getElementById('actNumberFr').textContent = urlParams.get('actNumber') || '';

            // بيانات الوفاة بالفرنسية
            document.getElementById('deathDateFr').textContent = formatFrenchDate(urlParams.get('deathDate')) || '';
            document.getElementById('deathPlaceFr').textContent = translateToFrench(urlParams.get('deathPlace')) || '';
            document.getElementById('ageFr').textContent = urlParams.get('age') || '';
            document.getElementById('professionFr').textContent = translateToFrench(urlParams.get('profession')) || '';
            document.getElementById('maritalStatusFr').textContent = translateMaritalStatus(urlParams.get('maritalStatus')) || '';
            document.getElementById('deathCauseFr').textContent = translateToFrench(urlParams.get('deathCause')) || '';
        }

        // تحميل بيانات مواطن بالمعرف
        async function loadCitizenDataById(citizenId) {
            try {
                console.log('🔍 البحث عن المواطن بالمعرف:', citizenId);

                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    console.log('⚙️ تهيئة قاعدة البيانات...');
                    await citizensDB.init();
                }

                // الحصول على بيانات المواطن
                const record = await citizensDB.getCitizen(citizenId, true);

                if (record && record.isDeceased) {
                    console.log('✅ تم العثور على المواطن المتوفى:', record);
                    fillDataFromRecord(record);
                } else if (record && !record.isDeceased) {
                    alert('⚠️ هذا المواطن ليس متوفى');
                } else {
                    alert('❌ السجل غير موجود');
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن:', error);
                alert('❌ خطأ في تحميل بيانات المواطن: ' + error.message);
            }
        }

        // وظيفة الطباعة
        function printCertificate() {
            console.log('🖨️ بدء عملية الطباعة...');

            // التحقق من وجود بيانات في النموذج
            const hasData = checkIfFormHasData();

            if (!hasData) {
                const confirmPrint = confirm('⚠️ النموذج فارغ!\n\nهل تريد طباعة النموذج الفارغ؟');
                if (!confirmPrint) {
                    console.log('❌ تم إلغاء الطباعة - النموذج فارغ');
                    return;
                }
            }

            // تحديث تاريخ الطباعة وتاريخ الإصدار
            updatePrintDate();
            updateCurrentDate();

            // حفظ العنوان الأصلي
            const originalTitle = document.title;

            // تغيير العنوان إلى فراغ لإخفائه من الطباعة
            document.title = '';

            // إخفاء الأزرار مؤقت<|im_start|>
            const printControls = document.querySelector('.print-controls');
            const originalDisplay = printControls.style.display;
            printControls.style.display = 'none';

            // طباعة النموذج
            try {
                window.print();
                console.log('✅ تم إرسال النموذج للطباعة');
            } catch (error) {
                console.error('❌ خطأ في الطباعة:', error);
                alert('❌ حدث خطأ أثناء الطباعة');
            } finally {
                // إعادة العنوان الأصلي
                document.title = originalTitle;

                // إعادة إظهار الأزرار
                setTimeout(() => {
                    printControls.style.display = originalDisplay;
                }, 1000);
            }
        }

        // التحقق من وجود بيانات في النموذج
        function checkIfFormHasData() {
            const fieldsToCheck = [
                'deathPlaceAr', 'deathDateAr', 'firstNameAr', 'familyNameAr',
                'birthDateAr', 'birthPlaceAr', 'professionAr', 'residenceAr',
                'fatherNameAr', 'motherNameAr'
            ];

            for (const fieldId of fieldsToCheck) {
                const element = document.getElementById(fieldId);
                if (element && element.textContent.trim() !== '') {
                    return true;
                }
            }
            return false;
        }

        // تحديث تاريخ الطباعة (دالة محسنة وموحدة)
        function updatePrintDate() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'Africa/Casablanca' // توقيت المغرب
            };

            const arabicDate = now.toLocaleDateString('ar-MA', options);
            const printDateElement = document.getElementById('printDate');

            if (printDateElement) {
                printDateElement.textContent = arabicDate;
                console.log('📅 تم تحديث تاريخ الطباعة:', arabicDate);
            } else {
                console.warn('⚠️ عنصر printDate غير موجود');
            }
        }

        // تحديث التاريخ الحالي أمام "أيير في"
        function updateCurrentDate() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };

            const arabicDate = now.toLocaleDateString('ar-MA', options);
            const currentDateElement = document.getElementById('currentDate');

            if (currentDateElement) {
                currentDateElement.textContent = arabicDate;
                console.log('📅 تم تحديث التاريخ الحالي:', arabicDate);
            }
        }

        // ===== دوال مساعدة محسنة للطباعة =====

        // عرض رسالة تأكيد محسنة
        function showConfirmDialog(title, message) {
            return new Promise((resolve) => {
                const result = confirm(`${title}\n\n${message}`);
                resolve(result);
            });
        }

        // عرض رسالة خطأ محسنة
        function showErrorDialog(title, message) {
            return new Promise((resolve) => {
                alert(`❌ ${title}\n\n${message}`);
                resolve();
            });
        }

        // تحضير بيئة الطباعة
        async function preparePrintEnvironment() {
            console.log('🔧 تحضير بيئة الطباعة...');

            // حفظ العنوان الأصلي
            window.originalTitle = document.title;

            // تغيير العنوان لإخفائه من الطباعة
            document.title = '';

            // إضافة فئة CSS للطباعة
            document.body.classList.add('printing');

            console.log('✅ تم تحضير بيئة الطباعة');
        }

        // تحسين جودة الطباعة
        function optimizePrintQuality() {
            console.log('🎨 تحسين جودة الطباعة...');

            // تحسين جودة الألوان
            document.documentElement.style.webkitPrintColorAdjust = 'exact';
            document.documentElement.style.printColorAdjust = 'exact';

            // تحسين جودة النصوص
            document.body.style.webkitFontSmoothing = 'antialiased';
            document.body.style.mozOsxFontSmoothing = 'grayscale';

            console.log('✅ تم تحسين جودة الطباعة');
        }

        // إخفاء عناصر التحكم
        function hideControlElements() {
            console.log('🙈 إخفاء عناصر التحكم...');

            const elementsToHide = [
                '.print-controls',
                '.controls',
                'button',
                '.no-print'
            ];

            const hiddenElements = [];

            elementsToHide.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element.style.display !== 'none') {
                        hiddenElements.push({
                            element: element,
                            originalDisplay: element.style.display || 'block'
                        });
                        element.style.display = 'none';
                    }
                });
            });

            console.log(`✅ تم إخفاء ${hiddenElements.length} عنصر`);
            return hiddenElements;
        }

        // معالجة ما بعد الطباعة
        async function handleAfterPrint(hiddenElements) {
            console.log('🔄 معالجة ما بعد الطباعة...');

            // انتظار قصير قبل استعادة العناصر
            await new Promise(resolve => setTimeout(resolve, 1000));

            // استعادة العنوان الأصلي
            if (window.originalTitle) {
                document.title = window.originalTitle;
            }

            // إزالة فئة CSS للطباعة
            document.body.classList.remove('printing');

            // استعادة عرض العناصر المخفية
            hiddenElements.forEach(item => {
                item.element.style.display = item.originalDisplay;
            });

            console.log('✅ تمت معالجة ما بعد الطباعة');
        }

        // دالة طباعة محسنة مع معاينة
        async function printWithPreview() {
            console.log('🔍 بدء الطباعة مع المعاينة...');

            try {
                // تحضير الطباعة
                await preparePrintEnvironment();
                optimizePrintQuality();

                // تحديث التواريخ
                updatePrintDate();
                updateCurrentDate();

                // عرض معاينة
                const preview = confirm('🔍 هل تريد معاينة الطباعة أولاً؟\n\nاضغط "موافق" للمعاينة أو "إلغاء" للطباعة مباشرة.');

                if (preview) {
                    // فتح نافذة معاينة
                    const previewWindow = window.open('', '_blank', 'width=800,height=600');
                    previewWindow.document.write(document.documentElement.outerHTML);
                    previewWindow.document.close();

                    console.log('🔍 تم فتح نافذة المعاينة');
                } else {
                    // طباعة مباشرة
                    const hiddenElements = hideControlElements();
                    window.print();
                    await handleAfterPrint(hiddenElements);
                }

            } catch (error) {
                console.error('❌ خطأ في الطباعة مع المعاينة:', error);
                await showErrorDialog('خطأ في الطباعة', error.message);
            }
        }

        // دالة طباعة سريعة
        async function quickPrint() {
            console.log('⚡ بدء الطباعة السريعة...');

            try {
                await preparePrintEnvironment();
                optimizePrintQuality();
                updatePrintDate();
                updateCurrentDate();

                const hiddenElements = hideControlElements();
                window.print();
                await handleAfterPrint(hiddenElements);

                console.log('✅ تمت الطباعة السريعة بنجاح');

            } catch (error) {
                console.error('❌ خطأ في الطباعة السريعة:', error);
                await showErrorDialog('خطأ في الطباعة', error.message);
            }
        }

        // تهيئة الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل صفحة نسخة موجزة من رسم الوفاة...');

            // مسح النموذج ليصبح ورقة بيضاء
            clearAllFields();

            // تحديث التاريخ الحالي عند تحميل الصفحة
            updateCurrentDate();

            // تحميل البيانات من URL إذا كانت موجودة
            loadDataFromURL();

            // إضافة مستمعي أحداث الطباعة
            window.addEventListener('beforeprint', function() {
                console.log('🖨️ بدء عملية الطباعة...');
                optimizePrintQuality();
            });

            window.addEventListener('afterprint', function() {
                console.log('✅ انتهت عملية الطباعة');
            });

            console.log('✅ تم تهيئة الصفحة بنجاح');
        });





        console.log('📋 تم تحميل نموذج نسخة موجزة من رسم الوفاة بنجاح');
    </script>
</body>
</html>
