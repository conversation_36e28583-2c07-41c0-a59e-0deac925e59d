<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار التنقل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a 0%, #e74c3c 50%, #c41e3a 100%);
        }

        .header-main {
            padding: 25px 0;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            border: 3px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .header-text h1 {
            font-size: 2em;
            margin: 0 0 8px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 5px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        /* قسم أزرار الانتقال */
        .header-navigation-section {
            background: rgba(0,0,0,0.15);
            border-top: 1px solid rgba(255,255,255,0.1);
            padding: 12px 0;
            position: relative;
        }

        .header-navigation-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        .header-navigation {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            flex-wrap: nowrap;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .header-navigation::-webkit-scrollbar {
            display: none;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 12px 18px;
            border: none;
            border-radius: 30px;
            text-decoration: none;
            font-size: 0.9em;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            min-width: 140px;
            justify-content: center;
            text-align: center;
            flex-shrink: 0;
            white-space: nowrap;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.35);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.25);
            border-color: rgba(255,255,255,0.5);
        }

        .nav-btn:active {
            transform: translateY(-1px);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-color: rgba(231, 76, 60, 1);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
            transform: translateY(-2px);
        }

        .nav-btn.active:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.5);
        }

        .nav-btn .icon {
            font-size: 1.1em;
            filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.3));
        }

        .nav-btn span:last-child {
            font-weight: 600;
            letter-spacing: 0.3px;
        }

        /* تحسين الأزرار النشطة */
        .nav-btn.active .icon {
            animation: pulse-icon 2s ease-in-out infinite;
        }

        @keyframes pulse-icon {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .content {
            flex: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .test-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .test-info h2 {
            margin-bottom: 10px;
            font-size: 1.5em;
        }

        .features-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .features-list h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .features-list li::before {
            content: '✅';
            font-size: 1.2em;
        }

        /* تحسينات للشاشات الكبيرة */
        @media (min-width: 1400px) {
            .header-navigation {
                gap: 20px;
            }
            
            .nav-btn {
                min-width: 160px;
                padding: 14px 20px;
                font-size: 1em;
            }
        }

        @media (max-width: 992px) {
            .header-navigation {
                gap: 12px;
            }
            
            .nav-btn {
                min-width: 130px;
                padding: 10px 16px;
                font-size: 0.85em;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 1.6em;
            }

            .header-navigation {
                gap: 8px;
                padding: 0 10px;
                justify-content: flex-start;
            }

            .nav-btn {
                padding: 8px 12px;
                font-size: 0.8em;
                min-width: 110px;
                gap: 6px;
            }

            .nav-btn .icon {
                font-size: 1em;
            }

            .content {
                padding: 20px 15px;
                margin: 15px;
            }

            .morocco-emblem {
                width: 60px;
                height: 60px;
                font-size: 1.8em;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-main">
            <div class="header-content">
                <div class="header-right">
                    <div class="morocco-emblem">🇲🇦</div>
                    <div class="header-text">
                        <h1>نظام إدارة الحالة المدنية</h1>
                        <div class="subtitle">مكتب الحالة المدنية - أيير</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم أزرار الانتقال -->
        <div class="header-navigation-section">
            <div class="header-content">
                <div class="header-navigation">
                    <a href="#" class="nav-btn active">
                        <span class="icon">🏠</span>
                        <span>الرئيسية</span>
                    </a>
                    <a href="#" class="nav-btn">
                        <span class="icon">🗃️</span>
                        <span>إدارة البيانات</span>
                    </a>
                    <a href="#" class="nav-btn">
                        <span class="icon">🔍</span>
                        <span>البحث</span>
                    </a>
                    <a href="#" class="nav-btn">
                        <span class="icon">⚱️</span>
                        <span>بيانات الوفاة</span>
                    </a>
                    <a href="#" class="nav-btn">
                        <span class="icon">🆔</span>
                        <span>البطاقة الشخصية</span>
                    </a>
                    <a href="#" class="nav-btn">
                        <span class="icon">🔐</span>
                        <span>تسجيل الدخول</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="test-info">
            <h2>🎯 اختبار أزرار التنقل المحسنة</h2>
            <p>تم تحسين أزرار التنقل لتظهر في صف واحد مع تصميم متجاوب</p>
        </div>

        <div class="features-list">
            <h3>🚀 المميزات المطبقة:</h3>
            <ul>
                <li>جميع الأزرار في صف واحد أفقي</li>
                <li>تصميم متجاوب يتكيف مع جميع أحجام الشاشات</li>
                <li>تمرير أفقي مخفي للشاشات الصغيرة</li>
                <li>تأثيرات بصرية جميلة عند التمرير</li>
                <li>أيقونة متحركة للزر النشط</li>
                <li>تحسين المسافات والأحجام</li>
                <li>خط مضيء في أعلى قسم التنقل</li>
                <li>تأثيرات الظلال والشفافية</li>
                <li>تحسين الألوان والتدرجات</li>
                <li>دعم كامل للغة العربية</li>
            </ul>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <p style="color: #7f8c8d; font-style: italic;">
                💡 جرب تغيير حجم النافذة لرؤية التصميم المتجاوب
            </p>
        </div>
    </div>

    <script>
        // إضافة تفاعل للأزرار
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // إزالة الفئة النشطة من جميع الأزرار
                document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));
                
                // إضافة الفئة النشطة للزر المنقور
                this.classList.add('active');
                
                // تأثير بصري
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // تحديث الوقت (اختياري)
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            console.log('الوقت الحالي:', timeString);
        }

        // تحديث كل دقيقة
        updateTime();
        setInterval(updateTime, 60000);
    </script>
</body>
</html>
