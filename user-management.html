<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة الحالة المدنية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .nav-link {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .nav-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1em;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            direction: rtl;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .users-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }

        .users-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .role-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .role-admin {
            background: #fff3cd;
            color: #856404;
        }

        .role-manager {
            background: #d1ecf1;
            color: #0c5460;
        }

        .role-employee {
            background: #e2e3e5;
            color: #383d41;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 600;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .modal-title {
            font-size: 1.5em;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close {
            background: none;
            border: none;
            font-size: 2em;
            cursor: pointer;
            color: #7f8c8d;
            transition: color 0.3s ease;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close:hover {
            color: #e74c3c;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 0.8em;
            margin: 0 2px;
        }

        .btn-edit {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .btn-edit:hover {
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-delete {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .btn-delete:hover {
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-toggle {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .btn-toggle:hover {
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        }

        .btn-activate {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn-activate:hover {
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }

        .confirmation-text {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid #ffeaa7;
        }

        .user-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid #e9ecef;
        }

        .user-info strong {
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .nav-links {
                flex-direction: column;
                align-items: center;
            }

            .users-table {
                font-size: 0.9em;
            }

            .users-table th,
            .users-table td {
                padding: 8px;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 20px;
            }

            .btn-small {
                padding: 4px 8px;
                font-size: 0.7em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>
                <span>👥</span>
                إدارة المستخدمين
            </h1>
            <p>إدارة حسابات المستخدمين وصلاحياتهم في نظام الحالة المدنية</p>

            <div class="nav-links">
                <a href="main-dashboard.html" class="nav-link">🏠 الصفحة الرئيسية</a>
                <a href="citizens-database-indexeddb.html" class="nav-link">🗃️ إدارة البيانات</a>
                <a href="search-citizens.html" class="nav-link">🔍 البحث</a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">0</div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeUsers">0</div>
                <div class="stat-label">المستخدمين النشطين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="adminUsers">0</div>
                <div class="stat-label">المديرين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="onlineUsers">1</div>
                <div class="stat-label">متصل الآن</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Add User Form -->
            <div class="card">
                <h2>
                    <span>➕</span>
                    إضافة مستخدم جديد
                </h2>

                <div id="alertContainer"></div>

                <form id="addUserForm">
                    <div class="form-group">
                        <label for="fullName" class="form-label">الاسم الكامل</label>
                        <input type="text" id="fullName" name="fullName" class="form-input"
                               placeholder="أدخل الاسم الكامل" required>
                    </div>

                    <div class="form-group">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" id="username" name="username" class="form-input"
                               placeholder="أدخل اسم المستخدم" required>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" class="form-input"
                               placeholder="أدخل البريد الإلكتروني" required>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" id="password" name="password" class="form-input"
                               placeholder="أدخل كلمة المرور" required>
                    </div>

                    <div class="form-group">
                        <label for="role" class="form-label">الدور</label>
                        <select id="role" name="role" class="form-select" required>
                            <option value="">اختر الدور</option>
                            <option value="admin">مدير النظام</option>
                            <option value="manager">مدير</option>
                            <option value="employee">موظف</option>
                            <option value="viewer">مشاهد</option>
                        </select>
                    </div>

                    <button type="submit" class="btn">
                        <span>➕</span>
                        إضافة المستخدم
                    </button>
                </form>
            </div>

            <!-- Users List -->
            <div class="card">
                <h2>
                    <span>📋</span>
                    قائمة المستخدمين
                </h2>

                <div style="overflow-x: auto;">
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">
                    <span>✏️</span>
                    تعديل المستخدم
                </h2>
                <button class="close" onclick="closeEditModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">

                    <div class="form-group">
                        <label for="editFullName" class="form-label">الاسم الكامل</label>
                        <input type="text" id="editFullName" name="fullName" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="editUsername" class="form-label">اسم المستخدم</label>
                        <input type="text" id="editUsername" name="username" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="editEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" id="editEmail" name="email" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label for="editRole" class="form-label">الدور</label>
                        <select id="editRole" name="role" class="form-select" required>
                            <option value="admin">مدير النظام</option>
                            <option value="manager">مدير</option>
                            <option value="employee">موظف</option>
                            <option value="viewer">مشاهد</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="editPassword" class="form-label">كلمة المرور الجديدة (اختياري)</label>
                        <input type="password" id="editPassword" name="password" class="form-input"
                               placeholder="اترك فارغاً إذا لم ترد تغييرها">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeEditModal()">إلغاء</button>
                <button type="submit" form="editUserForm" class="btn btn-success">حفظ التغييرات</button>
            </div>
        </div>
    </div>

    <!-- Delete User Modal -->
    <div id="deleteUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">
                    <span>🗑️</span>
                    حذف المستخدم
                </h2>
                <button class="close" onclick="closeDeleteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="confirmation-text">
                    <strong>⚠️ تحذير:</strong> هذه العملية غير قابلة للتراجع!
                </div>
                <div class="user-info" id="deleteUserInfo">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
                <p>هل أنت متأكد من رغبتك في حذف هذا المستخدم؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteUser()">حذف نهائياً</button>
            </div>
        </div>
    </div>

    <!-- Include User Management System -->
    <script src="user-management.js"></script>
    <script src="auth-guard.js"></script>

    <script>
        // متغيرات عامة
        let users = [];

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // تهيئة نظام المستخدمين
                await userManager.init();

                // فحص الصلاحيات
                if (!userManager.hasPermission('manage_users')) {
                    showAlert('ليس لديك صلاحية لإدارة المستخدمين', 'error');
                    setTimeout(() => {
                        window.location.href = 'main-dashboard.html';
                    }, 2000);
                    return;
                }

                // تحميل المستخدمين
                await loadUsers();
                updateStatistics();

            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);
                showAlert('خطأ في تهيئة الصفحة: ' + error.message, 'error');
            }
        });

        // تحميل المستخدمين
        async function loadUsers() {
            try {
                users = await userManager.getAllUsers();
                displayUsers();
                console.log(`تم تحميل ${users.length} مستخدم`);
            } catch (error) {
                console.error('خطأ في تحميل المستخدمين:', error);
                showAlert('خطأ في تحميل المستخدمين: ' + error.message, 'error');
            }
        }

        // عرض المستخدمين في الجدول
        function displayUsers() {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                const isCurrentUser = userManager.getCurrentUser()?.id === user.id;

                row.innerHTML = `
                    <td>${user.fullName}</td>
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td><span class="role-badge role-${user.role}">${getRoleText(user.role)}</span></td>
                    <td><span class="status-badge ${user.isActive ? 'status-active' : 'status-inactive'}">${user.isActive ? 'نشط' : 'معطل'}</span></td>
                    <td>${user.lastLogin ? formatDate(user.lastLogin) : 'لم يسجل دخول'}</td>
                    <td>
                        <button onclick="editUser(${user.id})"
                                class="btn btn-edit btn-small"
                                title="تعديل">
                            ✏️
                        </button>

                        <button onclick="toggleUserStatus(${user.id}, ${!user.isActive})"
                                class="btn ${user.isActive ? 'btn-toggle' : 'btn-activate'} btn-small"
                                title="${user.isActive ? 'تعطيل' : 'تفعيل'}">
                            ${user.isActive ? '🚫' : '✅'}
                        </button>

                        ${!isCurrentUser ? `
                            <button onclick="deleteUser(${user.id})"
                                    class="btn btn-delete btn-small"
                                    title="حذف">
                                🗑️
                            </button>
                        ` : `
                            <span class="btn btn-small" style="background: #6c757d; cursor: not-allowed;" title="لا يمكن حذف حسابك">
                                🔒
                            </span>
                        `}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            document.getElementById('totalUsers').textContent = users.length;
            document.getElementById('activeUsers').textContent = users.filter(u => u.isActive).length;
            document.getElementById('adminUsers').textContent = users.filter(u => u.role === 'admin').length;
        }

        // معالج إضافة مستخدم جديد
        document.getElementById('addUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const userData = {
                fullName: formData.get('fullName'),
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password'),
                role: formData.get('role'),
                permissions: getPermissionsByRole(formData.get('role'))
            };

            try {
                await userManager.addUser(userData);
                showAlert('تم إضافة المستخدم بنجاح', 'success');

                // إعادة تعيين النموذج
                e.target.reset();

                // إعادة تحميل المستخدمين
                await loadUsers();
                updateStatistics();

            } catch (error) {
                console.error('خطأ في إضافة المستخدم:', error);
                showAlert('خطأ في إضافة المستخدم: ' + error.message, 'error');
            }
        });

        // تبديل حالة المستخدم
        async function toggleUserStatus(userId, newStatus) {
            try {
                await userManager.updateUserStatus(userId, newStatus);
                showAlert(`تم ${newStatus ? 'تفعيل' : 'تعطيل'} المستخدم بنجاح`, 'success');

                // إعادة تحميل المستخدمين
                await loadUsers();
                updateStatistics();

            } catch (error) {
                console.error('خطأ في تحديث حالة المستخدم:', error);
                showAlert('خطأ في تحديث حالة المستخدم: ' + error.message, 'error');
            }
        }

        // الحصول على نص الدور
        function getRoleText(role) {
            const roles = {
                'admin': 'مدير النظام',
                'manager': 'مدير',
                'employee': 'موظف',
                'viewer': 'مشاهد'
            };
            return roles[role] || 'غير محدد';
        }

        // الحصول على الصلاحيات حسب الدور
        function getPermissionsByRole(role) {
            const permissions = {
                'admin': ['all'],
                'manager': ['manage_data', 'view_reports', 'manage_certificates'],
                'employee': ['add_data', 'edit_data', 'view_data'],
                'viewer': ['view_data']
            };
            return permissions[role] || ['view_data'];
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // تعديل المستخدم
        async function editUser(userId) {
            try {
                const user = await userManager.getUserById(userId);

                // ملء النموذج ببيانات المستخدم
                document.getElementById('editUserId').value = user.id;
                document.getElementById('editFullName').value = user.fullName;
                document.getElementById('editUsername').value = user.username;
                document.getElementById('editEmail').value = user.email;
                document.getElementById('editRole').value = user.role;
                document.getElementById('editPassword').value = ''; // فارغ دائماً

                // عرض النافذة المنبثقة
                document.getElementById('editUserModal').style.display = 'block';

            } catch (error) {
                console.error('خطأ في تحميل بيانات المستخدم:', error);
                showAlert('خطأ في تحميل بيانات المستخدم: ' + error.message, 'error');
            }
        }

        // إغلاق نافذة التعديل
        function closeEditModal() {
            document.getElementById('editUserModal').style.display = 'none';
            document.getElementById('editUserForm').reset();
        }

        // معالج تعديل المستخدم
        document.getElementById('editUserForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const userId = parseInt(document.getElementById('editUserId').value);
            const formData = new FormData(e.target);

            const userData = {
                fullName: formData.get('fullName'),
                username: formData.get('username'),
                email: formData.get('email'),
                role: formData.get('role'),
                permissions: getPermissionsByRole(formData.get('role'))
            };

            // إضافة كلمة المرور إذا تم إدخالها
            const password = formData.get('password');
            if (password && password.trim() !== '') {
                userData.password = password;
            }

            try {
                await userManager.updateUser(userId, userData);
                showAlert('تم تعديل المستخدم بنجاح', 'success');

                // إغلاق النافذة
                closeEditModal();

                // إعادة تحميل المستخدمين
                await loadUsers();
                updateStatistics();

            } catch (error) {
                console.error('خطأ في تعديل المستخدم:', error);
                showAlert('خطأ في تعديل المستخدم: ' + error.message, 'error');
            }
        });

        // حذف المستخدم
        let userToDelete = null;

        async function deleteUser(userId) {
            try {
                const user = await userManager.getUserById(userId);
                userToDelete = user;

                // ملء معلومات المستخدم
                document.getElementById('deleteUserInfo').innerHTML = `
                    <strong>الاسم:</strong> ${user.fullName}<br>
                    <strong>اسم المستخدم:</strong> ${user.username}<br>
                    <strong>البريد:</strong> ${user.email}<br>
                    <strong>الدور:</strong> ${getRoleText(user.role)}<br>
                    <strong>الحالة:</strong> ${user.isActive ? 'نشط' : 'معطل'}
                `;

                // عرض النافذة المنبثقة
                document.getElementById('deleteUserModal').style.display = 'block';

            } catch (error) {
                console.error('خطأ في تحميل بيانات المستخدم:', error);
                showAlert('خطأ في تحميل بيانات المستخدم: ' + error.message, 'error');
            }
        }

        // إغلاق نافذة الحذف
        function closeDeleteModal() {
            document.getElementById('deleteUserModal').style.display = 'none';
            userToDelete = null;
        }

        // تأكيد حذف المستخدم
        async function confirmDeleteUser() {
            if (!userToDelete) return;

            try {
                const result = await userManager.deleteUser(userToDelete.id);
                showAlert(`تم حذف المستخدم "${result.deletedUser.fullName}" بنجاح`, 'success');

                // إغلاق النافذة
                closeDeleteModal();

                // إعادة تحميل المستخدمين
                await loadUsers();
                updateStatistics();

            } catch (error) {
                console.error('خطأ في حذف المستخدم:', error);
                showAlert('خطأ في حذف المستخدم: ' + error.message, 'error');
            }
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const editModal = document.getElementById('editUserModal');
            const deleteModal = document.getElementById('deleteUserModal');

            if (event.target === editModal) {
                closeEditModal();
            }
            if (event.target === deleteModal) {
                closeDeleteModal();
            }
        };

        // عرض التنبيهات
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alertContainer');

            // إزالة التنبيهات السابقة
            container.innerHTML = '';

            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            container.appendChild(alert);

            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
