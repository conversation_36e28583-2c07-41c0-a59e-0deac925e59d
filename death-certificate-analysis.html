<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحليل شامل لشهادة الوفاة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .analysis-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .analysis-section h2 {
            color: #e74c3c;
            margin-bottom: 20px;
            font-size: 1.6em;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 10px;
        }

        .problem-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #e74c3c;
        }

        .problem-card.critical {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
        }

        .problem-card.warning {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fffbf0 0%, #ffffff 100%);
        }

        .problem-card.info {
            border-left-color: #17a2b8;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
        }

        .problem-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .issue-list {
            list-style: none;
            padding: 0;
        }

        .issue-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 1px solid rgba(231, 76, 60, 0.1);
        }

        .issue-list li:last-child {
            border-bottom: none;
        }

        .issue-list li.critical::before {
            content: '🚨';
            font-size: 1.2em;
        }

        .issue-list li.warning::before {
            content: '⚠️';
            font-size: 1.2em;
        }

        .issue-list li.info::before {
            content: 'ℹ️';
            font-size: 1.2em;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            font-weight: 600;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .comparison-table tr:hover {
            background: #ffe6e6;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }

        .highlight-error {
            background: #e74c3c;
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .highlight-fix {
            background: #27ae60;
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-family: 'Cairo', sans-serif;
            cursor: pointer;
            margin: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .severity-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .severity-critical {
            background: #dc3545;
            color: white;
        }

        .severity-high {
            background: #fd7e14;
            color: white;
        }

        .severity-medium {
            background: #ffc107;
            color: #212529;
        }

        .severity-low {
            background: #6c757d;
            color: white;
        }

        .recommendations {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .recommendations h3 {
            color: #155724;
            margin-bottom: 15px;
        }

        .recommendations ul {
            list-style: none;
            padding: 0;
        }

        .recommendations li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .recommendations li::before {
            content: '💡';
            font-size: 1.2em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .comparison-table {
                font-size: 0.8em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚱️ تحليل شامل لشهادة الوفاة</h1>
        
        <!-- ملخص المشاكل -->
        <div class="analysis-section">
            <h2>📊 ملخص المشاكل المكتشفة</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div style="background: #fff5f5; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid #dc3545;">
                    <h3 style="color: #dc3545; margin-bottom: 10px;">🚨 مشاكل حرجة</h3>
                    <div style="font-size: 2em; font-weight: bold; color: #dc3545;">5</div>
                </div>
                <div style="background: #fffbf0; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid #ffc107;">
                    <h3 style="color: #856404; margin-bottom: 10px;">⚠️ تحذيرات</h3>
                    <div style="font-size: 2em; font-weight: bold; color: #856404;">3</div>
                </div>
                <div style="background: #f0f9ff; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid #17a2b8;">
                    <h3 style="color: #0c5460; margin-bottom: 10px;">ℹ️ ملاحظات</h3>
                    <div style="font-size: 2em; font-weight: bold; color: #0c5460;">2</div>
                </div>
            </div>
        </div>

        <!-- المشاكل الحرجة -->
        <div class="analysis-section">
            <h2>🚨 المشاكل الحرجة</h2>
            
            <div class="problem-card critical">
                <h3>❌ إعدادات @page خاطئة تماماً</h3>
                <div class="code-block">
@page {
    size: <span class="highlight-error">A4 landscape</span>; /* خطأ! */
}

.document {
    width: <span class="highlight-error">148.5mm</span>; /* نصف A4 أفقي */
    height: <span class="highlight-error">190mm</span>; /* أكبر من A4 عمودي! */
}
                </div>
                <p><strong>المشكلة:</strong> الكود يحدد A4 أفقي (297mm × 210mm) لكن الحاوية 148.5mm × 190mm!</p>
                <p><strong>النتيجة:</strong> الطباعة ستكون مشوهة ومقطوعة.</p>
            </div>

            <div class="problem-card critical">
                <h3>📏 تضارب في الأبعاد</h3>
                <ul class="issue-list">
                    <li class="critical">العرض: 148.5mm (نصف A4 أفقي) لكن @page يحدد A4 أفقي كامل</li>
                    <li class="critical">الارتفاع: 190mm أكبر من A4 عمودي (210mm) مع الهوامش</li>
                    <li class="critical">لا يوجد تطابق بين حجم الورق والحاوية</li>
                </ul>
            </div>

            <div class="problem-card critical">
                <h3>🔤 أحجام الخطوط غير محسنة</h3>
                <div class="code-block">
body { font-size: <span class="highlight-error">12px</span>; } /* كبير للمساحة المحدودة */
.header { font-size: <span class="highlight-error">11px</span>; } /* كبير أيضاً */
                </div>
                <p>الخطوط كبيرة جداً للمساحة المحدودة المتاحة.</p>
            </div>

            <div class="problem-card critical">
                <h3>🎨 لا يوجد تصميم مزدوج اللغة</h3>
                <ul class="issue-list">
                    <li class="critical">عربي فقط (بخلاف عقد الازدياد المزدوج)</li>
                    <li class="critical">لا يتماشى مع المعايير الرسمية</li>
                    <li class="critical">صعوبة في الاستخدام الرسمي</li>
                </ul>
            </div>

            <div class="problem-card critical">
                <h3>🔄 دوال مكررة في JavaScript</h3>
                <div class="code-block">
// دالة updatePrintDate مكررة مرتين!
function updatePrintDate() { ... } // السطر 912
function updatePrintDate() { ... } // السطر 932 (مكررة!)
                </div>
            </div>
        </div>

        <!-- التحذيرات -->
        <div class="analysis-section">
            <h2>⚠️ التحذيرات</h2>
            
            <div class="problem-card warning">
                <h3>📐 هوامش مفرطة</h3>
                <div class="code-block">
body { padding: <span class="highlight-error">5mm</span>; }
.document { padding: <span class="highlight-error">6mm</span>; }
/* إجمالي: 11mm هوامش! */
                </div>
                <p>الهوامش المضاعفة تقلل المساحة المتاحة للمحتوى.</p>
            </div>

            <div class="problem-card warning">
                <h3>🎯 تخطيط غير محسن</h3>
                <ul class="issue-list">
                    <li class="warning">المسافات بين العناصر كبيرة</li>
                    <li class="warning">استخدام غير فعال للمساحة</li>
                    <li class="warning">التخطيط لا يناسب الطباعة على نصف ورقة</li>
                </ul>
            </div>

            <div class="problem-card warning">
                <h3>🖨️ إعدادات طباعة ناقصة</h3>
                <ul class="issue-list">
                    <li class="warning">لا توجد تحسينات للخطوط عند الطباعة</li>
                    <li class="warning">لا توجد تحسينات للمسافات عند الطباعة</li>
                    <li class="warning">إعدادات @page غير متطابقة مع الحاوية</li>
                </ul>
            </div>
        </div>

        <!-- مقارنة مع عقد الازدياد -->
        <div class="analysis-section">
            <h2>📊 مقارنة مع عقد الازدياد (المرجع)</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>عقد الازدياد ✅</th>
                        <th>شهادة الوفاة ❌</th>
                        <th>الفجوة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>حجم الورق</td>
                        <td>A5 أفقي (210×148mm)</td>
                        <td>A4 أفقي (297×210mm)</td>
                        <td class="severity-critical">تضارب كامل</td>
                    </tr>
                    <tr>
                        <td>أبعاد الحاوية</td>
                        <td>210×148mm (مطابق)</td>
                        <td>148.5×190mm (متضارب)</td>
                        <td class="severity-critical">غير متطابق</td>
                    </tr>
                    <tr>
                        <td>حجم الخط الأساسي</td>
                        <td>10px (محسن)</td>
                        <td>12px (كبير)</td>
                        <td class="severity-high">+20%</td>
                    </tr>
                    <tr>
                        <td>التصميم</td>
                        <td>مزدوج اللغة</td>
                        <td>عربي فقط</td>
                        <td class="severity-critical">مفقود</td>
                    </tr>
                    <tr>
                        <td>الهوامش</td>
                        <td>5mm محسن</td>
                        <td>11mm مفرط</td>
                        <td class="severity-medium">+120%</td>
                    </tr>
                    <tr>
                        <td>إعدادات الطباعة</td>
                        <td>محسنة ومتطابقة</td>
                        <td>متضاربة</td>
                        <td class="severity-critical">غير عملية</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- الحلول المقترحة -->
        <div class="recommendations">
            <h3>💡 الحلول المقترحة</h3>
            
            <h4>🔧 إصلاحات فورية (حرجة):</h4>
            <ul>
                <li>تصحيح إعدادات @page إلى A5 أفقي أو A4 عمودي</li>
                <li>توحيد أبعاد الحاوية مع حجم الورق المحدد</li>
                <li>تقليل أحجام الخطوط (10px بدلاً من 12px)</li>
                <li>إزالة الدالة المكررة updatePrintDate</li>
                <li>تقليل الهوامش المفرطة</li>
            </ul>
            
            <h4>📈 تحسينات مستقبلية:</h4>
            <ul>
                <li>إضافة تصميم مزدوج اللغة (عربي/فرنسي)</li>
                <li>تحسين التخطيط ليناسب المساحة المحدودة</li>
                <li>إضافة إعدادات طباعة محسنة</li>
                <li>توحيد التصميم مع عقد الازدياد</li>
            </ul>
        </div>

        <!-- تقييم الخطورة -->
        <div class="analysis-section">
            <h2>🎯 تقييم الخطورة</h2>
            <div style="background: #fff; padding: 20px; border-radius: 10px; border: 2px solid #dc3545;">
                <h3 style="color: #dc3545; text-align: center; margin-bottom: 20px;">
                    🚨 تقييم عام: خطر عالي
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div>
                        <strong>قابلية الاستخدام:</strong><br>
                        <span class="severity-critical">20%</span> - غير عملي
                    </div>
                    <div>
                        <strong>جودة الطباعة:</strong><br>
                        <span class="severity-critical">25%</span> - ضعيفة جداً
                    </div>
                    <div>
                        <strong>التوافق مع المعايير:</strong><br>
                        <span class="severity-critical">15%</span> - غير متوافق
                    </div>
                    <div>
                        <strong>سهولة الصيانة:</strong><br>
                        <span class="severity-high">40%</span> - صعبة
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px; font-size: 1.2em; font-weight: bold; color: #dc3545;">
                    📊 النتيجة الإجمالية: 25/100 - يحتاج إعادة كتابة شاملة
                </div>
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="analysis-section">
            <h2>🧪 اختبار المشاكل</h2>
            <div style="text-align: center;">
                <a href="death-certificate.html" class="btn">
                    ⚱️ فتح شهادة الوفاة الحالية
                </a>
                <button class="btn btn-warning" onclick="simulatePrintIssues()">
                    🖨️ محاكاة مشاكل الطباعة
                </button>
                <button class="btn btn-info" onclick="showFixPlan()">
                    🔧 خطة الإصلاح
                </button>
                <button class="btn btn-success" onclick="generateReport()">
                    📋 تقرير تفصيلي
                </button>
            </div>
        </div>
    </div>

    <script>
        function simulatePrintIssues() {
            const issues = [
                '🚨 تضارب الأبعاد: A4 أفقي vs 148.5×190mm',
                '📏 الحاوية أكبر من الورق المحدد',
                '🔤 الخطوط كبيرة جداً (12px)',
                '📐 هوامش مفرطة (11mm إجمالي)',
                '🎨 لا يوجد تصميم مزدوج اللغة',
                '🔄 دوال JavaScript مكررة',
                '🖨️ إعدادات طباعة غير محسنة'
            ];

            let report = '🖨️ محاكاة مشاكل الطباعة:\n\n';
            issues.forEach((issue, index) => {
                report += `${index + 1}. ${issue}\n`;
            });

            report += '\n💡 النتيجة المتوقعة:';
            report += '\n• الطباعة ستكون مقطوعة أو مشوهة';
            report += '\n• استخدام غير فعال للورق';
            report += '\n• صعوبة في القراءة';
            report += '\n• عدم توافق مع المعايير الرسمية';

            alert(report);
        }

        function showFixPlan() {
            const plan = `🔧 خطة الإصلاح الشاملة:

📋 المرحلة الأولى (حرجة):
1. تصحيح @page إلى A5 أفقي
2. توحيد أبعاد الحاوية (210×148mm)
3. تقليل أحجام الخطوط (10px)
4. إزالة الدوال المكررة
5. تحسين الهوامش (5mm)

📋 المرحلة الثانية (تحسينات):
1. إضافة تصميم مزدوج اللغة
2. تحسين التخطيط
3. إضافة إعدادات طباعة محسنة
4. توحيد التصميم مع عقد الازدياد

⏱️ الوقت المقدر: 4-6 ساعات
🎯 النتيجة المتوقعة: تحسن 300% في جودة الطباعة`;

            alert(plan);
        }

        function generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                severity: 'critical',
                issues: {
                    critical: 5,
                    warning: 3,
                    info: 2
                },
                score: 25,
                recommendations: [
                    'إعادة كتابة إعدادات الطباعة',
                    'تصحيح الأبعاد والتخطيط',
                    'إضافة تصميم مزدوج اللغة',
                    'تحسين أحجام الخطوط',
                    'إزالة الكود المكرر'
                ],
                priority: 'urgent'
            };

            console.log('📊 تقرير تحليل شهادة الوفاة:', report);
            
            alert(`📊 تقرير التحليل الشامل:

🚨 مستوى الخطورة: حرج
📈 النتيجة: ${report.score}/100

🔍 المشاكل المكتشفة:
• حرجة: ${report.issues.critical}
• تحذيرات: ${report.issues.warning}  
• ملاحظات: ${report.issues.info}

⚡ الأولوية: عاجلة
💡 يحتاج إعادة كتابة شاملة

📋 أهم التوصيات:
${report.recommendations.map(r => '• ' + r).join('\n')}`);
        }

        // تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📋 تم تحميل تحليل شهادة الوفاة');
            
            // إضافة تأثيرات بصرية
            const sections = document.querySelectorAll('.analysis-section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(20px)';
                    section.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
