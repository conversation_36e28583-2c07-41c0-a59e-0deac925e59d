// Data Generator using IndexedDB - Enhanced for large datasets
const arabicFirstNames = [
    'أحمد', 'محمد', 'علي', 'حسن', 'حسين', 'عبدالله', 'عبدالرحمن', 'خالد', 'سعد', 'فهد',
    'عمر', 'يوسف', 'إبراهيم', 'عبدالعزيز', 'سلمان', 'فيصل', 'نايف', 'بندر', 'تركي', 'مشعل',
    'فاطمة', 'عائشة', 'خديجة', 'زينب', 'مريم', 'سارة', 'نورا', 'هند', 'ريم', 'لينا',
    'أمل', 'سعاد', 'منى', 'ليلى', 'سلمى', 'دينا', 'رنا', 'هالة', 'نادية', 'سميرة'
];

const arabicFamilyNames = [
    'العتيبي', 'المطيري', 'الدوسري', 'الشمري', 'القحطاني', 'الغامدي', 'الزهراني', 'الحربي', 'العنزي', 'الرشيد',
    'آل سعود', 'آل الشيخ', 'البراك', 'الفايز', 'السديري', 'الثنيان', 'الخالد', 'المبارك', 'الصالح', 'الفهد',
    'محمد', 'أحمد', 'علي', 'حسن', 'إبراهيم', 'عبدالله', 'الأحمد', 'المحمد', 'العلي', 'الحسن'
];

const frenchFirstNames = [
    'Ahmed', 'Mohamed', 'Ali', 'Hassan', 'Hussein', 'Abdullah', 'Abderrahman', 'Khalid', 'Saad', 'Fahd',
    'Omar', 'Youssef', 'Ibrahim', 'Abdelaziz', 'Salman', 'Faisal', 'Naif', 'Bandar', 'Turki', 'Mishaal',
    'Fatima', 'Aicha', 'Khadija', 'Zeinab', 'Mariam', 'Sara', 'Nora', 'Hind', 'Reem', 'Lina',
    'Amal', 'Souad', 'Mona', 'Laila', 'Salma', 'Dina', 'Rana', 'Hala', 'Nadia', 'Samira'
];

const frenchFamilyNames = [
    'Al-Otaibi', 'Al-Mutairi', 'Al-Dosari', 'Al-Shammari', 'Al-Qahtani', 'Al-Ghamdi', 'Al-Zahrani', 'Al-Harbi', 'Al-Anzi', 'Al-Rashid',
    'Al-Saud', 'Al-Sheikh', 'Al-Barrak', 'Al-Fayez', 'Al-Sudairi', 'Al-Thunayan', 'Al-Khalid', 'Al-Mubarak', 'Al-Saleh', 'Al-Fahd',
    'Mohamed', 'Ahmed', 'Ali', 'Hassan', 'Ibrahim', 'Abdullah', 'Al-Ahmed', 'Al-Mohamed', 'Al-Ali', 'Al-Hassan'
];

const cities = [
    { ar: 'الرياض', fr: 'Riyadh' },
    { ar: 'جدة', fr: 'Jeddah' },
    { ar: 'مكة المكرمة', fr: 'Mecca' },
    { ar: 'المدينة المنورة', fr: 'Medina' },
    { ar: 'الدمام', fr: 'Dammam' },
    { ar: 'الطائف', fr: 'Taif' },
    { ar: 'بريدة', fr: 'Buraidah' },
    { ar: 'تبوك', fr: 'Tabuk' },
    { ar: 'القصيم', fr: 'Qassim' },
    { ar: 'حائل', fr: 'Hail' },
    { ar: 'الأحساء', fr: 'Al-Ahsa' },
    { ar: 'ينبع', fr: 'Yanbu' },
    { ar: 'الخبر', fr: 'Khobar' },
    { ar: 'أبها', fr: 'Abha' },
    { ar: 'نجران', fr: 'Najran' }
];

// أسباب الوفاة الشائعة
const deathCauses = [
    'مرض مزمن',
    'حادث مروري',
    'نوبة قلبية',
    'سكتة دماغية',
    'مرض السرطان',
    'فشل كلوي',
    'مضاعفات السكري',
    'التهاب رئوي',
    'شيخوخة طبيعية',
    'مرض الكبد',
    'حادث عمل',
    'غير محدد',
    'مرض وراثي',
    'مضاعفات جراحية',
    'عدوى شديدة'
];

// أماكن الوفاة
const deathPlaces = [
    'المستشفى العام',
    'المنزل',
    'مستشفى خاص',
    'دار المسنين',
    'الطريق العام',
    'مكان العمل',
    'مستشفى الملك فهد',
    'مستشفى الملك خالد',
    'مركز صحي',
    'عيادة خاصة',
    'مستشفى الأطفال',
    'مستشفى القلب',
    'مستشفى الأورام',
    'وحدة العناية المركزة',
    'قسم الطوارئ'
];

// Global variables
let isGenerating = false;
let startTime = 0;
let generationStats = {
    total: 0,
    generated: 0,
    living: 0,
    deceased: 0,
    withImages: 0,
    errors: 0,
    speed: 0
};

// Initialize page
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Initialize IndexedDB
        await citizensDB.init();
        console.log('تم تهيئة قاعدة البيانات بنجاح');
        await updateStatistics();
        updateImageRecommendation();
        updateRecordTypeOptions();
    } catch (error) {
        console.error('خطأ في تهيئة قاعدة البيانات:', error);
        alert('❌ خطأ في تهيئة قاعدة البيانات\nتأكد من دعم المتصفح لـ IndexedDB');
    }
});

// تحديث خيارات نوع السجل
function updateRecordTypeOptions() {
    const recordType = document.getElementById('recordType').value;
    const deathPercentageGroup = document.getElementById('deathPercentageGroup');

    if (recordType === 'mixed') {
        deathPercentageGroup.style.display = 'block';
    } else {
        deathPercentageGroup.style.display = 'none';
    }
}

// Update image recommendation based on record count
function updateImageRecommendation() {
    const recordCount = parseInt(document.getElementById('recordCount').value);
    const recommendationDiv = document.getElementById('imageRecommendation');

    let recommendation = '';
    let recommendedImagePercent = '0';

    if (recordCount <= 100) {
        recommendation = '💡 يمكنك استخدام 100% صور للاختبار الصغير';
        recommendedImagePercent = '100';
    } else if (recordCount <= 1000) {
        recommendation = '💡 يُنصح بـ 50% صور للاختبار المتوسط';
        recommendedImagePercent = '50';
    } else if (recordCount <= 10000) {
        recommendation = '💡 يُنصح بـ 25% صور للاختبار الكبير';
        recommendedImagePercent = '25';
    } else if (recordCount <= 50000) {
        recommendation = '⚠️ يُنصح بـ 10% صور للاختبار العملاق';
        recommendedImagePercent = '10';
    } else {
        recommendation = '🚫 يُنصح بـ 5% صور أو أقل للاختبار الأقصى';
        recommendedImagePercent = '5';
    }

    recommendationDiv.textContent = recommendation;

    // Auto-select recommended percentage
    document.getElementById('withImages').value = recommendedImagePercent;
}

// Generate random data
function getRandomItem(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomDate(start, end) {
    const date = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    return date.toISOString().split('T')[0];
}

function getRandomHijriDate() {
    const hijriMonths = ['محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية', 'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'];
    const day = Math.floor(Math.random() * 29) + 1;
    const month = getRandomItem(hijriMonths);
    const year = Math.floor(Math.random() * 50) + 1400; // 1400-1450
    return `${day} ${month} ${year}`;
}

function generateActNumber(index, year) {
    return `${index + 1}/${year}`;
}

// Generate a simple test certificate image (optimized for large datasets)
function generateTestCertificateImage() {
    const canvas = document.createElement('canvas');
    canvas.width = 150; // Very small for large datasets
    canvas.height = 100;
    const ctx = canvas.getContext('2d');

    // Create a simple certificate-like image
    const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6'];
    ctx.fillStyle = getRandomItem(colors);
    ctx.fillRect(0, 0, 150, 100);

    ctx.fillStyle = 'white';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('شهادة تجريبية', 75, 45);
    ctx.fillText('Test Certificate', 75, 60);

    return canvas.toDataURL('image/jpeg', 0.2); // Very low quality for smallest size
}

// Generate single citizen record
function generateCitizen(index, year, withImage = false, isDeceased = false) {
    const gender = Math.random() > 0.5 ? 'ذكر' : 'أنثى';
    const city = getRandomItem(cities);

    // Generate more unique ID to avoid conflicts
    const timestamp = Date.now();
    const randomPart = Math.floor(Math.random() * 10000);
    const uniqueId = `${timestamp}_${index}_${randomPart}`; // Use string ID to avoid conflicts

    // Generate birth date (older for deceased people)
    const birthStartYear = isDeceased ? 1930 : 1950;
    const birthEndYear = isDeceased ? 2010 : 2020;
    const birthDate = getRandomDate(new Date(birthStartYear, 0, 1), new Date(birthEndYear, 11, 31));

    const citizen = {
        id: uniqueId,
        firstNameAr: getRandomItem(arabicFirstNames),
        firstNameFr: getRandomItem(frenchFirstNames),
        familyNameAr: getRandomItem(arabicFamilyNames),
        familyNameFr: getRandomItem(frenchFamilyNames),
        birthPlaceAr: city.ar,
        birthPlaceFr: city.fr,
        birthDate: birthDate,
        hijriDate: getRandomHijriDate(),
        gender: gender,
        fatherNameAr: getRandomItem(arabicFirstNames) + ' ' + getRandomItem(arabicFamilyNames),
        fatherNameFr: getRandomItem(frenchFirstNames) + ' ' + getRandomItem(frenchFamilyNames),
        motherNameAr: getRandomItem(arabicFirstNames) + ' ' + getRandomItem(arabicFamilyNames),
        motherNameFr: getRandomItem(frenchFirstNames) + ' ' + getRandomItem(frenchFamilyNames),
        actNumber: generateActNumber(index, year),
        registrationDate: getRandomDate(new Date(2020, 0, 1), new Date()),
        createdAt: new Date().toISOString(),
        isDeceased: isDeceased || false
    };

    // Add death information if deceased
    if (isDeceased) {
        const deathDate = getRandomDate(new Date(birthDate), new Date());
        citizen.deathInfo = {
            deathPlace: getRandomItem(deathPlaces),
            deathDate: deathDate,
            deathTime: `${Math.floor(Math.random() * 24).toString().padStart(2, '0')}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`,
            deathCause: getRandomItem(deathCauses),
            registrationDate: getRandomDate(new Date(deathDate), new Date()),
            registrationTime: new Date().toLocaleTimeString('ar-MA'),
            certificationOfficer: 'ضابط الحالة المدنية - أيير'
        };
    }

    // Add certificate image if requested
    if (withImage) {
        citizen.certificateImage = {
            data: generateTestCertificateImage(),
            fileName: `certificate_${citizen.id}.jpg`,
            uploadDate: new Date().toISOString()
        };
    }

    return citizen;
}

// Main generation function with enhanced progress tracking
async function generateTestData() {
    if (isGenerating) {
        alert('⚠️ عملية إنشاء البيانات جارية بالفعل!');
        return;
    }

    const recordCount = parseInt(document.getElementById('recordCount').value);
    const recordType = document.getElementById('recordType').value;
    const deathPercentage = parseInt(document.getElementById('deathPercentage').value);
    const imagePercentage = parseInt(document.getElementById('withImages').value);
    const batchSize = parseInt(document.getElementById('batchSize').value);

    let typeDescription = '';
    if (recordType === 'living') {
        typeDescription = 'مواطنين أحياء فقط';
    } else if (recordType === 'deceased') {
        typeDescription = 'متوفين فقط';
    } else {
        typeDescription = `مختلط (${deathPercentage}% متوفين)`;
    }

    const confirmMessage = `هل أنت متأكد من إنشاء ${recordCount.toLocaleString()} سجل؟\n\n` +
                          `• نوع السجلات: ${typeDescription}\n` +
                          `• نسبة الشهادات: ${imagePercentage}%\n` +
                          `• حجم الدفعة: ${batchSize}\n` +
                          `• الوقت المتوقع: ${Math.ceil(recordCount / 2000)} دقيقة\n` +
                          `• سيتم استخدام IndexedDB للتخزين\n\n` +
                          `⚠️ تحذير: قد يستغرق وقتاً طويلاً مع الأعداد الكبيرة!`;

    if (!confirm(confirmMessage)) return;

    isGenerating = true;
    startTime = Date.now();

    // Initialize stats
    generationStats = {
        total: recordCount,
        generated: 0,
        living: 0,
        deceased: 0,
        withImages: 0,
        errors: 0,
        speed: 0
    };

    // Show progress
    document.getElementById('progressSection').style.display = 'block';

    const currentYear = new Date().getFullYear();

    // Clear existing data if requested
    if (confirm('هل تريد مسح البيانات الموجودة أولاً؟')) {
        try {
            await citizensDB.clearAllData();
            console.log('تم مسح البيانات الموجودة');
        } catch (error) {
            console.error('خطأ في مسح البيانات:', error);
        }
    }

    try {
        // Generate in batches to avoid memory issues
        for (let batch = 0; batch < Math.ceil(recordCount / batchSize); batch++) {
            const batchStart = batch * batchSize;
            const batchEnd = Math.min(batchStart + batchSize, recordCount);

            console.log(`معالجة الدفعة ${batch + 1}: السجلات ${batchStart + 1} إلى ${batchEnd}`);
            console.log(`نوع السجلات: ${recordType}, نسبة الوفيات: ${recordType === 'mixed' ? deathPercentage + '%' : recordType === 'deceased' ? '100%' : '0%'}`);

            // Generate batch with retry mechanism
            const batchPromises = [];
            let batchDeceasedCount = 0;
            let batchLivingCount = 0;

            for (let i = batchStart; i < batchEnd; i++) {
                const withImage = Math.random() * 100 < imagePercentage;

                // Determine if this record should be deceased
                let isDeceased = false;
                if (recordType === 'deceased') {
                    isDeceased = true;
                } else if (recordType === 'mixed') {
                    isDeceased = Math.random() * 100 < deathPercentage;
                }

                if (isDeceased) {
                    batchDeceasedCount++;
                } else {
                    batchLivingCount++;
                }

                // Function to add citizen with retry
                const addCitizenWithRetry = async (retryCount = 0) => {
                    try {
                        const citizen = generateCitizen(i, currentYear, withImage, isDeceased);

                        // Debug logging for death records
                        if (isDeceased) {
                            console.log(`إنشاء سجل وفاة ${i}:`, {
                                id: citizen.id,
                                name: `${citizen.firstNameAr} ${citizen.familyNameAr}`,
                                isDeceased: citizen.isDeceased,
                                deathInfo: citizen.deathInfo
                            });
                        }

                        await citizensDB.addCitizen(citizen);

                        generationStats.generated++;
                        if (isDeceased) {
                            generationStats.deceased++;
                            console.log(`✅ تم حفظ سجل وفاة ${citizen.id} بنجاح`);
                        } else {
                            generationStats.living++;
                        }
                        if (withImage) generationStats.withImages++;

                        // Update progress every 50 records for better responsiveness
                        if (generationStats.generated % 50 === 0) {
                            updateProgress();
                        }

                    } catch (error) {
                        if (retryCount < 3) {
                            // Retry up to 3 times with increasing delay
                            console.warn(`إعادة محاولة للمواطن ${i}, المحاولة ${retryCount + 1}:`, error);
                            await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 100));
                            return addCitizenWithRetry(retryCount + 1);
                        } else {
                            console.error(`فشل نهائي في إضافة المواطن ${i}:`, error);
                            generationStats.errors++;
                        }
                    }
                };

                batchPromises.push(addCitizenWithRetry());
            }

            console.log(`الدفعة ${batch + 1} - متوقع: ${batchLivingCount} أحياء، ${batchDeceasedCount} متوفين`);

            // Wait for batch to complete
            await Promise.all(batchPromises);

            console.log(`✅ انتهت الدفعة ${batch + 1} - الإجمالي: ${generationStats.living} أحياء، ${generationStats.deceased} متوفين`);

            // Update progress after each batch
            updateProgress();

            // Longer delay to prevent IndexedDB overload and ensure stability
            const delayTime = batchSize > 250 ? 50 : 25; // More delay for larger batches
            await new Promise(resolve => setTimeout(resolve, delayTime));
        }

        // Final update
        updateProgress();

    } catch (error) {
        console.error('خطأ في إنشاء البيانات:', error);
        alert('❌ خطأ في إنشاء البيانات: ' + error.message);
    }

    isGenerating = false;
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);

    // Hide progress and update stats
    document.getElementById('progressSection').style.display = 'none';
    await updateStatistics();

    // Show completion message with detailed analysis
    const successRate = ((generationStats.generated / generationStats.total) * 100).toFixed(1);
    const errorRate = ((generationStats.errors / generationStats.total) * 100).toFixed(1);

    let statusIcon = '✅';
    let statusText = 'ممتاز';

    if (successRate < 95) {
        statusIcon = '⚠️';
        statusText = 'جيد مع تحسينات مطلوبة';
    }
    if (successRate < 85) {
        statusIcon = '❌';
        statusText = 'يحتاج تحسين';
    }

    const successMessage = `${statusIcon} تم إنشاء ${generationStats.generated.toLocaleString()} سجل!\n\n` +
                          `📊 تقرير مفصل:\n` +
                          `• نسبة النجاح: ${successRate}% (${statusText})\n` +
                          `• الأحياء: ${generationStats.living.toLocaleString()}\n` +
                          `• المتوفين: ${generationStats.deceased.toLocaleString()}\n` +
                          `• مع شهادات: ${generationStats.withImages.toLocaleString()}\n` +
                          `• أخطاء: ${generationStats.errors} (${errorRate}%)\n` +
                          `• الوقت المستغرق: ${duration} ثانية\n` +
                          `• متوسط السرعة: ${(generationStats.generated / duration).toFixed(0)} سجل/ثانية\n\n` +
                          `💡 التوصيات:\n` +
                          `${successRate >= 95 ? '• الأداء ممتاز! النظام جاهز للإنتاج' :
                            successRate >= 85 ? '• جرب تقليل حجم الدفعة لتحسين النجاح' :
                            '• استخدم دفعات أصغر (50-100) وتأكد من استقرار النظام'}`;

    alert(successMessage);
}

// Update progress with detailed information
function updateProgress() {
    const percentage = (generationStats.generated / generationStats.total) * 100;
    const currentTime = Date.now();
    const elapsed = (currentTime - startTime) / 1000;
    const speed = generationStats.generated / elapsed;
    const remaining = (generationStats.total - generationStats.generated) / speed;

    generationStats.speed = speed;

    // Update progress bar
    document.getElementById('progressFill').style.width = percentage + '%';
    document.getElementById('progressText').textContent = percentage.toFixed(1) + '%';

    // Update detailed text
    const detailsText = `📊 التقدم التفصيلي:\n` +
                       `▶️ المعالجة: ${generationStats.generated.toLocaleString()} من ${generationStats.total.toLocaleString()} سجل\n` +
                       `💚 الأحياء: ${generationStats.living.toLocaleString()}\n` +
                       `⚱️ المتوفين: ${generationStats.deceased.toLocaleString()}\n` +
                       `📄 مع شهادات: ${generationStats.withImages.toLocaleString()} (${((generationStats.withImages / generationStats.generated) * 100).toFixed(1)}%)\n` +
                       `⚡ السرعة الحالية: ${speed.toFixed(0)} سجل/ثانية\n` +
                       `⏱️ الوقت المنقضي: ${Math.floor(elapsed / 60)}:${(elapsed % 60).toFixed(0).padStart(2, '0')}\n` +
                       `⏳ الوقت المتبقي: ${remaining > 0 ? Math.floor(remaining / 60) + ':' + (remaining % 60).toFixed(0).padStart(2, '0') : '0:00'}\n` +
                       `❌ أخطاء: ${generationStats.errors}`;

    document.getElementById('progressDetails').textContent = detailsText;

    // Update live stats
    document.getElementById('liveGenerated').textContent = generationStats.generated.toLocaleString();
    document.getElementById('liveLiving').textContent = generationStats.living.toLocaleString();
    document.getElementById('liveDeceased').textContent = generationStats.deceased.toLocaleString();
    document.getElementById('liveWithImages').textContent = generationStats.withImages.toLocaleString();
    document.getElementById('liveSpeed').textContent = speed.toFixed(0);
    document.getElementById('liveErrors').textContent = generationStats.errors;
}

// Update statistics from IndexedDB
async function updateStatistics() {
    try {
        const dbInfo = await citizensDB.getDatabaseInfo();

        if (dbInfo) {
            document.getElementById('totalRecords').textContent = dbInfo.totalCitizens.toLocaleString();

            // Calculate living and deceased counts
            const allCitizens = await citizensDB.getAllCitizens();
            const livingCount = allCitizens.filter(c => !c.isDeceased).length;
            const deceasedCount = allCitizens.filter(c => c.isDeceased).length;

            document.getElementById('livingRecords').textContent = livingCount.toLocaleString();
            document.getElementById('deceasedRecords').textContent = deceasedCount.toLocaleString();
            document.getElementById('withCertificates').textContent = dbInfo.withCertificates.toLocaleString();
            document.getElementById('dataSize').textContent = dbInfo.usedMB + ' MB';

            if (startTime > 0) {
                const duration = ((Date.now() - startTime) / 1000).toFixed(1);
                document.getElementById('generationTime').textContent = duration + 's';
            }
        }
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
    }
}

// Clear all data
async function clearAllData() {
    if (confirm('⚠️ هل أنت متأكد من حذف جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه!')) {
        try {
            await citizensDB.clearAllData();
            await updateStatistics();
            alert('✅ تم حذف جميع البيانات من IndexedDB');
        } catch (error) {
            console.error('خطأ في حذف البيانات:', error);
            alert('❌ خطأ في حذف البيانات: ' + error.message);
        }
    }
}
