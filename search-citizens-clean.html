<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 البحث في المواطنين - نسخة نظيفة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .search-section {
            padding: 30px;
            background: white;
        }

        .search-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-secondary { background: #6c757d; color: white; }

        .search-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }

        .results-section {
            padding: 30px;
            background: #f8f9fa;
        }

        .citizen-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .citizen-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .citizen-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .citizen-info div {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .citizen-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .radio-group {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .radio-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-weight: 500;
        }

        .radio-group input[type="radio"] {
            margin: 0;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            background: white;
            border-radius: 12px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .search-controls {
                grid-template-columns: 1fr;
            }
            
            .citizen-actions {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                margin: 2px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔍 البحث في المواطنين</h1>
            <p>نظام إدارة السجل المدني - جماعة أيير</p>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">🔎 البحث والتصفية</h2>
            
            <!-- Search Type -->
            <div class="radio-group">
                <label>
                    <input type="radio" name="searchType" value="all" checked>
                    الكل
                </label>
                <label>
                    <input type="radio" name="searchType" value="living">
                    الأحياء فقط
                </label>
                <label>
                    <input type="radio" name="searchType" value="deceased">
                    المتوفين فقط
                </label>
            </div>

            <!-- Search Controls -->
            <div class="search-controls">
                <div class="form-group">
                    <label for="searchName">🔍 البحث بالاسم</label>
                    <input type="text" id="searchName" placeholder="ادخل الاسم الشخصي أو العائلي">
                </div>
                
                <div class="form-group">
                    <label for="searchActNumber">📋 رقم العقد</label>
                    <input type="text" id="searchActNumber" placeholder="مثال: 123/2024">
                </div>
                
                <div class="form-group">
                    <label for="searchBirthDate">📅 تاريخ الازدياد</label>
                    <input type="date" id="searchBirthDate">
                </div>
            </div>

            <!-- Search Actions -->
            <div class="search-actions">
                <button class="btn btn-primary" onclick="searchCitizens()">🔍 بحث</button>
                <button class="btn btn-secondary" onclick="clearSearch()">🗑️ مسح</button>
                <button class="btn btn-success" onclick="showAll()">📋 عرض الكل</button>
            </div>
        </div>

        <!-- Results Section -->
        <div class="results-section">
            <div id="searchResults">
                <div class="no-results">
                    <h3>🔍 ابدأ البحث</h3>
                    <p>استخدم نموذج البحث أعلاه للعثور على المواطنين</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let db = null;
        const STORE_NAME = 'citizens';
        const DB_NAME = 'CitizensDatabase';

        // تهيئة قاعدة البيانات
        async function initDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, 1);
                
                request.onerror = () => reject(request.error);
                request.onsuccess = () => {
                    db = request.result;
                    resolve(db);
                };
                
                request.onupgradeneeded = (event) => {
                    db = event.target.result;
                    if (!db.objectStoreNames.contains(STORE_NAME)) {
                        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
                        store.createIndex('name', 'personalName', { unique: false });
                        store.createIndex('actNumber', 'actNumber', { unique: false });
                    }
                };
            });
        }
    </script>
</body>
</html>
