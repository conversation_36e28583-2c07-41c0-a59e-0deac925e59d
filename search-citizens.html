<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث في سجلات المواطنين - نظام الحالة المدنية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a 0%, #e74c3c 50%, #c41e3a 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.2);
            padding: 10px 0;
            font-size: 0.9em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 25px 0;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .header-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            font-size: 0.9em;
        }

        .current-time {
            background: rgba(255,255,255,0.15);
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .user-info {
            background: rgba(255,255,255,0.1);
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .morocco-emblem {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            border: 3px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .header-text {
            text-align: right;
        }

        .header-text h1 {
            font-size: 2em;
            margin: 0 0 8px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 8px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        .header-navigation {
            background: rgba(0,0,0,0.1);
            padding: 12px 0;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        .header-nav-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .header-nav-links {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
        }

        .header-nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .header-nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-color: rgba(255,255,255,0.4);
        }

        .header-nav-link.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }

        .header-nav-separator {
            width: 1px;
            height: 20px;
            background: rgba(255,255,255,0.3);
            margin: 0 5px;
        }

        .content {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .main-content {
            padding: 40px;
        }

        .search-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.8);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .search-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e74c3c, #3498db, #2ecc71);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .search-section:hover::before {
            transform: scaleX(1);
        }

        .search-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .section-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
            font-weight: 700;
            text-align: center;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 2px;
        }

        @keyframes titleGlow {
            0% {
                width: 60px;
                box-shadow: 0 0 10px rgba(196, 30, 58, 0.3);
            }
            100% {
                width: 100px;
                box-shadow: 0 0 20px rgba(196, 30, 58, 0.6);
            }
        }

        .search-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .search-type {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(196, 30, 58, 0.2);
            border-radius: 15px;
            padding: 25px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .search-type::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(196, 30, 58, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .search-type:hover::before {
            left: 100%;
        }

        .search-type:hover {
            border-color: #c41e3a;
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 12px 35px rgba(196, 30, 58, 0.2);
            background: linear-gradient(135deg, rgba(196, 30, 58, 0.05) 0%, rgba(0, 98, 51, 0.05) 100%);
        }

        .search-type input[type="radio"] {
            margin-left: 12px;
            transform: scale(1.3);
            accent-color: #c41e3a;
            position: relative;
            z-index: 1;
            pointer-events: none;
        }

        .search-type label {
            font-weight: 700;
            color: #2c3e50;
            cursor: pointer;
            display: block;
            margin-top: 12px;
            font-size: 1.1rem;
            position: relative;
            z-index: 1;
            transition: color 0.3s ease;
        }

        .search-type:hover label {
            color: #c41e3a;
        }

        .search-type input[type="radio"]:checked + label {
            color: #c41e3a;
            font-weight: 800;
        }

        .search-type:has(input[type="radio"]:checked) {
            border-color: #c41e3a;
            background: linear-gradient(135deg, rgba(196, 30, 58, 0.1) 0%, rgba(0, 98, 51, 0.05) 100%);
            box-shadow: 0 8px 25px rgba(196, 30, 58, 0.25);
            transform: translateY(-2px);
        }

        .search-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .field-group {
            display: flex;
            flex-direction: column;
        }

        .field-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .field-group input,
        .field-group select {
            padding: 15px 20px;
            border: 2px solid rgba(196, 30, 58, 0.2);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,249,250,0.9));
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .field-group input:focus,
        .field-group select:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 0 4px rgba(196, 30, 58, 0.15), 0 8px 25px rgba(196, 30, 58, 0.1);
            transform: translateY(-2px);
            background: rgba(255,255,255,0.95);
        }

        .field-group input::placeholder {
            color: #7f8c8d;
            transition: color 0.3s ease;
        }

        .field-group input:focus::placeholder {
            color: #bdc3c7;
        }

        .field-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1rem;
            display: block;
        }

        .death-fields {
            background: linear-gradient(135deg, rgba(255,245,245,0.9) 0%, rgba(254,215,215,0.9) 100%);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(196, 30, 58, 0.3);
            border-radius: 15px;
            padding: 25px;
            margin-top: 25px;
            display: none;
            box-shadow: 0 4px 20px rgba(196, 30, 58, 0.1);
        }

        .death-fields h3 {
            color: #c41e3a;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 700;
            font-size: 1.2em;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .btn {
            padding: 14px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            position: relative;
            overflow: hidden;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
            background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
            transform: translateY(-2px);
        }

        .btn-info {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
            transform: translateY(-2px);
        }

        .statistics-section {
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .statistics-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #006233 0%, #c41e3a 50%, #006233 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,249,250,0.9));
            backdrop-filter: blur(15px);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 2px solid rgba(196, 30, 58, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(196, 30, 58, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(196, 30, 58, 0.15);
            border-color: #c41e3a;
        }

        .stat-number {
            font-size: 1.8em;
            font-weight: 800;
            color: #c41e3a;
            margin-bottom: 8px;
            text-shadow: 0 1px 3px rgba(196, 30, 58, 0.2);
            position: relative;
            z-index: 1;
        }

        .stat-label {
            color: #2c3e50;
            font-weight: 600;
            font-size: 0.9em;
            position: relative;
            z-index: 1;
        }

        .results-section {
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .results-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a 0%, #006233 50%, #c41e3a 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite reverse;
        }

        .results-placeholder {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 60px 30px;
            background: linear-gradient(135deg, rgba(248,249,250,0.8), rgba(233,236,239,0.8));
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 2px dashed rgba(196, 30, 58, 0.3);
            font-size: 1.1em;
            font-weight: 500;
        }

        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 25px;
            margin-top: 40px;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #c41e3a, #006233);
        }

        .footer p {
            margin: 0;
            font-weight: 500;
            opacity: 0.9;
        }

        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 18px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .quick-actions-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
            font-weight: 700;
            text-align: center;
            border-bottom: 2px solid #006233;
            padding-bottom: 8px;
        }

        .quick-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .quick-btn {
            padding: 10px 16px;
            border-radius: 18px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 1.8em;
            }

            .header-nav-links {
                gap: 6px;
                justify-content: center;
            }

            .header-nav-link {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .header-nav-separator {
                display: none;
            }

            .content {
                padding: 20px 15px;
            }

            .search-fields {
                grid-template-columns: 1fr;
            }

            .morocco-emblem {
                width: 60px;
                height: 60px;
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">البحث في سجلات المواطنين</div>
                            <div class="department">مكتب الحالة المدنية - أيير</div>
                        </div>
                    </div>
                    <div class="header-left">
                        <div class="current-time" id="currentTime">جاري التحميل...</div>
                        <div class="user-info">👤 مستخدم النظام</div>
                    </div>
                </div>
            </div>

            <!-- Header Navigation -->
            <div class="header-navigation">
                <div class="header-nav-content">
                    <div class="header-nav-links">
                        <a href="main-dashboard.html" class="header-nav-link">
                            <span>🏠</span>
                            <span>الرئيسية</span>
                        </a>

                        <div class="header-nav-separator"></div>

                        <a href="citizens-database-indexeddb.html" class="header-nav-link">
                            <span>📝</span>
                            <span>إدخال البيانات</span>
                        </a>

                        <a href="search-citizens.html" class="header-nav-link active">
                            <span>🔍</span>
                            <span>البحث</span>
                        </a>

                        <div class="header-nav-separator"></div>

                        <a href="dual-birth-certificate.html" class="header-nav-link">
                            <span>📜</span>
                            <span>شهادة الازدياد</span>
                        </a>

                        <a href="death-data-entry.html" class="header-nav-link">
                            <span>⚱️</span>
                            <span>بيانات الوفاة</span>
                        </a>

                        <div class="header-nav-separator"></div>

                        <a href="data-generator.html" class="header-nav-link">
                            <span>🎲</span>
                            <span>مولد البيانات</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Search Section -->
            <div class="search-section">
                <h2 class="section-title">🔍 البحث في السجلات</h2>

                <!-- Search Types -->
                <div class="search-types">
                    <div class="search-type">
                        <input type="radio" id="searchAll" name="searchType" value="all" checked>
                        <label for="searchAll">🌐 البحث العام</label>
                    </div>
                    <div class="search-type">
                        <input type="radio" id="searchLiving" name="searchType" value="living">
                        <label for="searchLiving">👥 الأحياء فقط</label>
                    </div>
                    <div class="search-type">
                        <input type="radio" id="searchDeceased" name="searchType" value="deceased">
                        <label for="searchDeceased">⚱️ المتوفين فقط</label>
                    </div>
                </div>

                <!-- Search Fields -->
                <div class="search-fields">
                    <div class="field-group">
                        <label for="searchName">الاسم الشخصي أو العائلي</label>
                        <input type="text" id="searchName" placeholder="أدخل الاسم للبحث...">
                    </div>
                    <div class="field-group">
                        <label for="searchActNumberField">رقم العقد</label>
                        <input type="text" id="searchActNumberField" placeholder="أدخل رقم العقد...">
                    </div>
                    <div class="field-group">
                        <label for="searchBirthDate">تاريخ الازدياد</label>
                        <input type="date" id="searchBirthDate">
                    </div>
                    <div class="field-group">
                        <label for="searchParent">اسم الوالد أو الوالدة</label>
                        <input type="text" id="searchParent" placeholder="أدخل اسم الوالد أو الوالدة...">
                    </div>
                </div>

                <!-- Death Search Fields -->
                <div class="death-fields" id="deathSearchFields">
                    <h3>🔍 البحث في بيانات الوفيات</h3>
                    <div class="search-fields">
                        <div class="field-group">
                            <label for="searchDeathDate">تاريخ الوفاة</label>
                            <input type="date" id="searchDeathDate">
                        </div>
                        <div class="field-group">
                            <label for="searchDeathPlace">مكان الوفاة</label>
                            <input type="text" id="searchDeathPlace" placeholder="أدخل مكان الوفاة...">
                        </div>
                        <div class="field-group">
                            <label for="searchDeathCause">سبب الوفاة</label>
                            <input type="text" id="searchDeathCause" placeholder="أدخل سبب الوفاة...">
                        </div>
                        <div class="field-group">
                            <label for="searchDeathYear">سنة الوفاة</label>
                            <input type="number" id="searchDeathYear" placeholder="مثال: 2023" min="1900" max="2030">
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button onclick="searchCitizens()" class="btn btn-primary">🔍 بحث</button>
                    <button onclick="clearSearch()" class="btn btn-secondary">🗑️ مسح البحث</button>
                    <button onclick="showAll()" class="btn btn-success">📊 عرض الكل</button>
                </div>
            </div>

            <!-- Results Section -->
            <div class="results-section">
                <h2 class="section-title">📋 نتائج البحث</h2>
                <div class="results-placeholder" id="searchResults">
                    استخدم البحث أعلاه للعثور على المواطنين
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="statistics-section">
                <h2 class="section-title">📊 إحصائيات قاعدة البيانات</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalCitizens">0</div>
                        <div class="stat-label">إجمالي المواطنين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="livingCitizens">0</div>
                        <div class="stat-label">الأحياء</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="deceasedCitizens">0</div>
                        <div class="stat-label">المتوفين</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="withCertificates">0</div>
                        <div class="stat-label">مع الشهادات</div>
                    </div>
                </div>

                <!-- Refresh Button -->
                <div style="text-align: center; margin-top: 15px;">
                    <button onclick="refreshStatistics()" class="btn btn-success" style="padding: 8px 16px; font-size: 0.85em; border-radius: 15px;">
                        🔄 تحديث الإحصائيات
                    </button>
                </div>
            </div>


        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; 2024 نظام الحالة المدنية - جميع الحقوق محفوظة</p>
        </div>
    </div>

    <!-- Include IndexedDB Manager -->
    <script src="indexeddb-manager.js"></script>

    <script>
        // تهيئة قاعدة البيانات
        async function initDB() {
            try {
                console.log('🔧 Initializing database...');
                await citizensDB.init();
                console.log('✅ Database initialized successfully');
                return true;
            } catch (error) {
                console.error('❌ Database initialization error:', error);
                throw error;
            }
        }

        // تحديث الإحصائيات
        async function updateStatistics() {
            try {
                console.log('📊 Starting statistics update...');

                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    console.log('🔧 Database not initialized, initializing...');
                    await initDB();
                }

                console.log('📊 Reading data from database...');
                const citizens = await citizensDB.getAllCitizens();
                console.log('📋 Found', citizens.length, 'records in database');

                // حساب الإحصائيات
                const total = citizens.length;
                const living = citizens.filter(c => !c.isDeceased).length;
                const deceased = citizens.filter(c => c.isDeceased).length;
                const withCertificates = citizens.filter(c => c.certificateImage || c.birthCertificateImage || c.deathCertificateImage).length;

                console.log('📈 Statistics:', {
                    total: total,
                    living: living,
                    deceased: deceased,
                    withCertificates: withCertificates
                });

                // تحديث العناصر في الصفحة
                const totalElement = document.getElementById('totalCitizens');
                const livingElement = document.getElementById('livingCitizens');
                const deceasedElement = document.getElementById('deceasedCitizens');
                const certificatesElement = document.getElementById('withCertificates');

                if (totalElement) totalElement.textContent = total;
                if (livingElement) livingElement.textContent = living;
                if (deceasedElement) deceasedElement.textContent = deceased;
                if (certificatesElement) certificatesElement.textContent = withCertificates;

                console.log('✅ Statistics updated successfully');

                // إضافة تأثير بصري للأرقام
                animateNumbers();

            } catch (error) {
                console.error('❌ Error updating statistics:', error);

                // عرض قيم افتراضية في حالة الخطأ
                const elements = ['totalCitizens', 'livingCitizens', 'deceasedCitizens', 'withCertificates'];
                elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = '0';
                });
            }
        }

        // تأثير بصري للأرقام
        function animateNumbers() {
            const numberElements = document.querySelectorAll('.stat-number');
            numberElements.forEach(element => {
                const finalValue = parseInt(element.textContent);
                let currentValue = 0;
                const increment = Math.ceil(finalValue / 20);

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    element.textContent = currentValue;
                }, 50);
            });
        }

        // فحص قواعد البيانات المتاحة
        async function checkDatabases() {
            try {
                console.log('🔍 Checking available databases...');

                if (!window.indexedDB) {
                    console.error('❌ IndexedDB not supported');
                    return null;
                }

                console.log('✅ IndexedDB is available');

                // قائمة بأسماء قواعد البيانات المحتملة
                const possibleDBNames = ['CitizensDB', 'citizensDB', 'citizens-db'];

                for (const dbName of possibleDBNames) {
                    try {
                        console.log(`🔍 Checking database: ${dbName}`);

                        const result = await new Promise((resolve, reject) => {
                            const request = indexedDB.open(dbName);

                            request.onsuccess = () => {
                                const db = request.result;
                                const stores = Array.from(db.objectStoreNames);
                                console.log(`✅ Database ${dbName} exists with stores:`, stores);

                                // فحص البيانات في المخزن
                                if (stores.includes('citizens')) {
                                    const transaction = db.transaction(['citizens'], 'readonly');
                                    const store = transaction.objectStore('citizens');
                                    const countRequest = store.count();

                                    countRequest.onsuccess = () => {
                                        const count = countRequest.result;
                                        console.log(`📊 Records count in ${dbName}/citizens:`, count);
                                        db.close();
                                        resolve({ name: dbName, count: count, stores: stores });
                                    };

                                    countRequest.onerror = () => {
                                        console.log(`❌ Error counting records in ${dbName}`);
                                        db.close();
                                        resolve({ name: dbName, count: 0, stores: stores });
                                    };
                                } else {
                                    console.log(`⚠️ Database ${dbName} doesn't contain 'citizens' store`);
                                    db.close();
                                    resolve({ name: dbName, count: 0, stores: stores });
                                }
                            };

                            request.onerror = () => {
                                console.log(`❌ Database ${dbName} doesn't exist`);
                                resolve(null);
                            };

                            request.onupgradeneeded = () => {
                                console.log(`🆕 Database ${dbName} is new or needs upgrade`);
                                const db = request.result;
                                const stores = Array.from(db.objectStoreNames);
                                resolve({ name: dbName, count: 0, stores: stores });
                            };
                        });

                        if (result && result.count > 0) {
                            console.log(`✅ Found data in: ${result.name}`);
                            return result;
                        }

                    } catch (error) {
                        console.log(`❌ Error checking ${dbName}:`, error);
                    }
                }

                console.log('⚠️ No databases with data found');
                return null;

            } catch (error) {
                console.error('❌ Error in checkDatabases:', error);
                return null;
            }
        }

        // تحديث الإحصائيات يدوياً
        async function refreshStatistics() {
            console.log('🔄 Manual statistics refresh...');

            // إضافة مؤشر تحميل
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(element => {
                element.textContent = '...';
                element.style.color = '#6c757d';
            });

            try {
                await updateStatistics();

                // إعادة اللون الطبيعي
                statNumbers.forEach(element => {
                    element.style.color = '#c41e3a';
                });

            } catch (error) {
                console.error('❌ Error in manual refresh:', error);

                // عرض خطأ
                statNumbers.forEach(element => {
                    element.textContent = 'خطأ';
                    element.style.color = '#dc3545';
                });
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('🚀 Starting page initialization...');

                // تهيئة قاعدة البيانات أولاً
                console.log('🔧 Initializing database...');
                await initDB();

                // فحص قواعد البيانات المتاحة
                console.log('📊 Checking available databases...');
                const dbInfo = await checkDatabases();

                if (dbInfo && dbInfo.count > 0) {
                    console.log(`✅ Found data in: ${dbInfo.name} (${dbInfo.count} records)`);
                } else if (dbInfo && dbInfo.count === 0) {
                    console.log(`⚠️ Database ${dbInfo.name} exists but is empty`);
                } else {
                    console.log('⚠️ No existing databases found');
                }

                // تحديث الإحصائيات
                console.log('📈 Updating statistics...');
                await updateStatistics();

                console.log('✅ Page initialization completed');

                // عرض رسالة إذا كانت قاعدة البيانات فارغة
                const totalElement = document.getElementById('totalCitizens');
                if (totalElement && totalElement.textContent === '0') {
                    console.log('ℹ️ Database appears to be empty');

                    // يمكن إضافة رسالة للمستخدم هنا
                    setTimeout(() => {
                        const searchResults = document.getElementById('searchResults');
                        if (searchResults) {
                            searchResults.innerHTML = `
                                <div style="text-align: center; padding: 30px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; color: #856404;">
                                    <h3>📊 قاعدة البيانات فارغة</h3>
                                    <p>لا توجد بيانات مواطنين في النظام حالياً</p>
                                    <p>يمكنك إضافة بيانات جديدة من خلال:</p>
                                    <div style="margin-top: 15px;">
                                        <a href="citizens-database-indexeddb.html" class="btn btn-primary" style="margin: 5px;">📝 إدخال البيانات</a>
                                        <a href="data-generator.html" class="btn btn-success" style="margin: 5px;">🎲 مولد البيانات</a>
                                    </div>
                                </div>
                            `;
                        }
                    }, 1000);
                }

            } catch (error) {
                console.error('❌ Error during page initialization:', error);

                // محاولة إعادة التهيئة بعد 3 ثوان
                setTimeout(async () => {
                    console.log('🔄 Attempting re-initialization...');
                    try {
                        await initDB();
                        await updateStatistics();
                        console.log('✅ Re-initialization successful');
                    } catch (retryError) {
                        console.error('❌ Re-initialization failed:', retryError);
                    }
                }, 3000);
            }
        });

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(async () => {
            try {
                await updateStatistics();
                console.log('🔄 Auto-refresh statistics completed');
            } catch (error) {
                console.error('❌ Auto-refresh failed:', error);
            }
        }, 30000);

        // وظائف البحث

        // تغيير نوع البحث
        function toggleSearchFields() {
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            const deathFields = document.getElementById('deathSearchFields');

            console.log('🔄 Changing search type to:', searchType);

            if (searchType === 'deceased') {
                deathFields.style.display = 'block';
                console.log('👁️ Showing death search fields');
            } else {
                deathFields.style.display = 'none';
                console.log('👥 Hiding death search fields');
            }
        }

        // البحث في المواطنين
        async function searchCitizens() {
            try {
                console.log('🔍 Starting citizen search...');

                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await initDB();
                }

                // الحصول على معايير البحث مع التحقق من وجود العناصر
                const searchTypeElement = document.querySelector('input[name="searchType"]:checked');
                const searchNameElement = document.getElementById('searchName');
                const searchActNumberElement = document.getElementById('searchActNumberField');
                const searchBirthDateElement = document.getElementById('searchBirthDate');
                const searchParentElement = document.getElementById('searchParent');

                if (!searchTypeElement || !searchNameElement || !searchActNumberElement || !searchBirthDateElement || !searchParentElement) {
                    console.error('❌ Some search elements not found in DOM');
                    console.log('Missing elements:', {
                        searchType: !!searchTypeElement,
                        searchName: !!searchNameElement,
                        searchActNumber: !!searchActNumberElement,
                        searchBirthDate: !!searchBirthDateElement,
                        searchParent: !!searchParentElement
                    });
                    return;
                }

                const searchType = searchTypeElement.value;
                const searchName = searchNameElement.value.toLowerCase().trim();
                const searchActNumber = searchActNumberElement.value.trim();
                const searchBirthDate = searchBirthDateElement.value;
                const searchParent = searchParentElement.value.toLowerCase().trim();

                // معايير البحث في الوفيات
                const searchDeathDateElement = document.getElementById('searchDeathDate');
                const searchDeathPlaceElement = document.getElementById('searchDeathPlace');
                const searchDeathCauseElement = document.getElementById('searchDeathCause');
                const searchDeathYearElement = document.getElementById('searchDeathYear');

                const searchDeathDate = searchDeathDateElement ? searchDeathDateElement.value : '';
                const searchDeathPlace = searchDeathPlaceElement ? searchDeathPlaceElement.value.toLowerCase().trim() : '';
                const searchDeathCause = searchDeathCauseElement ? searchDeathCauseElement.value.toLowerCase().trim() : '';
                const searchDeathYear = searchDeathYearElement ? searchDeathYearElement.value : '';

                console.log('🔍 Search criteria:', {
                    searchType: searchType,
                    searchName: searchName,
                    searchActNumber: searchActNumber,
                    searchBirthDate: searchBirthDate,
                    searchParent: searchParent,
                    searchDeathDate: searchDeathDate,
                    searchDeathPlace: searchDeathPlace,
                    searchDeathCause: searchDeathCause,
                    searchDeathYear: searchDeathYear
                });

                // التحقق من وجود معايير بحث
                const hasSearchCriteria = searchName || searchActNumber || searchBirthDate || searchParent ||
                                        searchDeathDate || searchDeathPlace || searchDeathCause || searchDeathYear;

                console.log('🔍 Has search criteria:', hasSearchCriteria);
                console.log('🔍 Individual criteria:', {
                    name: !!searchName,
                    actNumber: !!searchActNumber,
                    birthDate: !!searchBirthDate,
                    parent: !!searchParent,
                    deathDate: !!searchDeathDate,
                    deathPlace: !!searchDeathPlace,
                    deathCause: !!searchDeathCause,
                    deathYear: !!searchDeathYear
                });

                if (!hasSearchCriteria) {
                    console.log('⚠️ No search criteria provided');
                    document.getElementById('searchResults').innerHTML = `
                        <div style="text-align: center; padding: 30px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; color: #856404;">
                            <h3>🔍 لم يتم تحديد معايير البحث</h3>
                            <p>يرجى إدخال معايير البحث أولاً</p>
                            <p style="font-size: 0.9em; margin-top: 10px;">يمكنك البحث بالاسم، رقم العقد، تاريخ الازدياد، أو اسم الوالدين</p>
                        </div>
                    `;
                    return;
                }

                // الحصول على جميع البيانات
                let results = await citizensDB.getAllCitizens();
                console.log(`📊 Total records in database: ${results.length}`);

                if (results.length === 0) {
                    console.log('⚠️ No data in database');
                    document.getElementById('searchResults').innerHTML = `
                        <div style="text-align: center; padding: 30px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; color: #856404;">
                            <h3>📊 قاعدة البيانات فارغة</h3>
                            <p>لا توجد بيانات في قاعدة البيانات للبحث فيها</p>
                        </div>
                    `;
                    return;
                }

                // عرض عينة من البيانات للتشخيص
                console.log('📋 Sample data:', results[0]);

                // تصفية حسب نوع البحث أولاً
                if (searchType === 'living') {
                    results = results.filter(citizen => !citizen.isDeceased);
                    console.log(`👥 Living citizens: ${results.length}`);
                } else if (searchType === 'deceased') {
                    results = results.filter(citizen => citizen.isDeceased);
                    console.log(`⚱️ Deceased citizens: ${results.length}`);
                }

                // تطبيق فلاتر البحث
                if (searchName) {
                    const beforeFilter = results.length;
                    console.log(`🔍 Searching for name: "${searchName}"`);

                    results = results.filter(citizen => {
                        // جمع جميع الأسماء المحتملة
                        const names = [
                            citizen.firstNameAr || '',
                            citizen.familyNameAr || '',
                            citizen.personalName || '',
                            citizen.familyName || '',
                            citizen.firstName || '',
                            citizen.lastName || ''
                        ].filter(name => name.trim() !== '');

                        const fullName = names.join(' ').toLowerCase();
                        const isMatch = fullName.includes(searchName);

                        if (isMatch) {
                            console.log(`✅ Match found: ${fullName}`);
                        }

                        return isMatch;
                    });
                    console.log(`👤 Name filter (${searchName}): ${beforeFilter} → ${results.length}`);
                }

                if (searchActNumber) {
                    const beforeFilter = results.length;
                    console.log(`🔍 Searching for act number: "${searchActNumber}"`);

                    results = results.filter(citizen => {
                        const actNumber = citizen.actNumber || citizen.registrationNumber || '';
                        const actNumberStr = actNumber.toString().trim();
                        const searchStr = searchActNumber.trim();

                        // المطابقة الكاملة (الأولوية الأولى) - هذا ما نريده للأرقام الفريدة
                        if (actNumberStr === searchStr) {
                            console.log(`✅ Exact match: ${actNumber}`);
                            return true;
                        }

                        // إذا كان البحث يحتوي على فاصل (مثل 2/2025)، نبحث عن مطابقة كاملة فقط
                        // هذا يضمن أن البحث عن "2/2025" لا يجد "12/2025"
                        if (searchStr.includes('/') || searchStr.includes('-') || searchStr.includes('.')) {
                            return false; // لا نريد مطابقات جزئية للأرقام المركبة
                        }

                        // للأرقام البسيطة فقط، نسمح بالبحث في بداية الرقم أو بداية أجزائه
                        // مثال: البحث عن "2" يجد "2/2025" و "20/2025" لكن ليس "12/2025"
                        const actParts = actNumberStr.split(/[\/\-\.]/);
                        const isMatch = actParts.some(part => part.startsWith(searchStr));

                        if (isMatch) {
                            console.log(`✅ Partial match: ${actNumber} (part starts with "${searchStr}")`);
                        }

                        return isMatch;
                    });
                    console.log(`🔢 Act number filter (${searchActNumber}): ${beforeFilter} → ${results.length}`);

                    // إذا كان هناك نتيجة واحدة فقط، فهذا مثالي للبحث برقم العقد الفريد
                    if (results.length === 1) {
                        console.log(`🎯 Found unique act number: ${results[0].actNumber}`);
                    } else if (results.length === 0) {
                        console.log(`❌ No matches found for act number: ${searchActNumber}`);
                    } else {
                        console.log(`⚠️ Multiple matches found (${results.length}) - consider more specific search`);
                    }
                }

                if (searchBirthDate) {
                    const beforeFilter = results.length;
                    console.log(`🔍 Searching for birth date: "${searchBirthDate}"`);

                    results = results.filter(citizen => {
                        const birthDate = citizen.birthDate || citizen.dateOfBirth || '';
                        const isMatch = birthDate === searchBirthDate;

                        if (isMatch) {
                            console.log(`✅ Birth date match: ${birthDate}`);
                        }

                        return isMatch;
                    });
                    console.log(`📅 Birth date filter (${searchBirthDate}): ${beforeFilter} → ${results.length}`);
                }

                if (searchParent) {
                    const beforeFilter = results.length;
                    console.log(`🔍 Searching for parent name: "${searchParent}"`);

                    results = results.filter(citizen => {
                        const parentNames = [
                            citizen.fatherNameAr || '',
                            citizen.fatherName || '',
                            citizen.motherNameAr || '',
                            citizen.motherName || '',
                            citizen.father || '',
                            citizen.mother || ''
                        ].filter(name => name.trim() !== '');

                        const allParentNames = parentNames.join(' ').toLowerCase();
                        const isMatch = allParentNames.includes(searchParent);

                        if (isMatch) {
                            console.log(`✅ Parent name match: ${allParentNames}`);
                        }

                        return isMatch;
                    });
                    console.log(`👨‍👩‍👧‍👦 Parent filter (${searchParent}): ${beforeFilter} → ${results.length}`);
                }

                // تطبيق فلاتر البحث الخاصة بالوفيات
                if (searchType === 'deceased' || searchType === 'all') {
                    if (searchDeathDate) {
                        const beforeFilter = results.length;
                        results = results.filter(citizen =>
                            citizen.deathInfo && citizen.deathInfo.deathDate === searchDeathDate
                        );
                        console.log(`📅 Death date filter (${searchDeathDate}): ${beforeFilter} → ${results.length}`);
                    }

                    if (searchDeathPlace) {
                        const beforeFilter = results.length;
                        results = results.filter(citizen =>
                            citizen.deathInfo && citizen.deathInfo.deathPlace &&
                            citizen.deathInfo.deathPlace.toLowerCase().includes(searchDeathPlace)
                        );
                        console.log(`🏥 Death place filter (${searchDeathPlace}): ${beforeFilter} → ${results.length}`);
                    }

                    if (searchDeathCause) {
                        const beforeFilter = results.length;
                        results = results.filter(citizen =>
                            citizen.deathInfo && citizen.deathInfo.deathCause &&
                            citizen.deathInfo.deathCause.toLowerCase().includes(searchDeathCause)
                        );
                        console.log(`💊 Death cause filter (${searchDeathCause}): ${beforeFilter} → ${results.length}`);
                    }

                    if (searchDeathYear) {
                        const beforeFilter = results.length;
                        results = results.filter(citizen => {
                            if (citizen.deathInfo && citizen.deathInfo.deathDate) {
                                const deathDate = citizen.deathInfo.deathDate;
                                const deathYear = deathDate.split('-')[0] || deathDate.substring(0, 4);
                                return deathYear === searchDeathYear;
                            }
                            return false;
                        });
                        console.log(`📆 Death year filter (${searchDeathYear}): ${beforeFilter} → ${results.length}`);
                    }
                }

                console.log(`✅ Final search results: ${results.length} records`);
                displayResults(results);

            } catch (error) {
                console.error('❌ Error in search:', error);
                document.getElementById('searchResults').innerHTML = `
                    <div style="text-align: center; padding: 30px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 10px; color: #721c24;">
                        <h3>❌ خطأ في البحث</h3>
                        <p>حدث خطأ أثناء البحث في قاعدة البيانات</p>
                    </div>
                `;
            }
        }

        // عرض النتائج
        function displayResults(results) {
            const resultsContainer = document.getElementById('searchResults');

            if (results.length === 0) {
                resultsContainer.innerHTML = `
                    <div style="text-align: center; padding: 30px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 10px; color: #0c5460;">
                        <h3>🔍 لا توجد نتائج</h3>
                        <p>لم يتم العثور على مواطنين يطابقون معايير البحث</p>
                        <button onclick="clearSearch()" class="btn btn-secondary" style="margin-top: 10px;">🗑️ مسح البحث</button>
                    </div>
                `;
                return;
            }

            let html = `
                <div style="margin-bottom: 15px; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; color: #155724;">
                    <strong>📊 تم العثور على ${results.length} نتيجة</strong>
                </div>
                <div style="display: grid; gap: 15px;">
            `;

            results.forEach(citizen => {
                const isDeceased = citizen.isDeceased;
                const statusColor = isDeceased ? '#dc3545' : '#28a745';
                const statusText = isDeceased ? '⚱️ متوفى' : '👤 حي';

                // تشخيص المعرف
                console.log('🔍 Citizen ID for display:', citizen.id, typeof citizen.id);

                html += `
                    <div style="background: white; border: 1px solid #dee2e6; border-radius: 10px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 15px;">
                            <div>
                                <h4 style="color: #2c3e50; margin: 0 0 5px 0;">
                                    ${citizen.firstNameAr || citizen.personalName || 'غير محدد'} ${citizen.familyNameAr || citizen.familyName || ''}
                                </h4>
                                <span style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">
                                    ${statusText}
                                </span>
                            </div>
                            <div style="text-align: left; font-size: 0.9em; color: #6c757d;">
                                ID: ${citizen.id}
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; font-size: 0.9em;">
                            <div><strong>تاريخ الازدياد:</strong> ${citizen.birthDate || 'غير محدد'}</div>
                            <div><strong>مكان الازدياد:</strong> ${citizen.birthPlaceAr || citizen.birthPlace || 'غير محدد'}</div>
                            <div><strong>رقم العقد:</strong> ${citizen.actNumber || 'غير محدد'}</div>
                            <div><strong>الوالد:</strong> ${citizen.fatherNameAr || citizen.fatherName || 'غير محدد'}</div>
                            <div><strong>الوالدة:</strong> ${citizen.motherNameAr || citizen.motherName || 'غير محدد'}</div>
                            ${citizen.profession ? `<div><strong>المهنة:</strong> ${citizen.profession}</div>` : ''}
                        </div>

                        ${isDeceased && citizen.deathInfo ? `
                            <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #dc3545;">
                                <h5 style="color: #dc3545; margin: 0 0 8px 0;">معلومات الوفاة</h5>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px; font-size: 0.85em;">
                                    <div><strong>تاريخ الوفاة:</strong> ${citizen.deathInfo.deathDate || 'غير محدد'}</div>
                                    <div><strong>مكان الوفاة:</strong> ${citizen.deathInfo.deathPlace || 'غير محدد'}</div>
                                    ${citizen.deathInfo.deathCause ? `<div><strong>سبب الوفاة:</strong> ${citizen.deathInfo.deathCause}</div>` : ''}
                                </div>
                            </div>
                        ` : ''}

                        <div style="margin-top: 15px; display: flex; gap: 8px; flex-wrap: wrap;">
                            ${!isDeceased ? `
                                <button onclick="viewBirthCertificate('${citizen.id}')" class="btn btn-primary" style="font-size: 0.8em; padding: 6px 12px;">
                                    📜 عرض شهادة الازدياد
                                </button>
                                <button onclick="viewFullCertificate('${citizen.id}')" class="btn btn-success" style="font-size: 0.8em; padding: 6px 12px;">
                                    📄 الشهادة الكاملة
                                </button>
                            ` : `
                                <button onclick="viewDeathCertificate('${citizen.id}')" class="btn btn-primary" style="font-size: 0.8em; padding: 6px 12px;">
                                    ⚱️ عرض شهادة الوفاة
                                </button>
                                <button onclick="viewDeathSummary('${citizen.id}')" class="btn btn-info" style="font-size: 0.8em; padding: 6px 12px;">
                                    📋 نسخة موجزة من رسم الوفاة
                                </button>
                            `}

                            ${citizen.deathInfo ? `
                                <button onclick="editDeceasedCitizen('${citizen.id}')" class="btn btn-secondary" style="font-size: 0.8em; padding: 6px 12px;">
                                    ✏️ تعديل بيانات الوفاة
                                </button>
                            ` : `
                                <button onclick="editCitizenWithData('${citizen.id}')" class="btn btn-secondary" style="font-size: 0.8em; padding: 6px 12px;">
                                    ✏️ تعديل
                                </button>
                            `}
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            resultsContainer.innerHTML = html;
        }

        // مسح البحث
        function clearSearch() {
            console.log('🗑️ Clearing all search fields');

            document.getElementById('searchName').value = '';
            document.getElementById('searchActNumberField').value = '';
            document.getElementById('searchBirthDate').value = '';
            document.getElementById('searchParent').value = '';

            const deathDateElement = document.getElementById('searchDeathDate');
            const deathPlaceElement = document.getElementById('searchDeathPlace');
            const deathCauseElement = document.getElementById('searchDeathCause');
            const deathYearElement = document.getElementById('searchDeathYear');

            if (deathDateElement) deathDateElement.value = '';
            if (deathPlaceElement) deathPlaceElement.value = '';
            if (deathCauseElement) deathCauseElement.value = '';
            if (deathYearElement) deathYearElement.value = '';

            // إعادة تعيين نوع البحث للعام
            document.querySelector('input[name="searchType"][value="all"]').checked = true;
            toggleSearchFields();

            // مسح النتائج
            document.getElementById('searchResults').innerHTML = `
                <div class="results-placeholder">
                    استخدم البحث أعلاه للعثور على المواطنين
                </div>
            `;

            console.log('✅ All fields cleared and default mode reset');
        }

        // عرض جميع المواطنين
        async function showAll() {
            try {
                console.log('📊 Showing all citizens...');

                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await initDB();
                }

                // الحصول على جميع البيانات
                const results = await citizensDB.getAllCitizens();
                console.log(`📊 Total citizens: ${results.length}`);

                if (results.length === 0) {
                    document.getElementById('searchResults').innerHTML = `
                        <div style="text-align: center; padding: 30px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; color: #856404;">
                            <h3>📊 قاعدة البيانات فارغة</h3>
                            <p>لا توجد بيانات مواطنين في النظام حالياً</p>
                            <div style="margin-top: 15px;">
                                <a href="citizens-database-indexeddb.html" class="btn btn-primary" style="margin: 5px;">📝 إدخال البيانات</a>
                                <a href="data-generator.html" class="btn btn-success" style="margin: 5px;">🎲 مولد البيانات</a>
                            </div>
                        </div>
                    `;
                    return;
                }

                // عرض جميع النتائج
                displayResults(results);

            } catch (error) {
                console.error('❌ Error showing all citizens:', error);
                document.getElementById('searchResults').innerHTML = `
                    <div style="text-align: center; padding: 30px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 10px; color: #721c24;">
                        <h3>❌ خطأ في عرض البيانات</h3>
                        <p>حدث خطأ أثناء قراءة قاعدة البيانات</p>
                    </div>
                `;
            }
        }

        // وظائف العرض والتعديل
        function viewBirthCertificate(id) {
            console.log('📜 Viewing birth certificate for citizen:', id, typeof id);

            // التحقق من صحة المعرف
            if (!id) {
                console.error('❌ No citizen ID provided');
                alert('❌ خطأ: لا يوجد معرف للمواطن');
                return;
            }

            // تشفير المعرف للتأكد من عدم وجود مشاكل في URL
            const encodedId = encodeURIComponent(id);
            const url = `dual-birth-certificate.html?id=${encodedId}`;

            console.log('📜 Opening URL:', url);
            console.log('📜 Encoded ID:', encodedId);
            window.open(url, '_blank');
        }

        async function viewDeathCertificate(id) {
            console.log('⚱️ بدء عرض شهادة الوفاة للمواطن:', id);

            try {
                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await citizensDB.init();
                }

                // الحصول على بيانات المواطن
                const citizen = await citizensDB.getCitizen(id, true);

                if (!citizen) {
                    alert('❌ لم يتم العثور على بيانات المواطن');
                    return;
                }

                if (!citizen.isDeceased) {
                    alert('⚠️ هذا المواطن ليس متوفى');
                    return;
                }

                // التحقق من وجود شهادة وفاة محملة (بنية موحدة)
                console.log('🔍 التحقق من وجود شهادة الوفاة المحملة...');

                if (!citizen.deathCertificateImage || !citizen.deathCertificateImage.hasImage || !citizen.deathCertificateImage.data) {
                    console.log('❌ لا توجد شهادة وفاة محملة');
                    alert('⚠️ لا توجد شهادة وفاة محملة لهذا المواطن\n\nيمكنك تحميل شهادة الوفاة من صفحة إدخال بيانات الوفيات');
                    return;
                }

                // استخدام البيانات مباشرة (بنية موحدة)
                const certificateData = citizen.deathCertificateImage;

                console.log('✅ تم العثور على شهادة الوفاة المحملة');

                // إعداد بيانات العرض من certificateData المعالج
                const citizenName = (citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '');
                const fileName = certificateData.fileName || 'شهادة_وفاة.jpg';
                const imageData = certificateData.data;
                const deathInfo = citizen.deathInfo || {}; // إضافة متغير deathInfo المفقود

                // إنشاء نافذة جديدة لعرض شهادة الوفاة المحملة
                const newWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes');

                if (!newWindow) {
                    alert('❌ تم حظر النافذة المنبثقة\n\nيرجى السماح بالنوافذ المنبثقة وإعادة المحاولة');
                    return;
                }

                newWindow.document.write('<!DOCTYPE html>');
                newWindow.document.write('<html lang="ar" dir="rtl">');
                newWindow.document.write('<head>');
                newWindow.document.write('<meta charset="UTF-8">');
                newWindow.document.write('<meta name="viewport" content="width=device-width, initial-scale=1.0">');
                newWindow.document.write('<title>شهادة الوفاة المحملة - ' + citizenName + '</title>');

                // CSS لعرض شهادة الوفاة المحملة
                newWindow.document.write('<style>');
                newWindow.document.write('* { margin: 0; padding: 0; box-sizing: border-box; }');
                newWindow.document.write('body { font-family: Arial, sans-serif; background: #2c3e50; padding: 20px; text-align: center; }');
                newWindow.document.write('.container { max-width: 100%; margin: 0 auto; background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }');
                newWindow.document.write('.header { background: linear-gradient(135deg, #2c3e50, #34495e); color: white; padding: 20px; text-align: center; }');
                newWindow.document.write('.header h1 { font-size: 1.8em; margin-bottom: 5px; }');
                newWindow.document.write('.header p { opacity: 0.9; font-size: 1em; margin: 2px 0; }');
                newWindow.document.write('.image-container { padding: 20px; background: white; text-align: center; }');
                newWindow.document.write('.certificate-image { max-width: 100%; height: auto; border: 2px solid #ddd; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }');
                newWindow.document.write('.info { padding: 15px; background: #f8f9fa; color: #666; font-size: 0.9em; }');
                newWindow.document.write('.controls { padding: 20px; background: #f8f9fa; text-align: center; }');
                newWindow.document.write('.btn { padding: 12px 24px; margin: 0 10px; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem; font-weight: 600; }');
                newWindow.document.write('.btn-primary { background: #3498db; color: white; }');
                newWindow.document.write('.btn-secondary { background: #95a5a6; color: white; }');
                newWindow.document.write('@media print { .controls { display: none; } body { background: white; padding: 0; } .header { background: #2c3e50 !important; } }');
                newWindow.document.write('</style>');
                newWindow.document.write('</head>');

                newWindow.document.write('<body>');
                newWindow.document.write('<div class="container">');

                // Header
                newWindow.document.write('<div class="header">');
                newWindow.document.write('<h1>⚱️ شهادة الوفاة المحملة</h1>');
                newWindow.document.write('<p>المواطن: ' + citizenName + '</p>');
                newWindow.document.write('<p>اسم الملف: ' + fileName + '</p>');
                newWindow.document.write('</div>');

                // عرض الصورة
                newWindow.document.write('<div class="image-container">');
                newWindow.document.write('<img src="' + imageData + '" alt="شهادة الوفاة" class="certificate-image">');
                newWindow.document.write('</div>');

                // معلومات إضافية
                newWindow.document.write('<div class="info">');
                newWindow.document.write('<p>📅 تم تحميل هذه الشهادة من الحاسوب</p>');
                newWindow.document.write('<p>🔍 يمكنك طباعة هذه الشهادة أو حفظها</p>');
                newWindow.document.write('</div>');

                // Controls
                newWindow.document.write('<div class="controls">');
                newWindow.document.write('<button class="btn btn-primary" onclick="window.print()">🖨️ طباعة</button>');
                newWindow.document.write('<button class="btn btn-secondary" onclick="window.close()">❌ إغلاق</button>');
                newWindow.document.write('</div>');

                newWindow.document.write('</div>');
                newWindow.document.write('</body>');
                newWindow.document.write('</html>');

                newWindow.document.close();

                console.log('✅ تم عرض شهادة الوفاة بنجاح');

            } catch (error) {
                console.error('❌ خطأ في عرض شهادة الوفاة:', error);
                alert('❌ حدث خطأ أثناء عرض شهادة الوفاة');
            }
        }

        async function viewFullCertificate(id) {
            console.log('📄 [الخطوة 2] بدء التحقق من الشهادة الكاملة للمواطن:', id);

            try {
                // ✅ الخطوة الأولى: التحقق من تهيئة قاعدة البيانات
                console.log('🔍 [الخطوة 2.1] التحقق من تهيئة قاعدة البيانات...');
                if (!citizensDB.isInitialized) {
                    console.log('⚙️ [الخطوة 2.1] تهيئة قاعدة البيانات...');
                    await initDB();
                }
                console.log('✅ [الخطوة 2.1] قاعدة البيانات جاهزة');

                // ✅ الخطوة الثانية: البحث عن المواطن
                console.log('🔍 [الخطوة 2.2] البحث عن المواطن بالمعرف:', id);
                const citizen = await citizensDB.getCitizen(id, true);

                if (!citizen) {
                    console.log('❌ [الخطوة 2.2] لم يتم العثور على المواطن');
                    alert('❌ خطأ في الخطوة الثانية:\\n\\nلم يتم العثور على بيانات المواطن بالمعرف: ' + id);
                    return;
                }
                console.log('✅ [الخطوة 2.2] تم العثور على المواطن:', citizen.firstNameAr, citizen.familyNameAr);

                // ✅ الخطوة الثالثة: التحقق من وجود الشهادة
                console.log('🔍 [الخطوة 2.3] التحقق من وجود الشهادة الكاملة...');
                if (!citizen.certificateImage) {
                    console.log('❌ [الخطوة 2.3] لا يوجد كائن certificateImage');
                    alert('⚠️ نتيجة الخطوة الثانية:\\n\\nلا توجد شهادة كاملة محفوظة لهذا المواطن\\n\\n✅ الخطوة الأولى: الزر يعمل\\n❌ الخطوة الثانية: لا توجد شهادة\\n\\nيمكنك إضافة الشهادة من صفحة التعديل');
                    return;
                }

                if (!citizen.certificateImage.hasImage) {
                    console.log('❌ [الخطوة 2.3] hasImage = false');
                    alert('⚠️ نتيجة الخطوة الثانية:\\n\\nلا توجد شهادة كاملة محفوظة لهذا المواطن\\n\\n✅ الخطوة الأولى: الزر يعمل\\n❌ الخطوة الثانية: لا توجد شهادة\\n\\nيمكنك إضافة الشهادة من صفحة التعديل');
                    return;
                }

                if (!citizen.certificateImage.data) {
                    console.log('❌ [الخطوة 2.3] لا توجد بيانات الصورة');
                    alert('❌ خطأ في الخطوة الثانية:\\n\\nبيانات الشهادة تالفة أو مفقودة\\n\\n✅ الخطوة الأولى: الزر يعمل\\n❌ الخطوة الثانية: بيانات تالفة');
                    return;
                }

                console.log('✅ [الخطوة 2.3] الشهادة موجودة وسليمة');

                // ✅ الخطوة الثالثة: عرض الشهادة
                console.log('🚀 [الخطوة 3] بدء عرض الشهادة الكاملة...');

                const citizenName = (citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '');
                const actNumber = citizen.actNumber || 'غير محدد';
                const fileName = citizen.certificateImage.fileName || 'غير محدد';
                const imageData = citizen.certificateImage.data;

                // إنشاء نافذة جديدة
                console.log('🔍 [الخطوة 3.1] إنشاء نافذة جديدة...');
                const newWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes,toolbar=no,menubar=no');

                if (!newWindow) {
                    console.log('❌ [الخطوة 3.1] فشل في إنشاء النافذة');
                    alert('❌ فشلت الخطوة الثالثة:\\n\\n✅ الخطوة الأولى: الزر يعمل\\n✅ الخطوة الثانية: التحقق مكتمل\\n❌ الخطوة الثالثة: تم حظر النافذة المنبثقة\\n\\nيرجى السماح بالنوافذ المنبثقة وإعادة المحاولة');
                    return;
                }
                console.log('✅ [الخطوة 3.1] تم إنشاء النافذة بنجاح');

                // إنشاء محتوى HTML بسيط وآمن
                console.log('🔍 [الخطوة 3.2] إنشاء محتوى الصفحة...');
                console.log('📊 بيانات الصورة متوفرة:', imageData ? 'نعم' : 'لا');
                console.log('📏 حجم بيانات الصورة:', imageData ? imageData.length : 0);

                // التأكد من صحة بيانات الصورة
                if (!imageData || !imageData.startsWith('data:image/')) {
                    console.error('❌ بيانات الصورة غير صحيحة:', imageData ? imageData.substring(0, 50) + '...' : 'فارغة');
                    alert('❌ بيانات الشهادة غير صحيحة أو تالفة');
                    newWindow.close();
                    return;
                }

                newWindow.document.write('<!DOCTYPE html>');
                newWindow.document.write('<html lang="ar" dir="rtl">');
                newWindow.document.write('<head>');
                newWindow.document.write('<meta charset="UTF-8">');
                newWindow.document.write('<meta name="viewport" content="width=device-width, initial-scale=1.0">');
                newWindow.document.write('<title>الشهادة الكاملة - ' + citizenName.replace(/'/g, '&#39;') + '</title>');

                // CSS بسيط
                newWindow.document.write('<style>');
                newWindow.document.write('* { margin: 0; padding: 0; box-sizing: border-box; }');
                newWindow.document.write('body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }');
                newWindow.document.write('.container { max-width: 1000px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }');
                newWindow.document.write('.header { background: linear-gradient(135deg, #c41e3a, #8b0000); color: white; padding: 20px; text-align: center; }');
                newWindow.document.write('.header h1 { font-size: 1.8em; margin-bottom: 10px; }');
                newWindow.document.write('.header p { opacity: 0.9; font-size: 1.1em; }');
                newWindow.document.write('.controls { padding: 15px 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; text-align: center; }');
                newWindow.document.write('.btn { padding: 10px 20px; margin: 0 5px; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem; font-weight: 600; transition: all 0.3s ease; }');
                newWindow.document.write('.btn-primary { background: #007bff; color: white; }');
                newWindow.document.write('.btn-primary:hover { background: #0056b3; }');
                newWindow.document.write('.btn-secondary { background: #6c757d; color: white; }');
                newWindow.document.write('.btn-secondary:hover { background: #545b62; }');
                newWindow.document.write('.image-container { padding: 20px; text-align: center; background: white; }');
                newWindow.document.write('.certificate-image { max-width: 100%; max-height: 70vh; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); cursor: zoom-in; }');
                newWindow.document.write('.info { padding: 15px 20px; background: #e9ecef; text-align: center; color: #495057; }');
                newWindow.document.write('@media print { @page { size: A4 portrait !important; margin: 0; } body { background: white; padding: 0; margin: 0; } .container { box-shadow: none; border-radius: 0; padding: 0; margin: 0; } .controls, .info, .header, .no-print { display: none !important; } .certificate-image { max-height: none; width: 100%; object-fit: contain; } .image-container { padding: 0; } }');
                newWindow.document.write('</style>');
                newWindow.document.write('</head>');

                // Body
                newWindow.document.write('<body>');
                newWindow.document.write('<div class="container">');

                // Header - مخفي عند الطباعة
                newWindow.document.write('<div class="header no-print">');
                newWindow.document.write('<h1>📄 الشهادة الكاملة</h1>');
                newWindow.document.write('<p>' + citizenName + ' - رقم العقد: ' + actNumber + '</p>');
                newWindow.document.write('</div>');

                // Controls
                newWindow.document.write('<div class="controls">');
                newWindow.document.write('<button class="btn btn-primary" onclick="window.print()">🖨️ طباعة</button>');
                newWindow.document.write('<button class="btn btn-secondary" onclick="window.close()">❌ إغلاق</button>');
                newWindow.document.write('</div>');

                // Image
                newWindow.document.write('<div class="image-container">');
                newWindow.document.write('<img class="certificate-image" src="' + imageData.replace(/"/g, '&quot;') + '" alt="الشهادة الكاملة - ' + citizenName.replace(/"/g, '&quot;') + '" onload="console.log(\'✅ تم تحميل الصورة بنجاح\')" onerror="console.error(\'❌ فشل في تحميل الصورة\'); alert(\'❌ فشل في تحميل الصورة\');">');
                newWindow.document.write('</div>');

                // Info
                newWindow.document.write('<div class="info">');
                newWindow.document.write('<p>📄 اسم الملف: ' + fileName + '</p>');
                newWindow.document.write('<p>💡 اضغط Ctrl+P للطباعة • اضغط Esc للإغلاق</p>');
                newWindow.document.write('</div>');

                newWindow.document.write('</div>');

                // JavaScript بسيط
                newWindow.document.write('<script>');
                newWindow.document.write('document.addEventListener("keydown", function(e) {');
                newWindow.document.write('if (e.key === "Escape") { window.close(); }');
                newWindow.document.write('else if (e.ctrlKey && e.key === "p") { e.preventDefault(); window.print(); }');
                newWindow.document.write('});');
                newWindow.document.write('<\/script>');

                newWindow.document.write('</body>');
                newWindow.document.write('</html>');

                newWindow.document.close();

                console.log('✅ [الخطوة 3.2] تم إنشاء المحتوى بنجاح');
                console.log('🎉 [الخطوة 3] اكتملت بنجاح!');

                // إضافة تأخير قصير للتأكد من تحميل المحتوى
                setTimeout(() => {
                    if (newWindow && !newWindow.closed) {
                        console.log('✅ النافذة ما زالت مفتوحة ومحتواها محمل');
                    } else {
                        console.log('⚠️ النافذة مغلقة أو لم تحمل بشكل صحيح');
                    }
                }, 1000);
                // تم حذف رسالة النجاح\\n\\n✅ الخطوة الأولى: الزر يعمل بشكل صحيح\\n✅ الخطوة الثانية: التحقق من النظام مكتمل\\n✅ الخطوة الثالثة: عرض الشهادة في نافذة جديدة\\n\\n🎯 النتيجة النهائية:\\n📄 تم عرض الشهادة الكاملة بنجاح\\n�️ يمكن طباعة الشهادة\\n❌ يمكن إغلاق النافذة\\n\\n�📋 تفاصيل المواطن:\\n👤 الاسم: ' + citizenName + '\\n🔢 رقم العقد: ' + actNumber + '\\n📄 اسم الملف: ' + fileName + '\\n\\n✨ خاصية عرض الشهادة الكاملة جاهزة للاستخدام!');

            } catch (error) {
                console.error('❌ [الخطوة 2] خطأ في التحقق:', error);
                alert('❌ فشلت الخطوة الثانية:\\n\\n✅ الخطوة الأولى: الزر يعمل\\n❌ الخطوة الثانية: خطأ في النظام\\n\\nتفاصيل الخطأ: ' + error.message);
            }
        }

        async function editCitizenWithData(id) {
            console.log('✏️ تحضير بيانات المواطن للتعديل:', id);

            try {
                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await initDB();
                }

                // الحصول على بيانات المواطن الكاملة
                const citizen = await citizensDB.getCitizen(id, true);

                if (!citizen) {
                    console.error('❌ لم يتم العثور على المواطن للتعديل');
                    alert('❌ خطأ: لم يتم العثور على بيانات المواطن');
                    return;
                }

                console.log('✅ تم العثور على بيانات المواطن:', citizen.firstNameAr, citizen.familyNameAr);

                // تحضير البيانات للتمرير
                const citizenData = {
                    id: citizen.id,
                    // البيانات الشخصية
                    firstNameAr: citizen.firstNameAr || '',
                    familyNameAr: citizen.familyNameAr || '',
                    firstNameFr: citizen.firstNameFr || '',
                    familyNameFr: citizen.familyNameFr || '',

                    // بيانات الازدياد
                    birthDate: citizen.birthDate || '',
                    birthPlaceAr: citizen.birthPlaceAr || citizen.birthPlace || '',
                    birthPlaceFr: citizen.birthPlaceFr || '',
                    hijriDate: citizen.hijriDate || '',
                    actNumber: citizen.actNumber || '',
                    registrationDate: citizen.registrationDate || citizen.createdAt ? citizen.createdAt.split('T')[0] : '',

                    // بيانات الوالدين
                    fatherNameAr: citizen.fatherNameAr || '',
                    fatherNameFr: citizen.fatherNameFr || '',
                    motherNameAr: citizen.motherNameAr || '',
                    motherNameFr: citizen.motherNameFr || '',

                    // بيانات إضافية
                    gender: citizen.gender || '',
                    nationality: citizen.nationality || '',
                    notes: citizen.notes || '',

                    // معلومات الشهادة
                    hasCertificate: citizen.certificateImage && citizen.certificateImage.hasImage ? 'true' : 'false',
                    certificateFileName: citizen.certificateImage && citizen.certificateImage.fileName ? citizen.certificateImage.fileName : '',
                    certificateImageData: citizen.certificateImage && citizen.certificateImage.data ? citizen.certificateImage.data : '',

                    // بيانات الوفاة (إن وجدت)
                    isDeceased: citizen.deathInfo ? 'true' : 'false',
                    deathDate: citizen.deathInfo ? citizen.deathInfo.deathDate || '' : '',
                    deathPlace: citizen.deathInfo ? citizen.deathInfo.deathPlace || '' : '',
                    deathCause: citizen.deathInfo ? citizen.deathInfo.deathCause || '' : '',

                    // بيانات النظام
                    createdAt: citizen.createdAt || '',
                    updatedAt: citizen.updatedAt || ''
                };

                // تحويل البيانات إلى URL parameters
                const params = new URLSearchParams();
                params.append('edit', 'true');

                // إضافة جميع البيانات كـ parameters
                Object.keys(citizenData).forEach(key => {
                    if (citizenData[key] !== null && citizenData[key] !== undefined) {
                        params.append(key, citizenData[key]);
                    }
                });

                // إنشاء URL مع البيانات
                const url = `citizens-database-indexeddb.html?${params.toString()}`;

                console.log('✅ تم تحضير البيانات للتعديل');
                console.log('📋 البيانات المرسلة:', citizenData);
                console.log('🔗 فتح صفحة التعديل...');

                // فتح صفحة التعديل مع البيانات
                window.open(url, '_blank');

            } catch (error) {
                console.error('❌ خطأ في تحضير بيانات التعديل:', error);
                alert('❌ حدث خطأ أثناء تحضير بيانات التعديل');
            }
        }

        // وظيفة تعديل بيانات المتوفين
        async function editDeceasedCitizen(id) {
            console.log('⚱️ تحضير بيانات المتوفى للتعديل:', id);

            try {
                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await initDB();
                }

                // الحصول على بيانات المواطن الكاملة
                const citizen = await citizensDB.getCitizen(id, true);

                if (!citizen) {
                    console.error('❌ لم يتم العثور على المتوفى للتعديل');
                    alert('❌ خطأ: لم يتم العثور على بيانات المتوفى');
                    return;
                }

                if (!citizen.deathInfo) {
                    console.error('❌ هذا المواطن ليس متوفياً');
                    alert('❌ خطأ: هذا المواطن ليس متوفياً');
                    return;
                }

                console.log('✅ تم العثور على بيانات المتوفى:', citizen.firstNameAr, citizen.familyNameAr);

                // إنشاء معاملات URL لصفحة تعديل شهادة الوفاة
                const params = new URLSearchParams({
                    edit: 'true',
                    id: citizen.id,
                    // البيانات الشخصية
                    personalName: citizen.firstNameAr || citizen.personalName || '',
                    familyName: citizen.familyNameAr || citizen.familyName || '',
                    birthDate: citizen.birthDate || '',
                    birthPlace: citizen.birthPlaceAr || citizen.birthPlace || '',
                    gender: citizen.gender || '',
                    profession: citizen.profession || '',
                    residence: citizen.residence || '',
                    maritalStatus: citizen.maritalStatus || '',
                    nationality: citizen.nationality || 'مغربية',
                    fatherName: citizen.fatherNameAr || citizen.fatherName || '',
                    motherName: citizen.motherNameAr || citizen.motherName || '',
                    // بيانات الوفاة
                    deathPlace: citizen.deathInfo?.deathPlace || '',
                    deathDate: citizen.deathInfo?.deathDate || '',
                    deathTime: citizen.deathInfo?.deathTime || '',
                    deathCause: citizen.deathInfo?.deathCause || '',
                    doctorName: citizen.deathInfo?.doctorName || '',
                    hospitalName: citizen.deathInfo?.hospitalName || '',
                    // بيانات العقد
                    actNumber: citizen.actNumber || '',
                    registrationDate: citizen.registrationDate || citizen.createdAt ? citizen.createdAt.split('T')[0] : ''
                });

                // إنشاء URL مع البيانات
                const url = `death-data-entry.html?${params.toString()}`;

                console.log('✅ تم تحضير بيانات الوفاة للتعديل');
                console.log('📋 البيانات المرسلة:', Object.fromEntries(params));
                console.log('🔗 فتح صفحة تعديل الوفاة...');

                // فتح صفحة تعديل الوفاة مع البيانات
                window.open(url, '_blank');

            } catch (error) {
                console.error('❌ خطأ في تحضير بيانات تعديل الوفاة:', error);
                alert('❌ حدث خطأ أثناء تحضير بيانات تعديل الوفاة');
            }
        }

        // الوظيفة القديمة للتوافق مع الأكواد الأخرى
        function editCitizen(id) {
            console.log('✏️ استدعاء الوظيفة القديمة، تحويل للوظيفة الجديدة...');
            editCitizenWithData(id);
        }

        // وظيفة عرض النسخة الموجزة من رسم الوفاة
        async function viewDeathSummary(id) {
            console.log('📋 طلب عرض النسخة الموجزة من رسم الوفاة للمواطن:', id);

            try {
                // التأكد من تهيئة قاعدة البيانات
                if (!citizensDB.isInitialized) {
                    await initDB();
                }

                // الحصول على بيانات المواطن من قاعدة البيانات
                const citizen = await citizensDB.getCitizen(id, true);

                if (!citizen) {
                    alert('❌ المواطن غير موجود');
                    return;
                }

                if (!citizen.isDeceased) {
                    alert('⚠️ هذا المواطن ليس متوفى');
                    return;
                }

                // فتح نموذج النسخة الموجزة من رسم الوفاة
                const url = `death-certificate.html?id=${encodeURIComponent(id)}`;
                console.log('📋 فتح النسخة الموجزة من رسم الوفاة:', url);
                window.open(url, '_blank');

            } catch (error) {
                console.error('❌ خطأ في عرض النسخة الموجزة من رسم الوفاة:', error);
                alert('❌ حدث خطأ أثناء عرض النسخة الموجزة من رسم الوفاة');
            }
        }

        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-MA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // إضافة مستمعي الأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث الوقت فوراً وكل دقيقة
            updateCurrentTime();
            setInterval(updateCurrentTime, 60000);

            // إضافة مستمعي الأحداث لأنواع البحث
            const searchTypeRadios = document.querySelectorAll('input[name="searchType"]');
            searchTypeRadios.forEach(radio => {
                radio.addEventListener('change', toggleSearchFields);
            });

            // إضافة مستمعي الأحداث للنقر على منطقة البحث بالكامل
            const searchTypeContainers = document.querySelectorAll('.search-type');
            searchTypeContainers.forEach(container => {
                container.addEventListener('click', function(e) {
                    // منع التفعيل المزدوج إذا تم النقر على الراديو مباشرة
                    if (e.target.type === 'radio') return;

                    // العثور على الراديو داخل هذه المنطقة
                    const radio = container.querySelector('input[type="radio"]');
                    if (radio) {
                        radio.checked = true;
                        // تفعيل حدث التغيير يدوياً
                        radio.dispatchEvent(new Event('change'));
                        console.log('🔘 تم تحديد خيار البحث:', radio.value);
                    }
                });

                // إضافة تأثير بصري عند النقر
                container.addEventListener('mousedown', function() {
                    container.style.transform = 'translateY(-3px) scale(0.98)';
                });

                container.addEventListener('mouseup', function() {
                    container.style.transform = '';
                });

                container.addEventListener('mouseleave', function() {
                    container.style.transform = '';
                });
            });
        });

    </script>
</body>
</html>
