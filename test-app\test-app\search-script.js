// Load citizens data
let citizens = JSON.parse(localStorage.getItem('citizens')) || [];
let searchIndex = {};
let currentPage = 1;
let itemsPerPage = 20;
let currentResults = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateStatistics();
    createSearchIndex();

    // Add Enter key support for search fields
    const searchFields = ['searchName', 'searchActNumber', 'searchBirthDate', 'searchParent'];
    searchFields.forEach(fieldId => {
        document.getElementById(fieldId).addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchCitizens();
            }
        });
    });
});

// Create search index for faster searching
function createSearchIndex() {
    searchIndex = {};
    citizens.forEach(citizen => {
        const searchText = [
            citizen.firstNameAr || '',
            citizen.firstNameFr || '',
            citizen.familyNameAr || '',
            citizen.familyNameFr || '',
            citizen.actNumber || '',
            citizen.fatherNameAr || '',
            citizen.fatherNameFr || '',
            citizen.motherNameAr || '',
            citizen.motherNameFr || ''
        ].join(' ').toLowerCase();

        searchIndex[citizen.id] = searchText;
    });
}

// Update statistics
function updateStatistics() {
    const total = citizens.length;
    const withCertificates = citizens.filter(c => c.certificateImage).length;
    const withoutCertificates = total - withCertificates;

    document.getElementById('totalCitizens').textContent = total;
    document.getElementById('withCertificates').textContent = withCertificates;
    document.getElementById('withoutCertificates').textContent = withoutCertificates;
}

// Search citizens with improved performance
function searchCitizens() {
    const searchName = document.getElementById('searchName').value.trim().toLowerCase();
    const searchActNumber = document.getElementById('searchActNumber').value.trim().toLowerCase();
    const searchBirthDate = document.getElementById('searchBirthDate').value;
    const searchParent = document.getElementById('searchParent').value.trim().toLowerCase();

    // Check if at least one search criteria is provided
    if (!searchName && !searchActNumber && !searchBirthDate && !searchParent) {
        alert('⚠️ يرجى إدخال معيار بحث واحد على الأقل');
        return;
    }

    // Apply all filters step by step
    let filtered = citizens.filter(citizen => {
        // Name filter
        let nameMatch = true;
        if (searchName) {
            const indexText = searchIndex[citizen.id] || '';
            nameMatch = indexText.includes(searchName);
        }

        // Act number filter
        let actNumberMatch = true;
        if (searchActNumber) {
            actNumberMatch = citizen.actNumber && citizen.actNumber.toLowerCase().includes(searchActNumber);
        }

        // Birth date filter
        let birthDateMatch = true;
        if (searchBirthDate) {
            birthDateMatch = citizen.birthDate && citizen.birthDate === searchBirthDate;
        }

        // Parent filter
        let parentMatch = true;
        if (searchParent) {
            const parentText = [
                citizen.fatherNameAr || '',
                citizen.fatherNameFr || '',
                citizen.motherNameAr || '',
                citizen.motherNameFr || ''
            ].join(' ').toLowerCase();
            parentMatch = parentText.includes(searchParent);
        }

        // Return true only if ALL conditions match
        return nameMatch && actNumberMatch && birthDateMatch && parentMatch;
    });

    // Store results and reset pagination
    currentResults = filtered;
    currentPage = 1;

    displayResults();
}

// Display search results with pagination
function displayResults() {
    const container = document.getElementById('searchResults');

    if (currentResults.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #7f8c8d; font-style: italic;">لم يتم العثور على نتائج مطابقة</p>';
        return;
    }

    // Calculate pagination
    const totalPages = Math.ceil(currentResults.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const citizensToShow = currentResults.slice(startIndex, endIndex);

    // Build results HTML
    let resultsHTML = '';

    // Add pagination info
    resultsHTML += '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center;">';
    resultsHTML += '<div style="margin-bottom: 10px;"><strong>📊 النتائج:</strong> ' + currentResults.length + ' مواطن | ';
    resultsHTML += '<strong>📄 الصفحة:</strong> ' + currentPage + ' من ' + totalPages + ' | ';
    resultsHTML += '<strong>📋 عرض:</strong> ' + (startIndex + 1) + '-' + Math.min(endIndex, currentResults.length) + '</div>';

    // Add pagination controls
    if (totalPages > 1) {
        resultsHTML += '<div>';
        if (currentPage > 1) {
            resultsHTML += '<button class="btn btn-secondary" onclick="changePage(' + (currentPage - 1) + ')" style="margin: 2px;">⬅️ السابق</button>';
        }

        // Page numbers
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            const activeClass = i === currentPage ? 'btn-primary' : 'btn-secondary';
            resultsHTML += '<button class="btn ' + activeClass + '" onclick="changePage(' + i + ')" style="margin: 2px;">' + i + '</button>';
        }

        if (currentPage < totalPages) {
            resultsHTML += '<button class="btn btn-secondary" onclick="changePage(' + (currentPage + 1) + ')" style="margin: 2px;">التالي ➡️</button>';
        }
        resultsHTML += '</div>';
    }
    resultsHTML += '</div>';

    // Add citizen cards
    citizensToShow.forEach(citizen => {
        const hasCertificate = citizen.certificateImage;
        const certificateBadge = hasCertificate ?
            '<span class="certificate-badge">📄 يوجد شهادة كاملة</span>' :
            '<span class="certificate-badge no-certificate">❌ لا توجد شهادة</span>';

        resultsHTML += '<div class="citizen-card">';
        resultsHTML += '<div class="citizen-info">';
        resultsHTML += '<div><strong>الاسم:</strong> ' + (citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '') + '</div>';
        resultsHTML += '<div><strong>الاسم (فرنسي):</strong> ' + (citizen.firstNameFr || '') + ' ' + (citizen.familyNameFr || '') + '</div>';
        resultsHTML += '<div><strong>رقم القيد:</strong> ' + citizen.actNumber + '</div>';
        resultsHTML += '<div><strong>تاريخ الازدياد:</strong> ' + citizen.birthDate + '</div>';
        resultsHTML += '<div><strong>الجنس:</strong> ' + citizen.gender + '</div>';
        resultsHTML += '<div><strong>مكان الازدياد:</strong> ' + (citizen.birthPlaceAr || '') + '</div>';
        resultsHTML += '<div><strong>الوالد:</strong> ' + (citizen.fatherNameAr || '') + '</div>';
        resultsHTML += '<div><strong>الوالدة:</strong> ' + (citizen.motherNameAr || '') + '</div>';
        resultsHTML += '</div>';
        resultsHTML += '<div style="margin-bottom: 15px;">' + certificateBadge + '</div>';
        resultsHTML += '<div class="citizen-actions">';
        resultsHTML += '<button class="btn btn-success" onclick="printCertificate(' + citizen.id + ')">🖨️ طباعة عقد الازدياد</button>';
        if (hasCertificate) {
            resultsHTML += '<button class="btn btn-primary" onclick="printFullCertificate(' + citizen.id + ')">🖨️ طباعة الشهادة الكاملة</button>';
        }
        resultsHTML += '<button class="btn btn-secondary" onclick="editCitizen(' + citizen.id + ')">✏️ تعديل البيانات</button>';
        resultsHTML += '<button class="btn" style="background: #e74c3c; color: white;" onclick="deleteCitizen(' + citizen.id + ')">🗑️ حذف</button>';
        resultsHTML += '</div>';
        resultsHTML += '</div>';
    });

    container.innerHTML = resultsHTML;
}

// Change page function
function changePage(page) {
    currentPage = page;
    displayResults();

    // Scroll to top of results
    document.getElementById('searchResults').scrollIntoView({ behavior: 'smooth' });
}

// Show all citizens
function showAll() {
    currentResults = [...citizens];
    currentPage = 1;
    displayResults();
}

// Clear search
function clearSearch() {
    document.getElementById('searchName').value = '';
    document.getElementById('searchActNumber').value = '';
    document.getElementById('searchBirthDate').value = '';
    document.getElementById('searchParent').value = '';

    // Clear results and show initial message
    currentResults = [];
    currentPage = 1;
    const container = document.getElementById('searchResults');
    container.innerHTML = '<p style="text-align: center; color: #7f8c8d; font-style: italic;">استخدم البحث أعلاه للعثور على المواطنين</p>';
}

// Print certificate
function printCertificate(id) {
    const citizen = citizens.find(c => c.id === id);
    if (!citizen) return;

    const params = new URLSearchParams({
        firstNameAr: citizen.firstNameAr || '',
        firstNameFr: citizen.firstNameFr || '',
        familyNameAr: citizen.familyNameAr || '',
        familyNameFr: citizen.familyNameFr || '',
        birthPlaceAr: citizen.birthPlaceAr || '',
        birthPlaceFr: citizen.birthPlaceFr || '',
        birthDate: citizen.birthDate,
        hijriDate: citizen.hijriDate,
        gender: citizen.gender,
        fatherNameAr: citizen.fatherNameAr || '',
        fatherNameFr: citizen.fatherNameFr || '',
        motherNameAr: citizen.motherNameAr || '',
        motherNameFr: citizen.motherNameFr || '',
        actNumber: citizen.actNumber,
        registrationDate: citizen.registrationDate
    });

    window.open('dual-birth-certificate.html?' + params.toString(), '_blank');
}

// Print full certificate image
function printFullCertificate(id) {
    const citizen = citizens.find(c => c.id === id);
    if (!citizen || !citizen.certificateImage) return;

    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    const htmlContent = '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>الشهادة الكاملة</title><style>body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; min-height: 100vh; background: white; } .certificate-image { max-width: 100%; max-height: 100vh; object-fit: contain; } @media print { @page { size: A4 portrait !important; margin: 0; } body { margin: 0; padding: 0; } .certificate-image { max-width: 100%; max-height: 100%; object-fit: contain; transform: rotate(0deg); } }</style></head><body><img src="' + citizen.certificateImage.data + '" alt="الشهادة الكاملة" class="certificate-image"><script>window.onload = function() { window.print(); window.onafterprint = function() { window.close(); } }</script></body></html>';

    printWindow.document.write(htmlContent);
    printWindow.document.close();
}

// Edit citizen
function editCitizen(id) {
    const citizen = citizens.find(c => c.id === id);
    if (!citizen) return;

    // Create URL parameters for the citizen data
    const params = new URLSearchParams({
        edit: 'true',
        id: citizen.id,
        firstNameAr: citizen.firstNameAr || '',
        firstNameFr: citizen.firstNameFr || '',
        familyNameAr: citizen.familyNameAr || '',
        familyNameFr: citizen.familyNameFr || '',
        birthPlaceAr: citizen.birthPlaceAr || '',
        birthPlaceFr: citizen.birthPlaceFr || '',
        birthDate: citizen.birthDate || '',
        hijriDate: citizen.hijriDate || '',
        gender: citizen.gender || '',
        fatherNameAr: citizen.fatherNameAr || '',
        fatherNameFr: citizen.fatherNameFr || '',
        motherNameAr: citizen.motherNameAr || '',
        motherNameFr: citizen.motherNameFr || '',
        actNumber: citizen.actNumber || '',
        registrationDate: citizen.registrationDate || ''
    });

    // Redirect to citizens database page with edit parameters
    window.location.href = 'citizens-database.html?' + params.toString();
}

// Global variable to store citizen ID for deletion
let citizenToDelete = null;

// Delete citizen - show confirmation modal
function deleteCitizen(id) {
    const citizen = citizens.find(c => c.id === id);
    if (!citizen) return;

    // Store citizen ID for deletion
    citizenToDelete = id;

    // Fill modal with citizen details
    const citizenName = ((citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '')).trim();
    document.getElementById('deleteCitizenName').textContent = citizenName;
    document.getElementById('deleteCitizenActNumber').textContent = citizen.actNumber;
    document.getElementById('deleteCitizenBirthDate').textContent = citizen.birthDate;

    // Show modal
    document.getElementById('deleteModal').style.display = 'block';
}

// Confirm deletion
function confirmDelete() {
    if (!citizenToDelete) return;

    const citizen = citizens.find(c => c.id === citizenToDelete);
    if (!citizen) return;

    const citizenName = ((citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '')).trim();

    // Remove citizen from array
    citizens = citizens.filter(c => c.id !== citizenToDelete);

    // Save updated data to localStorage
    localStorage.setItem('citizens', JSON.stringify(citizens));

    // Update search index
    createSearchIndex();

    // Update statistics
    updateStatistics();

    // Update current results if showing
    if (currentResults.length > 0) {
        currentResults = currentResults.filter(c => c.id !== citizenToDelete);
        // Adjust page if needed
        const totalPages = Math.ceil(currentResults.length / itemsPerPage);
        if (currentPage > totalPages && totalPages > 0) {
            currentPage = totalPages;
        }
        displayResults();
    }

    // Hide modal
    document.getElementById('deleteModal').style.display = 'none';

    // Show success message
    alert('✅ تم حذف بيانات المواطن بنجاح\nالاسم: ' + citizenName);

    // Reset citizen to delete
    citizenToDelete = null;
}

// Cancel deletion
function cancelDelete() {
    // Hide modal
    document.getElementById('deleteModal').style.display = 'none';

    // Reset citizen to delete
    citizenToDelete = null;
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('deleteModal');
    if (event.target === modal) {
        cancelDelete();
    }
}

// Export all data with backup
function exportAllData() {
    if (citizens.length === 0) {
        alert('⚠️ لا توجد بيانات للتصدير\nيرجى إضافة مواطنين أولاً');
        return;
    }

    const currentDate = new Date();
    const dateStr = currentDate.toISOString().split('T')[0];
    const timeStr = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

    const backupData = {
        exportDate: currentDate.toISOString(),
        totalCitizens: citizens.length,
        withCertificates: citizens.filter(c => c.certificateImage).length,
        version: '1.0',
        data: citizens
    };

    const dataStr = JSON.stringify(backupData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'citizens_backup_' + dateStr + '_' + timeStr + '.json';
    link.click();
    URL.revokeObjectURL(url);

    alert('✅ تم تصدير النسخة الاحتياطية بنجاح\nعدد المواطنين: ' + citizens.length + '\nالتاريخ: ' + dateStr);
}

// Auto backup function
function autoBackup() {
    if (citizens.length === 0) {
        alert('⚠️ لا توجد بيانات للنسخ الاحتياطي');
        return;
    }

    // Check last backup date
    const lastBackup = localStorage.getItem('lastBackupDate');
    const today = new Date().toDateString();

    if (lastBackup === today) {
        const confirmBackup = confirm('تم عمل نسخة احتياطية اليوم بالفعل.\nهل تريد عمل نسخة احتياطية أخرى؟');
        if (!confirmBackup) return;
    }

    // Export data
    exportAllData();

    // Save backup date
    localStorage.setItem('lastBackupDate', today);

    alert('✅ تم حفظ تاريخ النسخة الاحتياطية\nسيتم تذكيرك غداً لعمل نسخة احتياطية جديدة');
}

// Performance monitoring and cleanup
function checkPerformance() {
    const dataSize = JSON.stringify(citizens).length;
    const dataSizeMB = (dataSize / 1024 / 1024).toFixed(2);

    if (dataSize > 5 * 1024 * 1024) { // 5MB warning
        console.warn('⚠️ حجم البيانات كبير: ' + dataSizeMB + ' ميجابايت');

        if (confirm('⚠️ حجم البيانات أصبح كبيراً (' + dataSizeMB + ' ميجابايت)\nهل تريد تحسين الأداء بضغط الصور؟')) {
            optimizeStoredImages();
        }
    }
}

// Optimize stored images
function optimizeStoredImages() {
    let optimizedCount = 0;
    const totalWithImages = citizens.filter(c => c.certificateImage).length;

    if (totalWithImages === 0) {
        alert('لا توجد صور للتحسين');
        return;
    }

    alert('⏳ جاري تحسين ' + totalWithImages + ' صورة...\nقد يستغرق هذا بعض الوقت');

    citizens.forEach(citizen => {
        if (citizen.certificateImage && citizen.certificateImage.data) {
            const img = new Image();
            img.onload = function() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Reduce size if too large
                let { width, height } = img;
                if (width > 1920 || height > 1080) {
                    const ratio = Math.min(1920 / width, 1080 / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0, width, height);

                // Compress to JPEG with quality 0.7
                const optimizedData = canvas.toDataURL('image/jpeg', 0.7);

                if (optimizedData.length < citizen.certificateImage.data.length) {
                    citizen.certificateImage.data = optimizedData;
                    optimizedCount++;
                }

                // Save progress
                if (optimizedCount === totalWithImages) {
                    localStorage.setItem('citizens', JSON.stringify(citizens));
                    const newSize = (JSON.stringify(citizens).length / 1024 / 1024).toFixed(2);
                    alert('✅ تم تحسين ' + optimizedCount + ' صورة\nالحجم الجديد: ' + newSize + ' ميجابايت');
                }
            };
            img.src = citizen.certificateImage.data;
        }
    });
}

// Memory cleanup function
function cleanupMemory() {
    // Clear any unused variables
    if (window.gc) {
        window.gc(); // Force garbage collection if available
    }

    // Clear image caches
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        if (img.src.startsWith('data:')) {
            // Don't clear currently displayed images
            if (!img.closest('#imageDisplay')) {
                img.src = '';
            }
        }
    });
}

// Change items per page
function changeItemsPerPage() {
    const select = document.getElementById('itemsPerPageSelect');
    itemsPerPage = parseInt(select.value);
    currentPage = 1; // Reset to first page

    // Save preference
    localStorage.setItem('itemsPerPage', itemsPerPage);

    // Refresh display if there are current results
    if (currentResults.length > 0) {
        displayResults();
    }

    alert('✅ تم تغيير عدد النتائج إلى ' + itemsPerPage + ' في الصفحة');
}

// Load saved preferences
function loadPreferences() {
    const savedItemsPerPage = localStorage.getItem('itemsPerPage');
    if (savedItemsPerPage) {
        itemsPerPage = parseInt(savedItemsPerPage);
        document.getElementById('itemsPerPageSelect').value = itemsPerPage;
    }
}

// Auto cleanup every 5 minutes
setInterval(() => {
    cleanupMemory();
    checkPerformance();
}, 5 * 60 * 1000);

// Load preferences on page load
document.addEventListener('DOMContentLoaded', function() {
    loadPreferences();
});
