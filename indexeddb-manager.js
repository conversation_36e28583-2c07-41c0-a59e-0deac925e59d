// Enhanced IndexedDB Manager for Citizens Database - Cross-Computer Compatible
class CitizensDB {
    constructor() {
        this.dbName = 'CitizensDatabase';
        this.version = 3; // Increased version for user management support
        this.db = null;
        this.isInitialized = false;
        this.fallbackMode = false;
        this.initPromise = null;
    }

    // Enhanced initialization with fallback support
    async init() {
        // Return existing promise if already initializing
        if (this.initPromise) {
            return this.initPromise;
        }

        this.initPromise = this._performInit();
        return this.initPromise;
    }

    async _performInit() {
        try {
            // Check IndexedDB support
            if (!window.indexedDB) {
                console.warn('IndexedDB غير مدعوم، سيتم استخدام التخزين المحلي');
                this.fallbackMode = true;
                await this._initLocalStorageFallback();
                return true;
            }

            // Try to initialize IndexedDB
            await this._initIndexedDB();
            this.isInitialized = true;
            console.log('تم تهيئة IndexedDB بنجاح');

            // Auto-migrate from localStorage if needed
            await this._autoMigrateFromLocalStorage();

            return true;
        } catch (error) {
            console.error('خطأ في تهيئة IndexedDB:', error);
            console.log('التبديل إلى وضع التخزين المحلي...');

            this.fallbackMode = true;
            await this._initLocalStorageFallback();
            return true;
        }
    }

    async _initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('خطأ في فتح قاعدة البيانات:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;

                // Handle database errors and version changes
                this.db.onerror = (event) => {
                    console.error('خطأ في قاعدة البيانات:', event.target.error);
                };

                this.db.onversionchange = () => {
                    console.log('تم تحديث إصدار قاعدة البيانات');
                    this.db.close();
                    this.isInitialized = false;
                };

                console.log('تم فتح قاعدة البيانات بنجاح');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                this.db = event.target.result;
                console.log('إنشاء/تحديث قاعدة البيانات');

                // Create citizens store with enhanced structure
                if (!this.db.objectStoreNames.contains('citizens')) {
                    const citizensStore = this.db.createObjectStore('citizens', { keyPath: 'id' });

                    // Create indexes for faster searching
                    citizensStore.createIndex('actNumber', 'actNumber', { unique: false }); // Changed to non-unique for flexibility
                    citizensStore.createIndex('firstNameAr', 'firstNameAr', { unique: false });
                    citizensStore.createIndex('familyNameAr', 'familyNameAr', { unique: false });
                    citizensStore.createIndex('birthDate', 'birthDate', { unique: false });
                    citizensStore.createIndex('fatherNameAr', 'fatherNameAr', { unique: false });
                    citizensStore.createIndex('motherNameAr', 'motherNameAr', { unique: false });
                    citizensStore.createIndex('createdAt', 'createdAt', { unique: false });

                    console.log('تم إنشاء جدول المواطنين');
                }

                // Create images store (separate for better performance)
                if (!this.db.objectStoreNames.contains('images')) {
                    const imagesStore = this.db.createObjectStore('images', { keyPath: 'citizenId' });
                    console.log('تم إنشاء جدول الصور');
                }

                // Create settings store for app configuration
                if (!this.db.objectStoreNames.contains('settings')) {
                    const settingsStore = this.db.createObjectStore('settings', { keyPath: 'key' });
                    console.log('تم إنشاء جدول الإعدادات');
                }

                // Create metadata store for database info
                if (!this.db.objectStoreNames.contains('metadata')) {
                    const metadataStore = this.db.createObjectStore('metadata', { keyPath: 'key' });
                    console.log('تم إنشاء جدول البيانات الوصفية');
                }

                // Create users store for authentication system
                if (!this.db.objectStoreNames.contains('users')) {
                    const usersStore = this.db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
                    usersStore.createIndex('username', 'username', { unique: true });
                    usersStore.createIndex('email', 'email', { unique: true });
                    usersStore.createIndex('role', 'role', { unique: false });
                    usersStore.createIndex('isActive', 'isActive', { unique: false });
                    console.log('تم إنشاء جدول المستخدمين');
                }

                // Create sessions store for login management
                if (!this.db.objectStoreNames.contains('sessions')) {
                    const sessionsStore = this.db.createObjectStore('sessions', { keyPath: 'sessionId' });
                    sessionsStore.createIndex('userId', 'userId', { unique: false });
                    sessionsStore.createIndex('expiresAt', 'expiresAt', { unique: false });
                    console.log('تم إنشاء جدول الجلسات');
                }

                // Create audit_log store for tracking user actions
                if (!this.db.objectStoreNames.contains('audit_log')) {
                    const auditStore = this.db.createObjectStore('audit_log', { keyPath: 'id', autoIncrement: true });
                    auditStore.createIndex('userId', 'userId', { unique: false });
                    auditStore.createIndex('action', 'action', { unique: false });
                    auditStore.createIndex('timestamp', 'timestamp', { unique: false });
                    console.log('تم إنشاء جدول سجل العمليات');
                }
            };
        });
    }

    // Initialize localStorage fallback
    async _initLocalStorageFallback() {
        try {
            // Test localStorage availability
            const testKey = 'test_storage_' + Date.now();
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);

            console.log('تم تهيئة التخزين المحلي كبديل');
            this.isInitialized = true;
            return true;
        } catch (error) {
            console.error('خطأ في تهيئة التخزين المحلي:', error);
            throw new Error('لا يمكن تهيئة أي نوع من التخزين');
        }
    }

    // Auto-migrate data from localStorage to IndexedDB
    async _autoMigrateFromLocalStorage() {
        try {
            const localData = localStorage.getItem('citizens');
            if (localData && !this.fallbackMode) {
                const citizens = JSON.parse(localData);
                if (Array.isArray(citizens) && citizens.length > 0) {
                    const existingCount = await this.getCount();

                    // Only migrate if IndexedDB is empty
                    if (existingCount === 0) {
                        console.log(`بدء ترحيل ${citizens.length} سجل من التخزين المحلي إلى IndexedDB...`);

                        let migrated = 0;
                        for (const citizen of citizens) {
                            try {
                                await this.addCitizen(citizen);
                                migrated++;
                            } catch (error) {
                                console.warn(`فشل في ترحيل السجل ${citizen.id}:`, error);
                            }
                        }

                        console.log(`تم ترحيل ${migrated} سجل بنجاح`);

                        // Backup localStorage data before clearing
                        const backupKey = `citizens_backup_${new Date().toISOString().split('T')[0]}`;
                        localStorage.setItem(backupKey, localData);

                        // Clear old localStorage data
                        localStorage.removeItem('citizens');
                        console.log('تم مسح البيانات القديمة من التخزين المحلي');
                    }
                }
            }
        } catch (error) {
            console.error('خطأ في ترحيل البيانات:', error);
        }
    }

    // Get record count
    async getCount() {
        if (this.fallbackMode) {
            const data = localStorage.getItem('citizens');
            return data ? JSON.parse(data).length : 0;
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['citizens'], 'readonly');
            const store = transaction.objectStore('citizens');
            const request = store.count();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Add citizen with fallback support
    async addCitizen(citizen) {
        if (this.fallbackMode) {
            return this._addCitizenLocalStorage(citizen);
        }

        return new Promise((resolve, reject) => {
            try {
                // Check if images store exists
                const storeNames = Array.from(this.db.objectStoreNames);
                const hasImagesStore = storeNames.includes('images');

                // Use only citizens store if images store doesn't exist
                const transactionStores = hasImagesStore ? ['citizens', 'images'] : ['citizens'];
                const transaction = this.db.transaction(transactionStores, 'readwrite');
                const citizensStore = transaction.objectStore('citizens');
                const imagesStore = hasImagesStore ? transaction.objectStore('images') : null;

                // Separate image data from citizen data
                const citizenData = { ...citizen };
                let imageData = null;

                if (citizenData.certificateImage && hasImagesStore) {
                    imageData = {
                        citizenId: citizen.id,
                        ...citizenData.certificateImage
                    };
                    // Remove image from citizen data to keep it lightweight
                    citizenData.certificateImage = { hasImage: true };
                } else if (citizenData.certificateImage && !hasImagesStore) {
                    // Keep image data in citizen record if no separate images store
                    console.warn('مخزن الصور غير موجود، سيتم حفظ الصورة مع بيانات المواطن');
                }

                // Add timestamp if not exists
                if (!citizenData.createdAt) {
                    citizenData.createdAt = new Date().toISOString();
                }

                // Add citizen
                const citizenRequest = citizensStore.add(citizenData);

                citizenRequest.onsuccess = () => {
                    // Add image if exists and images store is available
                    if (imageData && imagesStore) {
                        const imageRequest = imagesStore.add(imageData);
                        imageRequest.onsuccess = () => resolve(citizen.id);
                        imageRequest.onerror = () => {
                            console.warn('فشل في حفظ الصورة، سيتم حفظ البيانات فقط');
                            resolve(citizen.id);
                        };
                    } else {
                        resolve(citizen.id);
                    }
                };

                citizenRequest.onerror = () => {
                    console.error('خطأ في إضافة المواطن:', citizenRequest.error);
                    reject(citizenRequest.error);
                };

                transaction.onerror = () => {
                    console.error('خطأ في المعاملة:', transaction.error);
                    reject(transaction.error);
                };
            } catch (error) {
                console.error('خطأ في addCitizen:', error);
                reject(error);
            }
        });
    }

    // LocalStorage fallback for addCitizen
    async _addCitizenLocalStorage(citizen) {
        try {
            const citizens = JSON.parse(localStorage.getItem('citizens') || '[]');

            // Check for duplicate ID
            const existingIndex = citizens.findIndex(c => c.id === citizen.id);
            if (existingIndex !== -1) {
                throw new Error(`مواطن بالرقم ${citizen.id} موجود بالفعل`);
            }

            // Add timestamp if not exists
            if (!citizen.createdAt) {
                citizen.createdAt = new Date().toISOString();
            }

            citizens.push(citizen);
            localStorage.setItem('citizens', JSON.stringify(citizens));

            console.log(`تم إضافة المواطن ${citizen.id} في التخزين المحلي`);
            return citizen.id;
        } catch (error) {
            console.error('خطأ في إضافة المواطن في التخزين المحلي:', error);
            throw error;
        }
    }

    // Get all citizens with fallback support
    async getAllCitizens() {
        if (this.fallbackMode) {
            return this._getAllCitizensLocalStorage();
        }

        return new Promise((resolve, reject) => {
            try {
                const transaction = this.db.transaction(['citizens'], 'readonly');
                const store = transaction.objectStore('citizens');
                const request = store.getAll();

                request.onsuccess = () => {
                    resolve(request.result || []);
                };

                request.onerror = () => {
                    console.error('خطأ في جلب البيانات:', request.error);
                    reject(request.error);
                };

                transaction.onerror = () => {
                    console.error('خطأ في المعاملة:', transaction.error);
                    reject(transaction.error);
                };
            } catch (error) {
                console.error('خطأ في getAllCitizens:', error);
                reject(error);
            }
        });
    }

    // LocalStorage fallback for getAllCitizens
    async _getAllCitizensLocalStorage() {
        try {
            const citizens = JSON.parse(localStorage.getItem('citizens') || '[]');
            return citizens;
        } catch (error) {
            console.error('خطأ في جلب البيانات من التخزين المحلي:', error);
            return [];
        }
    }

    // Get citizen by ID (with image if needed) - Enhanced with flexible ID matching
    async getCitizen(id, includeImage = false) {
        return new Promise((resolve, reject) => {
            // Check if images store exists
            const storeNames = Array.from(this.db.objectStoreNames);
            const hasImagesStore = storeNames.includes('images');

            const transactionStores = hasImagesStore ? ['citizens', 'images'] : ['citizens'];
            const transaction = this.db.transaction(transactionStores, 'readonly');
            const citizensStore = transaction.objectStore('citizens');
            const imagesStore = hasImagesStore ? transaction.objectStore('images') : null;

            // Try direct lookup first
            const request = citizensStore.get(id);

            request.onsuccess = () => {
                const citizen = request.result;
                if (citizen) {
                    // Found by direct lookup
                    this._handleCitizenResult(citizen, id, includeImage, imagesStore, resolve);
                } else {
                    // If not found, try alternative ID formats
                    console.log('🔍 Direct lookup failed, trying alternative formats for ID:', id);
                    this._findCitizenByAlternativeId(id, citizensStore, includeImage, imagesStore, resolve);
                }
            };

            request.onerror = () => reject(request.error);
        });
    }

    // Helper method to handle citizen result with image loading
    _handleCitizenResult(citizen, id, includeImage, imagesStore, resolve) {
        if (includeImage && citizen.certificateImage && citizen.certificateImage.hasImage && imagesStore) {
            const imageRequest = imagesStore.get(id);
            imageRequest.onsuccess = () => {
                if (imageRequest.result) {
                    citizen.certificateImage = imageRequest.result;
                }
                resolve(citizen);
            };
            imageRequest.onerror = () => resolve(citizen); // Return without image if error
        } else {
            resolve(citizen);
        }
    }

    // Helper method to find citizen by alternative ID formats
    _findCitizenByAlternativeId(searchId, citizensStore, includeImage, imagesStore, resolve) {
        const getAllRequest = citizensStore.getAll();

        getAllRequest.onsuccess = () => {
            const allCitizens = getAllRequest.result;
            console.log('🔍 Searching among', allCitizens.length, 'citizens for ID:', searchId);

            // Try different ID matching strategies
            let foundCitizen = null;

            // Strategy 1: Exact match (already tried above)
            foundCitizen = allCitizens.find(c => c.id === searchId);
            if (foundCitizen) {
                console.log('✅ Found by exact match');
                this._handleCitizenResult(foundCitizen, searchId, includeImage, imagesStore, resolve);
                return;
            }

            // Strategy 2: String vs Number conversion
            foundCitizen = allCitizens.find(c => c.id === searchId.toString());
            if (foundCitizen) {
                console.log('✅ Found by string conversion');
                this._handleCitizenResult(foundCitizen, searchId, includeImage, imagesStore, resolve);
                return;
            }

            foundCitizen = allCitizens.find(c => c.id === parseInt(searchId));
            if (foundCitizen) {
                console.log('✅ Found by number conversion');
                this._handleCitizenResult(foundCitizen, searchId, includeImage, imagesStore, resolve);
                return;
            }

            // Strategy 3: Loose string matching (for complex IDs)
            foundCitizen = allCitizens.find(c => c.id && c.id.toString() === searchId.toString());
            if (foundCitizen) {
                console.log('✅ Found by loose string matching');
                this._handleCitizenResult(foundCitizen, searchId, includeImage, imagesStore, resolve);
                return;
            }

            // Log some sample IDs for debugging
            console.log('❌ No match found. Sample IDs in database:',
                allCitizens.slice(0, 5).map(c => ({id: c.id, type: typeof c.id, name: c.firstNameAr || c.personalName}))
            );

            resolve(null);
        };

        getAllRequest.onerror = () => {
            console.error('❌ Error getting all citizens for alternative search');
            resolve(null);
        };
    }

    // Update citizen
    async updateCitizen(citizen) {
        return new Promise((resolve, reject) => {
            // Check if images store exists
            const storeNames = Array.from(this.db.objectStoreNames);
            const hasImagesStore = storeNames.includes('images');

            const transactionStores = hasImagesStore ? ['citizens', 'images'] : ['citizens'];
            const transaction = this.db.transaction(transactionStores, 'readwrite');
            const citizensStore = transaction.objectStore('citizens');
            const imagesStore = hasImagesStore ? transaction.objectStore('images') : null;

            // Separate image data
            const citizenData = { ...citizen };
            let imageData = null;

            if (citizenData.certificateImage && citizenData.certificateImage.data && hasImagesStore) {
                imageData = {
                    citizenId: citizen.id,
                    ...citizenData.certificateImage
                };
                citizenData.certificateImage = { hasImage: true };
            }

            // Update citizen
            const citizenRequest = citizensStore.put(citizenData);

            citizenRequest.onsuccess = () => {
                if (imageData && imagesStore) {
                    const imageRequest = imagesStore.put(imageData);
                    imageRequest.onsuccess = () => resolve(citizen.id);
                    imageRequest.onerror = () => reject(imageRequest.error);
                } else {
                    resolve(citizen.id);
                }
            };

            citizenRequest.onerror = () => reject(citizenRequest.error);
        });
    }

    // Delete citizen
    async deleteCitizen(id) {
        return new Promise((resolve, reject) => {
            // Check if images store exists
            const storeNames = Array.from(this.db.objectStoreNames);
            const hasImagesStore = storeNames.includes('images');

            const transactionStores = hasImagesStore ? ['citizens', 'images'] : ['citizens'];
            const transaction = this.db.transaction(transactionStores, 'readwrite');
            const citizensStore = transaction.objectStore('citizens');
            const imagesStore = hasImagesStore ? transaction.objectStore('images') : null;

            // Delete citizen
            const citizenRequest = citizensStore.delete(id);

            citizenRequest.onsuccess = () => {
                // Delete associated image if images store exists
                if (imagesStore) {
                    const imageRequest = imagesStore.delete(id);
                    imageRequest.onsuccess = () => resolve(true);
                    imageRequest.onerror = () => resolve(true); // Continue even if image deletion fails
                } else {
                    resolve(true);
                }
            };

            citizenRequest.onerror = () => reject(citizenRequest.error);
        });
    }

    // Search citizens
    async searchCitizens(searchCriteria) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(['citizens'], 'readonly');
            const store = transaction.objectStore('citizens');
            const request = store.getAll();

            request.onsuccess = () => {
                const allCitizens = request.result;

                // Apply filters
                const filtered = allCitizens.filter(citizen => {
                    // Name filter
                    let nameMatch = true;
                    if (searchCriteria.name) {
                        const searchText = [
                            citizen.firstNameAr || '',
                            citizen.firstNameFr || '',
                            citizen.familyNameAr || '',
                            citizen.familyNameFr || ''
                        ].join(' ').toLowerCase();
                        nameMatch = searchText.includes(searchCriteria.name.toLowerCase());
                    }

                    // Act number filter
                    let actNumberMatch = true;
                    if (searchCriteria.actNumber) {
                        actNumberMatch = citizen.actNumber &&
                            citizen.actNumber.toLowerCase().includes(searchCriteria.actNumber.toLowerCase());
                    }

                    // Birth date filter
                    let birthDateMatch = true;
                    if (searchCriteria.birthDate) {
                        birthDateMatch = citizen.birthDate === searchCriteria.birthDate;
                    }

                    // Parent filter
                    let parentMatch = true;
                    if (searchCriteria.parent) {
                        const parentText = [
                            citizen.fatherNameAr || '',
                            citizen.fatherNameFr || '',
                            citizen.motherNameAr || '',
                            citizen.motherNameFr || ''
                        ].join(' ').toLowerCase();
                        parentMatch = parentText.includes(searchCriteria.parent.toLowerCase());
                    }

                    return nameMatch && actNumberMatch && birthDateMatch && parentMatch;
                });

                resolve(filtered);
            };

            request.onerror = () => reject(request.error);
        });
    }

    // Get database size info
    async getDatabaseInfo() {
        try {
            const citizens = await this.getAllCitizens();

            // Estimate storage usage
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                const estimate = await navigator.storage.estimate();
                return {
                    totalCitizens: citizens.length,
                    withCertificates: citizens.filter(c => c.certificateImage && c.certificateImage.hasImage).length,
                    usedBytes: estimate.usage || 0,
                    availableBytes: estimate.quota || 0,
                    usedMB: ((estimate.usage || 0) / 1024 / 1024).toFixed(2),
                    availableMB: ((estimate.quota || 0) / 1024 / 1024).toFixed(2),
                    usagePercentage: estimate.quota ? ((estimate.usage / estimate.quota) * 100).toFixed(2) : 0
                };
            } else {
                return {
                    totalCitizens: citizens.length,
                    withCertificates: citizens.filter(c => c.certificateImage && c.certificateImage.hasImage).length,
                    usedBytes: 0,
                    availableBytes: 0,
                    usedMB: 'غير متاح',
                    availableMB: 'غير متاح',
                    usagePercentage: 0
                };
            }
        } catch (error) {
            console.error('خطأ في الحصول على معلومات قاعدة البيانات:', error);
            return null;
        }
    }

    // Clear all data
    async clearAllData() {
        return new Promise((resolve, reject) => {
            // Check if images store exists
            const storeNames = Array.from(this.db.objectStoreNames);
            const hasImagesStore = storeNames.includes('images');

            const transactionStores = hasImagesStore ? ['citizens', 'images'] : ['citizens'];
            const transaction = this.db.transaction(transactionStores, 'readwrite');
            const citizensStore = transaction.objectStore('citizens');
            const imagesStore = hasImagesStore ? transaction.objectStore('images') : null;

            const citizensRequest = citizensStore.clear();
            citizensRequest.onsuccess = () => {
                if (imagesStore) {
                    const imagesRequest = imagesStore.clear();
                    imagesRequest.onsuccess = () => resolve(true);
                    imagesRequest.onerror = () => reject(imagesRequest.error);
                } else {
                    resolve(true);
                }
            };
            citizensRequest.onerror = () => reject(citizensRequest.error);
        });
    }

    // Export all data to JSON file
    async exportData(includeImages = true) {
        try {
            const citizens = await this.getAllCitizens();
            const exportData = {
                version: this.version,
                exportDate: new Date().toISOString(),
                totalRecords: citizens.length,
                citizens: []
            };

            // Get citizens with images if requested
            for (const citizen of citizens) {
                if (includeImages && citizen.certificateImage && citizen.certificateImage.hasImage) {
                    const fullCitizen = await this.getCitizen(citizen.id, true);
                    exportData.citizens.push(fullCitizen);
                } else {
                    exportData.citizens.push(citizen);
                }
            }

            // Create and download file
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `citizens-backup-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);

            return {
                success: true,
                recordsExported: exportData.totalRecords,
                withImages: includeImages
            };
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            throw error;
        }
    }

    // Import data from JSON file
    async importData(file, options = { clearExisting: false, skipDuplicates: true }) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = async (e) => {
                try {
                    const importData = JSON.parse(e.target.result);

                    // Validate import data structure
                    if (!importData.citizens || !Array.isArray(importData.citizens)) {
                        throw new Error('ملف البيانات غير صحيح');
                    }

                    // Clear existing data if requested
                    if (options.clearExisting) {
                        await this.clearAllData();
                    }

                    let imported = 0;
                    let skipped = 0;
                    let errors = 0;

                    // Import citizens
                    for (const citizen of importData.citizens) {
                        try {
                            // Check for duplicates if skipDuplicates is enabled
                            if (options.skipDuplicates) {
                                const existing = await this.getCitizen(citizen.id);
                                if (existing) {
                                    skipped++;
                                    continue;
                                }
                            }

                            await this.addCitizen(citizen);
                            imported++;
                        } catch (error) {
                            console.error(`خطأ في استيراد المواطن ${citizen.id}:`, error);
                            errors++;
                        }
                    }

                    resolve({
                        success: true,
                        totalRecords: importData.citizens.length,
                        imported,
                        skipped,
                        errors,
                        importDate: importData.exportDate
                    });
                } catch (error) {
                    console.error('خطأ في استيراد البيانات:', error);
                    reject(error);
                }
            };

            reader.onerror = () => {
                reject(new Error('خطأ في قراءة الملف'));
            };

            reader.readAsText(file);
        });
    }

    // Export data as CSV (without images)
    async exportToCSV() {
        try {
            const citizens = await this.getAllCitizens();

            // CSV headers
            const headers = [
                'رقم الهوية',
                'رقم العقد',
                'الاسم الشخصي (عربي)',
                'الاسم العائلي (عربي)',
                'الاسم الشخصي (فرنسي)',
                'الاسم العائلي (فرنسي)',
                'تاريخ الازدياد',
                'مكان الازدياد',
                'الجنس',
                'اسم الوالد (عربي)',
                'اسم الوالدة (عربي)',
                'اسم الوالد (فرنسي)',
                'اسم الوالدة (فرنسي)',
                'تاريخ الإنشاء'
            ];

            // Convert citizens to CSV rows
            const csvRows = [headers.join(',')];

            citizens.forEach(citizen => {
                const row = [
                    citizen.id || '',
                    citizen.actNumber || '',
                    citizen.firstNameAr || '',
                    citizen.familyNameAr || '',
                    citizen.firstNameFr || '',
                    citizen.familyNameFr || '',
                    citizen.birthDate || '',
                    citizen.birthPlace || '',
                    citizen.gender || '',
                    citizen.fatherNameAr || '',
                    citizen.motherNameAr || '',
                    citizen.fatherNameFr || '',
                    citizen.motherNameFr || '',
                    citizen.createdAt || ''
                ].map(field => `"${field.toString().replace(/"/g, '""')}"`);

                csvRows.push(row.join(','));
            });

            // Create and download CSV file
            const csvContent = csvRows.join('\n');
            const csvBlob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(csvBlob);
            link.download = `citizens-data-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);

            return {
                success: true,
                recordsExported: citizens.length
            };
        } catch (error) {
            console.error('خطأ في تصدير CSV:', error);
            throw error;
        }
    }

    // Create automatic backup
    async createAutoBackup() {
        try {
            const info = await this.getDatabaseInfo();

            // Only create backup if there's data
            if (info.totalCitizens === 0) {
                return { success: false, message: 'لا توجد بيانات للنسخ الاحتياطي' };
            }

            // Store backup info in localStorage
            const backupInfo = {
                date: new Date().toISOString(),
                totalRecords: info.totalCitizens,
                withImages: info.withCertificates
            };

            // Export data (without images for auto backup to save space)
            await this.exportData(false);

            // Save backup info
            localStorage.setItem('lastAutoBackup', JSON.stringify(backupInfo));

            return {
                success: true,
                ...backupInfo
            };
        } catch (error) {
            console.error('خطأ في النسخ الاحتياطي التلقائي:', error);
            throw error;
        }
    }

    // Get last backup info
    getLastBackupInfo() {
        try {
            const backupInfo = localStorage.getItem('lastAutoBackup');
            return backupInfo ? JSON.parse(backupInfo) : null;
        } catch (error) {
            console.error('خطأ في قراءة معلومات النسخ الاحتياطي:', error);
            return null;
        }
    }

    // Validate database integrity
    async validateDatabase() {
        try {
            const citizens = await this.getAllCitizens();
            const issues = [];

            for (const citizen of citizens) {
                // Check required fields
                if (!citizen.id) issues.push(`مواطن بدون رقم هوية`);
                if (!citizen.firstNameAr) issues.push(`مواطن ${citizen.id}: اسم شخصي عربي مفقود`);
                if (!citizen.familyNameAr) issues.push(`مواطن ${citizen.id}: اسم عائلي عربي مفقود`);
                if (!citizen.birthDate) issues.push(`مواطن ${citizen.id}: تاريخ ازدياد مفقود`);

                // Check for duplicate act numbers
                const duplicates = citizens.filter(c => c.actNumber && c.actNumber === citizen.actNumber && c.id !== citizen.id);
                if (duplicates.length > 0) {
                    issues.push(`رقم عقد مكرر: ${citizen.actNumber}`);
                }
            }

            return {
                isValid: issues.length === 0,
                totalRecords: citizens.length,
                issues
            };
        } catch (error) {
            console.error('خطأ في فحص قاعدة البيانات:', error);
            return {
                isValid: false,
                totalRecords: 0,
                issues: ['خطأ في الوصول لقاعدة البيانات']
            };
        }
    }
}

// Global instance
const citizensDB = new CitizensDB();
