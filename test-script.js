// Certificate Management System - Test Environment JavaScript

// Test Environment Configuration
const TEST_CONFIG = {
    storagePrefix: 'test_',
    maxTestCertificates: 100,
    testDataSamples: [
        { name: 'أحمد محمد علي', type: 'شهادة تقدير', organization: 'الجماعة المحلية' },
        { name: 'فاطمة حسن', type: 'شهادة مشاركة', organization: 'المجلس البلدي' },
        { name: 'محمد عبدالله', type: 'شهادة إنجاز', organization: 'دائرة التنمية' },
        { name: 'عائشة أحمد', type: 'شهادة تفوق', organization: 'مكتب التعليم' },
        { name: 'يوسف إبراهيم', type: 'شهادة خدمة', organization: 'الإدارة المحلية' }
    ],
    birthCertificateSamples: [
        {
            firstName: 'أحمد',
            familyName: 'محمد علي',
            fatherName: 'محمد علي حسن',
            motherName: 'فاطمة أحمد',
            birthPlace: 'الرباط',
            gender: 'ذكر'
        },
        {
            firstName: 'فاطمة',
            familyName: 'حسن محمد',
            fatherName: 'حسن محمد أحمد',
            motherName: 'عائشة علي',
            birthPlace: 'الدار البيضاء',
            gender: 'أنثى'
        },
        {
            firstName: 'يوسف',
            familyName: 'عبدالله',
            fatherName: 'عبدالله يوسف',
            motherName: 'زينب محمد',
            birthPlace: 'فاس',
            gender: 'ذكر'
        }
    ]
};

// Initialize test environment
document.addEventListener('DOMContentLoaded', function() {
    initializeTestApp();
    updateStatistics();
    updateCurrentTime();
    addAnimations();
    addTestIndicator();
});

// Initialize test application
function initializeTestApp() {
    console.log('Test Environment Initialized');

    // Check if localStorage is available
    if (typeof(Storage) !== "undefined") {
        console.log('Local Storage is available for testing');
        initializeTestStorage();
    } else {
        showErrorMessage('متصفحك لا يدعم التخزين المحلي. لا يمكن تشغيل بيئة الاختبار.');
        return;
    }

    // Add error handling for test environment
    setupErrorHandling();
}

// Initialize test storage
function initializeTestStorage() {
    const testKey = TEST_CONFIG.storagePrefix + 'certificates';

    if (!localStorage.getItem(testKey)) {
        localStorage.setItem(testKey, JSON.stringify([]));
        console.log('Test storage initialized');
    }

    if (!localStorage.getItem(TEST_CONFIG.storagePrefix + 'settings')) {
        const defaultSettings = {
            organizationName: 'الجماعة المحلية - بيئة الاختبار',
            defaultCertificateType: 'شهادة تقدير',
            printFormat: 'A4',
            language: 'ar',
            testMode: true
        };
        localStorage.setItem(TEST_CONFIG.storagePrefix + 'settings', JSON.stringify(defaultSettings));
    }
}

// Add test environment indicator
function addTestIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'test-indicator';
    indicator.innerHTML = '<i class="fas fa-flask"></i> TEST';
    document.body.appendChild(indicator);
}

// Test Environment Functions

// Clear all test data
function clearTestData() {
    if (confirm('هل أنت متأكد من حذف جميع بيانات الاختبار؟\nسيتم حذف جميع الشهادات وعقود الازدياد.\nهذا الإجراء لا يمكن التراجع عنه.')) {
        try {
            // Clear test certificates
            localStorage.setItem(TEST_CONFIG.storagePrefix + 'certificates', JSON.stringify([]));

            // Clear birth certificates
            localStorage.setItem(TEST_CONFIG.storagePrefix + 'birth_certificates', JSON.stringify([]));

            // Clear test settings (keep default)
            const defaultSettings = {
                organizationName: 'الجماعة المحلية - بيئة الاختبار',
                defaultCertificateType: 'شهادة تقدير',
                printFormat: 'A4',
                language: 'ar',
                testMode: true
            };
            localStorage.setItem(TEST_CONFIG.storagePrefix + 'settings', JSON.stringify(defaultSettings));

            showSuccessMessage('تم مسح جميع بيانات الاختبار بنجاح (الشهادات وعقود الازدياد)');
            updateStatistics();

        } catch (error) {
            showErrorMessage('حدث خطأ أثناء مسح البيانات: ' + error.message);
        }
    }
}

// Generate test data
function generateTestData() {
    try {
        const certificates = getTestCertificates();
        const birthCertificates = getBirthCertificates();
        const numberOfCertificates = Math.floor(Math.random() * 15) + 5; // 5-20 certificates
        const numberOfBirthCerts = Math.floor(Math.random() * 10) + 3; // 3-13 birth certificates

        showLoadingMessage('جاري إنشاء البيانات التجريبية...');

        setTimeout(() => {
            // Generate regular certificates
            for (let i = 0; i < numberOfCertificates; i++) {
                const sample = TEST_CONFIG.testDataSamples[Math.floor(Math.random() * TEST_CONFIG.testDataSamples.length)];
                const randomDate = new Date();
                randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 30));

                const certificate = {
                    id: Date.now() + i,
                    name: sample.name + ' ' + (i + 1),
                    type: sample.type,
                    organization: sample.organization,
                    date: randomDate.toISOString().split('T')[0],
                    number: 'TEST-' + (1000 + i),
                    printed: Math.random() > 0.5,
                    notes: 'شهادة تجريبية للاختبار',
                    createdAt: new Date().toISOString()
                };

                certificates.push(certificate);
            }

            // Generate birth certificates
            for (let i = 0; i < numberOfBirthCerts; i++) {
                const sample = TEST_CONFIG.birthCertificateSamples[Math.floor(Math.random() * TEST_CONFIG.birthCertificateSamples.length)];
                const randomDate = new Date();
                randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 365 * 5)); // Random date within last 5 years

                const currentYear = new Date().getFullYear();
                const birthCertificate = {
                    id: Date.now() + i + 1000,
                    actNumber: `${currentYear}/${(i + 1).toString().padStart(4, '0')}`,
                    hijriYear: '1445',
                    gregorianYear: currentYear.toString(),
                    firstName: sample.firstName,
                    familyName: sample.familyName + ' ' + (i + 1),
                    birthDate: randomDate.toISOString().split('T')[0],
                    birthPlace: sample.birthPlace,
                    gender: sample.gender,
                    nationality: 'مغربية',
                    parentsMaritalStatus: 'متزوجان',
                    fatherName: sample.fatherName,
                    fatherNationality: 'مغربية',
                    motherName: sample.motherName,
                    motherNationality: 'مغربية',
                    declarantName: sample.fatherName,
                    declarantRelation: 'الوالد',
                    declarationDate: new Date().toISOString().split('T')[0],
                    declarationPlace: 'مكتب الحالة المدنية',
                    notes: 'عقد ازدياد تجريبي للاختبار',
                    type: 'عقد ازدياد',
                    status: 'مسجل',
                    createdAt: new Date().toISOString()
                };

                birthCertificates.push(birthCertificate);
            }

            localStorage.setItem(TEST_CONFIG.storagePrefix + 'certificates', JSON.stringify(certificates));
            localStorage.setItem(TEST_CONFIG.storagePrefix + 'birth_certificates', JSON.stringify(birthCertificates));

            hideLoadingMessage();
            showSuccessMessage(`تم إنشاء ${numberOfCertificates} شهادة و ${numberOfBirthCerts} عقد ازدياد تجريبي بنجاح`);
            updateStatistics();

        }, 1500);

    } catch (error) {
        hideLoadingMessage();
        showErrorMessage('حدث خطأ أثناء إنشاء البيانات التجريبية: ' + error.message);
    }
}

// Export test data
function exportTestData() {
    try {
        const certificates = getTestCertificates();
        if (certificates.length === 0) {
            showErrorMessage('لا توجد بيانات اختبار للتصدير');
            return;
        }

        const testData = {
            certificates: certificates,
            settings: JSON.parse(localStorage.getItem(TEST_CONFIG.storagePrefix + 'settings') || '{}'),
            exportDate: new Date().toISOString(),
            environment: 'test'
        };

        const dataStr = JSON.stringify(testData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `test-certificates-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showSuccessMessage('تم تصدير بيانات الاختبار بنجاح');

    } catch (error) {
        showErrorMessage('حدث خطأ أثناء تصدير البيانات: ' + error.message);
    }
}

// Reset to default
function resetToDefault() {
    if (confirm('هل تريد إعادة تعيين النظام إلى الحالة الافتراضية؟')) {
        try {
            clearTestData();
            generateTestData();
            showSuccessMessage('تم إعادة تعيين النظام بنجاح');
        } catch (error) {
            showErrorMessage('حدث خطأ أثناء إعادة التعيين: ' + error.message);
        }
    }
}

// Helper functions for test environment
function getTestCertificates() {
    const certificates = localStorage.getItem(TEST_CONFIG.storagePrefix + 'certificates');
    return certificates ? JSON.parse(certificates) : [];
}

function getBirthCertificates() {
    const birthCertificates = localStorage.getItem(TEST_CONFIG.storagePrefix + 'birth_certificates');
    return birthCertificates ? JSON.parse(birthCertificates) : [];
}

function updateStatistics() {
    const certificates = getTestCertificates();
    const birthCertificates = getBirthCertificates();
    const allDocuments = [...certificates, ...birthCertificates];

    const today = new Date().toDateString();
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    // Total certificates (including birth certificates)
    const totalCertificates = allDocuments.length;
    updateStatElement('totalCertificates', totalCertificates);

    // Today's certificates
    const todayCertificates = allDocuments.filter(cert => {
        const certDate = cert.date || cert.declarationDate || cert.createdAt;
        return new Date(certDate).toDateString() === today;
    }).length;
    updateStatElement('todayCertificates', todayCertificates);

    // This month's certificates
    const monthCertificates = allDocuments.filter(cert => {
        const certDate = new Date(cert.date || cert.declarationDate || cert.createdAt);
        return certDate.getMonth() === currentMonth && certDate.getFullYear() === currentYear;
    }).length;
    updateStatElement('monthCertificates', monthCertificates);

    // Printed certificates
    const printedCertificates = allDocuments.filter(cert => cert.printed || cert.status === 'مطبوع').length;
    updateStatElement('printedCertificates', printedCertificates);
}

function updateStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        animateNumber(element, parseInt(element.textContent) || 0, value);
    }
}

function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });

    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString + ' (TEST)';
    }

    setTimeout(updateCurrentTime, 60000);
}

// Navigation with test environment awareness
function navigateTo(page) {
    showLoadingMessage('جاري التحميل...');

    setTimeout(() => {
        // In test environment, show message instead of actual navigation
        hideLoadingMessage();
        showSuccessMessage(`في بيئة الاختبار: سيتم الانتقال إلى ${page}`);
    }, 1000);
}

// Message functions
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

function showErrorMessage(message) {
    showMessage(message, 'error');
}

function showLoadingMessage(message) {
    showMessage(message, 'loading');
}

function showMessage(message, type) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.test-message');
    existingMessages.forEach(msg => msg.remove());

    const messageDiv = document.createElement('div');
    messageDiv.className = `test-message ${type}-message`;

    if (type === 'loading') {
        messageDiv.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${message}`;
    } else if (type === 'success') {
        messageDiv.innerHTML = `<i class="fas fa-check-circle me-2"></i>${message}`;
    } else if (type === 'error') {
        messageDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
    }

    document.body.appendChild(messageDiv);

    // Auto remove after 3 seconds (except loading)
    if (type !== 'loading') {
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 3000);
    }
}

function hideLoadingMessage() {
    const loadingMessages = document.querySelectorAll('.loading-message');
    loadingMessages.forEach(msg => msg.remove());
}

// Error handling setup
function setupErrorHandling() {
    window.addEventListener('error', function(e) {
        console.error('Test Environment Error:', e.error);
        showErrorMessage('حدث خطأ في بيئة الاختبار: ' + e.message);
    });

    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
        showErrorMessage('خطأ غير متوقع: ' + e.reason);
    });
}

// Add animations
function addAnimations() {
    const cards = document.querySelectorAll('.function-card, .stat-card');

    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}
