<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قاعدة بيانات المواطنين - مكتب الحالة المدنية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .form-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .search-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .citizens-list {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            max-height: 600px;
            overflow-y: auto;
        }

        .citizen-card {
            background: white;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 10px;
            border-left: 5px solid #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .citizen-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .citizen-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .citizen-actions {
            text-align: left;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #ecf0f1;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .citizen-info {
                grid-template-columns: 1fr;
            }
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <a href="main-dashboard.html" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 10px 20px; border-radius: 25px; transition: all 0.3s ease;">
                    🏠 الصفحة الرئيسية
                </a>
                <div style="text-align: center;">
                    <h1 style="margin: 0;">🏛️ قاعدة بيانات المواطنين</h1>
                </div>
                <a href="dual-birth-certificate.html" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 10px 20px; border-radius: 25px; transition: all 0.3s ease;">
                    📜 عقود الازدياد
                </a>
            </div>
            <p>مكتب الحالة المدنية - أيير، إقليم أسفي</p>
        </div>

        <!-- Statistics -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalCitizens">0</div>
                <div class="stat-label">إجمالي المواطنين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="maleCount">0</div>
                <div class="stat-label">ذكور</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="femaleCount">0</div>
                <div class="stat-label">إناث</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayRegistrations">0</div>
                <div class="stat-label">تسجيلات اليوم</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Form Section -->
            <div class="form-section">
                <h2>📝 تسجيل مواطن جديد</h2>

                <div id="alertContainer"></div>

                <form id="citizenForm">
                    <div class="form-group">
                        <label for="firstNameAr">الاسم الشخصي (عربي):</label>
                        <input type="text" id="firstNameAr" name="firstNameAr" required placeholder="أحمد">
                    </div>

                    <div class="form-group">
                        <label for="firstNameFr">الاسم الشخصي (فرنسي):</label>
                        <input type="text" id="firstNameFr" name="firstNameFr" required placeholder="Ahmed">
                    </div>

                    <div class="form-group">
                        <label for="familyNameAr">الاسم العائلي (عربي):</label>
                        <input type="text" id="familyNameAr" name="familyNameAr" required placeholder="محمد علي">
                    </div>

                    <div class="form-group">
                        <label for="familyNameFr">الاسم العائلي (فرنسي):</label>
                        <input type="text" id="familyNameFr" name="familyNameFr" required placeholder="Mohamed Ali">
                    </div>

                    <div class="form-group">
                        <label for="birthPlaceAr">مكان الازدياد (عربي):</label>
                        <input type="text" id="birthPlaceAr" name="birthPlaceAr" required placeholder="الرباط">
                    </div>

                    <div class="form-group">
                        <label for="birthPlaceFr">مكان الازدياد (فرنسي):</label>
                        <input type="text" id="birthPlaceFr" name="birthPlaceFr" required placeholder="Rabat">
                    </div>

                    <div class="form-group">
                        <label for="birthDate">تاريخ الازدياد (ميلادي):</label>
                        <input type="date" id="birthDate" name="birthDate" required>
                    </div>

                    <div class="form-group">
                        <label for="hijriDate">التاريخ الهجري:</label>
                        <input type="text" id="hijriDate" name="hijriDate" placeholder="مثال: 5 رجب 1445">
                    </div>

                    <div class="form-group">
                        <label for="gender">الجنس:</label>
                        <select id="gender" name="gender" required>
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="fatherNameAr">اسم الوالد (عربي):</label>
                        <input type="text" id="fatherNameAr" name="fatherNameAr" required placeholder="محمد علي حسن">
                    </div>

                    <div class="form-group">
                        <label for="fatherNameFr">اسم الوالد (فرنسي):</label>
                        <input type="text" id="fatherNameFr" name="fatherNameFr" required placeholder="Mohamed Ali Hassan">
                    </div>

                    <div class="form-group">
                        <label for="motherNameAr">اسم الوالدة (عربي):</label>
                        <input type="text" id="motherNameAr" name="motherNameAr" required placeholder="فاطمة أحمد محمد">
                    </div>

                    <div class="form-group">
                        <label for="motherNameFr">اسم الوالدة (فرنسي):</label>
                        <input type="text" id="motherNameFr" name="motherNameFr" required placeholder="Fatima Ahmed Mohamed">
                    </div>

                    <div class="form-group">
                        <label for="actNumber">رقم القيد:</label>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="actNumberMode" value="auto" checked onchange="toggleActNumberMode()"> تلقائي
                            </label>
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="actNumberMode" value="manual" onchange="toggleActNumberMode()"> يدوي
                            </label>
                        </div>
                        <input type="text" id="actNumber" name="actNumber" required readonly style="background-color: #f8f9fa;">
                        <small id="actNumberHelp" style="color: #6c757d; font-size: 12px;">سيتم توليد الرقم تلقائياً بصيغة: رقم/سنة</small>
                    </div>

                    <div class="form-group">
                        <label for="registrationDate">تاريخ التسجيل:</label>
                        <input type="date" id="registrationDate" name="registrationDate" required>
                    </div>

                    <button type="submit" class="btn btn-primary">💾 حفظ البيانات</button>
                    <button type="button" class="btn btn-warning" onclick="clearForm()">🗑️ مسح النموذج</button>
                </form>
            </div>

            <!-- Certificate Display Section -->
            <div class="form-section">
                <h2>📄 عرض الشهادة الكاملة</h2>

                <!-- Upload Button -->
                <div style="text-align: center; margin-bottom: 20px;">
                    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()" style="font-size: 18px; padding: 15px 30px;">
                        📷 تحميل الشهادة الكاملة
                    </button>
                </div>

                <!-- Display Area -->
                <div class="upload-area" id="uploadArea" style="background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 10px; padding: 30px; text-align: center; min-height: 300px; transition: all 0.3s ease;">
                    <div id="uploadContent">
                        <div style="color: #6c757d; font-size: 48px; margin-bottom: 20px;">📄</div>
                        <h3 style="color: #6c757d; margin-bottom: 15px;">منطقة عرض الشهادة</h3>
                        <p style="color: #6c757d; margin-bottom: 20px;">استخدم الزر أعلاه لتحميل الشهادة</p>
                        <p style="color: #95a5a6; font-size: 14px;">يدعم: JPG, PNG, PDF</p>
                    </div>

                    <!-- Uploaded Image Display -->
                    <div id="imageDisplay" style="display: none;">
                        <div id="imageContainer" style="margin-bottom: 15px; position: relative; overflow: hidden; border-radius: 8px; border: 1px solid #ddd; cursor: zoom-in;">
                            <img id="uploadedImage" style="max-width: 100%; max-height: 500px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; transform-origin: center;">
                        </div>
                    </div>
                </div>

                <!-- Image Controls (Outside Upload Area) -->
                <div id="imageControls" style="display: none; margin-top: 15px; text-align: center;">
                    <div style="margin-bottom: 10px; color: #6c757d; font-size: 14px;">
                        💡 اضغط على الصورة للتكبير • مرر عجلة الفأرة للتكبير والتصغير
                    </div>
                    <div style="margin-bottom: 15px;">
                        <button class="btn btn-primary" onclick="zoomIn(event)" style="margin: 3px; font-size: 18px;">🔍+</button>
                        <button class="btn btn-primary" onclick="zoomOut(event)" style="margin: 3px; font-size: 18px;">🔍-</button>
                        <button class="btn btn-secondary" onclick="resetZoom(event)" style="margin: 3px;">🔄 إعادة تعيين</button>
                    </div>
                    <button class="btn btn-secondary" onclick="removeImage(event)" style="margin: 5px;">🗑️ إزالة الصورة</button>
                    <button class="btn btn-success" onclick="downloadImage(event)" style="margin: 5px;">💾 حفظ الصورة</button>
                </div>

                <!-- Hidden File Input -->
                <input type="file" id="fileInput" accept="image/*,.pdf" style="display: none;" onchange="handleFileUpload(event)">

                <!-- Instructions -->
                <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border: 1px solid #c3e6cb; border-radius: 8px;">
                    <h4 style="color: #155724; margin-bottom: 10px;">📋 تعليمات الاستخدام:</h4>
                    <ul style="color: #155724; text-align: right; margin: 0; padding-right: 20px;">
                        <li>قم بتحميل صورة الشهادة الكاملة للمواطن</li>
                        <li>اقرأ المعلومات من الشهادة المعروضة</li>
                        <li>أدخل البيانات يدوي<|im_start|> في النموذج على اليسار</li>
                        <li>تأكد من صحة جميع المعلومات قبل الحفظ</li>
                        <li>يمكنك تكبير الصورة لرؤية التفاصيل بوضوح</li>
                    </ul>
                </div>

                <!-- Backup Section -->
                <div style="margin-top: 20px; padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
                    <h4 style="color: #856404; margin-bottom: 15px;">💾 النسخ الاحتياطي والأرشفة</h4>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;">
                        <button type="button" class="btn btn-primary" onclick="exportAllData()">📤 تصدير جميع البيانات</button>
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('importFile').click()">📥 استيراد البيانات</button>
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('previewFile').click()">👁️ معاينة النسخة الاحتياطية</button>
                        <button type="button" class="btn btn-success" onclick="saveCurrentCertificateImage()">💾 حفظ الشهادة الحالية</button>
                        <button type="button" class="btn btn-warning" onclick="autoBackup()">🔄 نسخ احتياطي تلقائي</button>
                    </div>
                    <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(event)">
                    <input type="file" id="previewFile" accept=".json" style="display: none;" onchange="previewBackupData(event)">
                    <div style="margin-top: 10px; font-size: 12px; color: #856404; text-align: center;">
                        💡 يُنصح بعمل نسخة احتياطية يومياً لضمان عدم فقدان البيانات
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script>
        // Global variables
        let citizens = JSON.parse(localStorage.getItem('citizens')) || [];
        let editingId = null;
        let currentCertificateImage = null; // Store current certificate image

        // Performance optimization variables
        let citizensCache = new Map();
        let lastDataUpdate = 0;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatistics();

            // Set today's date as default for registration
            document.getElementById('registrationDate').value = new Date().toISOString().split('T')[0];

            // Generate automatic act number
            generateActNumber();

            // Check for edit parameters in URL
            checkForEditParameters();
        });

        // Check for edit parameters in URL
        function checkForEditParameters() {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('edit') === 'true') {
                const citizenId = parseInt(urlParams.get('id'));

                // Fill form with citizen data
                document.getElementById('firstNameAr').value = urlParams.get('firstNameAr') || '';
                document.getElementById('firstNameFr').value = urlParams.get('firstNameFr') || '';
                document.getElementById('familyNameAr').value = urlParams.get('familyNameAr') || '';
                document.getElementById('familyNameFr').value = urlParams.get('familyNameFr') || '';
                document.getElementById('birthPlaceAr').value = urlParams.get('birthPlaceAr') || '';
                document.getElementById('birthPlaceFr').value = urlParams.get('birthPlaceFr') || '';
                document.getElementById('birthDate').value = urlParams.get('birthDate') || '';
                document.getElementById('hijriDate').value = urlParams.get('hijriDate') || '';
                document.getElementById('gender').value = urlParams.get('gender') || '';
                document.getElementById('fatherNameAr').value = urlParams.get('fatherNameAr') || '';
                document.getElementById('fatherNameFr').value = urlParams.get('fatherNameFr') || '';
                document.getElementById('motherNameAr').value = urlParams.get('motherNameAr') || '';
                document.getElementById('motherNameFr').value = urlParams.get('motherNameFr') || '';
                document.getElementById('actNumber').value = urlParams.get('actNumber') || '';
                document.getElementById('registrationDate').value = urlParams.get('registrationDate') || '';

                // Set editing mode
                editingId = citizenId;

                // Update form title
                const formTitle = document.querySelector('.form-section h2');
                formTitle.textContent = '✏️ تعديل بيانات المواطن';
                formTitle.style.color = '#f39c12';

                // Update submit button
                const submitButton = document.querySelector('button[type="submit"]');
                submitButton.textContent = '💾 تحديث البيانات';
                submitButton.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';

                // Show alert
                showAlert('📝 وضع التعديل مفعل\nيمكنك الآن تعديل البيانات والضغط على "تحديث البيانات"', 'success');

                // Clear URL parameters
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }

        // Generate automatic act number
        function generateActNumber() {
            const currentYear = new Date().getFullYear();

            // Get all citizens from current year
            const currentYearCitizens = citizens.filter(citizen => {
                const actParts = citizen.actNumber.split('/');
                return actParts.length === 2 && parseInt(actParts[1]) === currentYear;
            });

            // Find the highest sequential number for current year
            let maxSequential = 0;
            currentYearCitizens.forEach(citizen => {
                const actParts = citizen.actNumber.split('/');
                if (actParts.length === 2) {
                    const sequential = parseInt(actParts[0]);
                    if (sequential > maxSequential) {
                        maxSequential = sequential;
                    }
                }
            });

            // Generate new act number
            const newSequential = maxSequential + 1;
            const newActNumber = `${newSequential}/${currentYear}`;

            document.getElementById('actNumber').value = newActNumber;
        }

        // Toggle act number mode
        function toggleActNumberMode() {
            const mode = document.querySelector('input[name="actNumberMode"]:checked').value;
            const actNumberInput = document.getElementById('actNumber');
            const helpText = document.getElementById('actNumberHelp');

            if (mode === 'auto') {
                actNumberInput.readOnly = true;
                actNumberInput.style.backgroundColor = '#f8f9fa';
                helpText.textContent = 'سيتم توليد الرقم تلقائياً بصيغة: رقم/سنة';
                generateActNumber();
            } else {
                actNumberInput.readOnly = false;
                actNumberInput.style.backgroundColor = 'white';
                actNumberInput.value = '';
                helpText.textContent = 'أدخل رقم القيد يدوياً (مثال: 20/2025)';
                actNumberInput.focus();
            }
        }

        // Form submission
        document.getElementById('citizenForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveCitizen();
        });

        // Save citizen data
        function saveCitizen() {
            const formData = new FormData(document.getElementById('citizenForm'));
            const citizenData = {
                id: editingId || Date.now(),
                firstNameAr: formData.get('firstNameAr'),
                firstNameFr: formData.get('firstNameFr'),
                familyNameAr: formData.get('familyNameAr'),
                familyNameFr: formData.get('familyNameFr'),
                birthPlaceAr: formData.get('birthPlaceAr'),
                birthPlaceFr: formData.get('birthPlaceFr'),
                birthDate: formData.get('birthDate'),
                hijriDate: formData.get('hijriDate'),
                gender: formData.get('gender'),
                fatherNameAr: formData.get('fatherNameAr'),
                fatherNameFr: formData.get('fatherNameFr'),
                motherNameAr: formData.get('motherNameAr'),
                motherNameFr: formData.get('motherNameFr'),
                actNumber: formData.get('actNumber'),
                registrationDate: formData.get('registrationDate'),
                createdAt: editingId ? citizens.find(c => c.id === editingId).createdAt : new Date().toISOString(),
                // Add certificate image if available
                certificateImage: currentCertificateImage ? {
                    data: currentCertificateImage.data,
                    fileName: currentCertificateImage.fileName,
                    uploadDate: currentCertificateImage.uploadDate
                } : null
            };

            // Validate required fields with specific messages
            const validationErrors = [];

            if (!citizenData.firstNameAr) {
                validationErrors.push('الاسم الشخصي (عربي)');
            }
            if (!citizenData.firstNameFr) {
                validationErrors.push('الاسم الشخصي (فرنسي)');
            }
            if (!citizenData.familyNameAr) {
                validationErrors.push('الاسم العائلي (عربي)');
            }
            if (!citizenData.familyNameFr) {
                validationErrors.push('الاسم العائلي (فرنسي)');
            }
            if (!citizenData.birthPlaceAr) {
                validationErrors.push('مكان الازدياد (عربي)');
            }
            if (!citizenData.birthPlaceFr) {
                validationErrors.push('مكان الازدياد (فرنسي)');
            }
            if (!citizenData.birthDate) {
                validationErrors.push('تاريخ الازدياد');
            }
            if (!citizenData.gender) {
                validationErrors.push('الجنس');
            }
            if (!citizenData.fatherNameAr) {
                validationErrors.push('اسم الوالد (عربي)');
            }
            if (!citizenData.fatherNameFr) {
                validationErrors.push('اسم الوالد (فرنسي)');
            }
            if (!citizenData.motherNameAr) {
                validationErrors.push('اسم الوالدة (عربي)');
            }
            if (!citizenData.motherNameFr) {
                validationErrors.push('اسم الوالدة (فرنسي)');
            }
            if (!citizenData.actNumber) {
                validationErrors.push('رقم القيد');
            }
            if (!citizenData.registrationDate) {
                validationErrors.push('تاريخ التسجيل');
            }

            if (validationErrors.length > 0) {
                const errorMessage = `⚠️ الحقول التالية مطلوبة:\n• ${validationErrors.join('\n• ')}`;
                showAlert(errorMessage, 'error');
                return;
            }

            // Additional validations
            if (citizenData.hijriDate && citizenData.hijriDate.trim() === '') {
                showAlert('⚠️ يرجى إدخال التاريخ الهجري أو تركه فارغ<|im_start|>', 'error');
                return;
            }

            // Check for duplicate act number
            const existingCitizen = citizens.find(c => c.actNumber === citizenData.actNumber && c.id !== editingId);
            if (existingCitizen) {
                showAlert('❌ رقم القيد موجود مسبق<|im_start|>\nيرجى استخدام رقم قيد مختلف أو التحقق من البيانات الموجودة', 'error');
                return;
            }

            // Validate act number format (should be number/year)
            const actNumberPattern = /^\d+\/\d{4}$/;
            if (!actNumberPattern.test(citizenData.actNumber)) {
                showAlert('❌ تنسيق رقم القيد غير صحيح\nيجب أن يكون بصيغة: رقم/سنة\nمثال: 20/2025', 'error');
                return;
            }

            if (editingId) {
                // Update existing citizen
                const index = citizens.findIndex(c => c.id === editingId);
                citizens[index] = citizenData;
                const imageStatus = currentCertificateImage ? '\n📷 تم ربط الشهادة الكاملة بالمواطن' : '';
                showAlert(`✅ تم تحديث بيانات المواطن بنجاح${imageStatus}\nيمكنك الآن طباعة الشهادة أو إضافة مواطن جديد`, 'success');
                editingId = null;
            } else {
                // Add new citizen
                citizens.push(citizenData);
                const imageStatus = currentCertificateImage ? '\n📷 تم ربط الشهادة الكاملة بالمواطن' : '';
                showAlert(`✅ تم حفظ بيانات المواطن بنجاح${imageStatus}\nتم إضافة المواطن إلى قاعدة البيانات`, 'success');
            }

            // Save to localStorage
            localStorage.setItem('citizens', JSON.stringify(citizens));

            // Reset form and update display
            clearForm();
            updateStatistics();
        }

        // Handle file upload with compression
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file type
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
            if (!validTypes.includes(file.type)) {
                showAlert('❌ نوع الملف غير مدعوم\nيرجى اختيار صورة (JPG, PNG) أو ملف PDF', 'error');
                return;
            }

            // Validate file size (max 15MB)
            if (file.size > 15 * 1024 * 1024) {
                showAlert('❌ حجم الملف كبير جداً\nيرجى اختيار ملف أصغر من 15 ميجابايت', 'error');
                return;
            }

            // Show loading message
            showAlert('⏳ جاري تحميل وضغط الصورة...', 'info');

            const reader = new FileReader();
            reader.onload = function(e) {
                if (file.type.startsWith('image/')) {
                    compressImage(e.target.result, file.name);
                } else {
                    displayUploadedImage(e.target.result, file.name);
                }
            };
            reader.readAsDataURL(file);
        }

        // Compress image to reduce storage size
        function compressImage(imageSrc, fileName) {
            const img = new Image();
            img.onload = function() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Calculate new dimensions (max 1920x1080 for certificates)
                let { width, height } = img;
                const maxWidth = 1920;
                const maxHeight = 1080;

                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw and compress
                ctx.drawImage(img, 0, 0, width, height);

                // Convert to compressed JPEG (quality 0.8)
                const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.8);

                // Calculate compression ratio
                const originalSize = imageSrc.length;
                const compressedSize = compressedDataUrl.length;
                const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

                showAlert(`✅ تم ضغط الصورة بنجاح\nتوفير في المساحة: ${compressionRatio}%\nالحجم الجديد: ${(compressedSize / 1024 / 1024).toFixed(2)} ميجابايت`, 'success');

                displayUploadedImage(compressedDataUrl, fileName);
            };
            img.src = imageSrc;
        }

        // Display uploaded image
        function displayUploadedImage(imageSrc, fileName) {
            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');
            const uploadedImage = document.getElementById('uploadedImage');

            uploadContent.style.display = 'none';
            imageDisplay.style.display = 'block';
            imageControls.style.display = 'block';
            uploadedImage.src = imageSrc;

            // Store certificate image data
            currentCertificateImage = {
                data: imageSrc,
                fileName: fileName,
                uploadDate: new Date().toISOString()
            };

            // Update upload area style
            const uploadArea = document.getElementById('uploadArea');
            uploadArea.style.border = '2px solid #28a745';
            uploadArea.style.background = '#f8fff9';

            // Initialize zoom functionality
            initializeImageZoom();

            showAlert(`✅ تم تحميل الشهادة الكاملة بنجاح\nاسم الملف: ${fileName}\nيمكنك الآن قراءة البيانات وإدخالها في النموذج\n\n💡 ستتم ربط هذه الصورة بالمواطن عند الحفظ`, 'success');
        }

        // Remove uploaded image
        function removeImage(event) {
            if (event) event.stopPropagation();

            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');

            uploadContent.style.display = 'block';
            imageDisplay.style.display = 'none';
            imageControls.style.display = 'none';
            fileInput.value = '';

            // Reset upload area style
            uploadArea.style.border = '2px dashed #dee2e6';
            uploadArea.style.background = '#f8f9fa';

            showAlert('🗑️ تم إزالة الشهادة', 'success');
        }



        // Initialize image zoom functionality
        let currentZoom = 1;
        let isDragging = false;
        let startX, startY, scrollLeft, scrollTop;

        function initializeImageZoom() {
            const imageContainer = document.getElementById('imageContainer');
            const uploadedImage = document.getElementById('uploadedImage');

            // Reset zoom
            currentZoom = 1;
            uploadedImage.style.transform = 'scale(1)';

            // Add wheel event for zooming (for desktop with mouse)
            imageContainer.addEventListener('wheel', handleZoom, { passive: false });

            // Add click event for zooming (for laptops/touchpads)
            imageContainer.addEventListener('click', handleClick);

            // Add mouse events for dragging when zoomed
            imageContainer.addEventListener('mousedown', startDrag);
            imageContainer.addEventListener('mousemove', drag);
            imageContainer.addEventListener('mouseup', endDrag);
            imageContainer.addEventListener('mouseleave', endDrag);

            // Add touch events for mobile devices
            imageContainer.addEventListener('touchstart', handleTouchStart, { passive: false });
            imageContainer.addEventListener('touchmove', handleTouchMove, { passive: false });
            imageContainer.addEventListener('touchend', handleTouchEnd);
        }

        // Handle click for zoom
        function handleClick(e) {
            e.preventDefault();

            const imageContainer = document.getElementById('imageContainer');
            const uploadedImage = document.getElementById('uploadedImage');
            const rect = imageContainer.getBoundingClientRect();

            // Calculate click position
            const clickX = e.clientX - rect.left;
            const clickY = e.clientY - rect.top;

            // Set transform origin to click position
            const percentX = (clickX / rect.width) * 100;
            const percentY = (clickY / rect.height) * 100;

            uploadedImage.style.transformOrigin = `${percentX}% ${percentY}%`;

            // Toggle zoom
            if (currentZoom <= 1) {
                currentZoom = 2; // Zoom in
            } else {
                currentZoom = 1; // Zoom out
            }

            uploadedImage.style.transform = `scale(${currentZoom})`;
            updateCursor();
        }

        function handleZoom(e) {
            e.preventDefault();

            const imageContainer = document.getElementById('imageContainer');
            const uploadedImage = document.getElementById('uploadedImage');
            const rect = imageContainer.getBoundingClientRect();

            // Calculate mouse position relative to image
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            // Calculate zoom
            const zoomIntensity = 0.1;
            const wheel = e.deltaY < 0 ? 1 : -1;
            const zoom = Math.exp(wheel * zoomIntensity);

            currentZoom *= zoom;
            currentZoom = Math.min(Math.max(0.5, currentZoom), 5); // Limit zoom between 0.5x and 5x

            // Set transform origin to mouse position
            const percentX = (mouseX / rect.width) * 100;
            const percentY = (mouseY / rect.height) * 100;

            uploadedImage.style.transformOrigin = `${percentX}% ${percentY}%`;
            uploadedImage.style.transform = `scale(${currentZoom})`;

            updateCursor();
        }

        // Update cursor based on zoom level
        function updateCursor() {
            const imageContainer = document.getElementById('imageContainer');
            if (currentZoom > 1) {
                imageContainer.style.cursor = 'grab';
            } else {
                imageContainer.style.cursor = 'zoom-in';
            }
        }

        // Zoom in function (for button)
        function zoomIn(event) {
            if (event) event.stopPropagation();
            const uploadedImage = document.getElementById('uploadedImage');
            currentZoom = Math.min(currentZoom * 1.5, 5);
            uploadedImage.style.transform = `scale(${currentZoom})`;
            updateCursor();
        }

        // Zoom out function (for button)
        function zoomOut(event) {
            if (event) event.stopPropagation();
            const uploadedImage = document.getElementById('uploadedImage');
            currentZoom = Math.max(currentZoom / 1.5, 0.5);
            uploadedImage.style.transform = `scale(${currentZoom})`;
            updateCursor();
        }

        function startDrag(e) {
            if (currentZoom <= 1) return;

            isDragging = true;
            const imageContainer = document.getElementById('imageContainer');
            imageContainer.style.cursor = 'grabbing';

            startX = e.pageX - imageContainer.offsetLeft;
            startY = e.pageY - imageContainer.offsetTop;
            scrollLeft = imageContainer.scrollLeft;
            scrollTop = imageContainer.scrollTop;
        }

        function drag(e) {
            if (!isDragging || currentZoom <= 1) return;

            e.preventDefault();
            const imageContainer = document.getElementById('imageContainer');
            const x = e.pageX - imageContainer.offsetLeft;
            const y = e.pageY - imageContainer.offsetTop;
            const walkX = (x - startX) * 2;
            const walkY = (y - startY) * 2;

            imageContainer.scrollLeft = scrollLeft - walkX;
            imageContainer.scrollTop = scrollTop - walkY;
        }

        function endDrag() {
            isDragging = false;
            const imageContainer = document.getElementById('imageContainer');
            if (currentZoom > 1) {
                imageContainer.style.cursor = 'grab';
            } else {
                imageContainer.style.cursor = 'zoom-in';
            }
        }

        // Reset zoom function
        function resetZoom(event) {
            if (event) event.stopPropagation();
            const uploadedImage = document.getElementById('uploadedImage');
            const imageContainer = document.getElementById('imageContainer');

            currentZoom = 1;
            uploadedImage.style.transform = 'scale(1)';
            uploadedImage.style.transformOrigin = 'center';
            imageContainer.style.cursor = 'zoom-in';
            imageContainer.scrollLeft = 0;
            imageContainer.scrollTop = 0;

            showAlert('🔄 تم إعادة تعيين التكبير', 'success');
        }

        // Touch events for mobile devices
        let touchStartDistance = 0;
        let lastTouchTime = 0;

        function handleTouchStart(e) {
            if (e.touches.length === 2) {
                // Pinch to zoom
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                touchStartDistance = Math.hypot(
                    touch1.clientX - touch2.clientX,
                    touch1.clientY - touch2.clientY
                );
            } else if (e.touches.length === 1) {
                // Double tap to zoom
                const currentTime = new Date().getTime();
                const tapLength = currentTime - lastTouchTime;
                if (tapLength < 500 && tapLength > 0) {
                    // Double tap detected
                    const touch = e.touches[0];
                    const rect = e.target.getBoundingClientRect();
                    const touchX = touch.clientX - rect.left;
                    const touchY = touch.clientY - rect.top;

                    const uploadedImage = document.getElementById('uploadedImage');
                    const percentX = (touchX / rect.width) * 100;
                    const percentY = (touchY / rect.height) * 100;

                    uploadedImage.style.transformOrigin = `${percentX}% ${percentY}%`;

                    if (currentZoom <= 1) {
                        currentZoom = 2;
                    } else {
                        currentZoom = 1;
                    }

                    uploadedImage.style.transform = `scale(${currentZoom})`;
                    updateCursor();
                }
                lastTouchTime = currentTime;
            }
        }

        function handleTouchMove(e) {
            if (e.touches.length === 2) {
                e.preventDefault();
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                const currentDistance = Math.hypot(
                    touch1.clientX - touch2.clientX,
                    touch1.clientY - touch2.clientY
                );

                if (touchStartDistance > 0) {
                    const scale = currentDistance / touchStartDistance;
                    currentZoom = Math.min(Math.max(currentZoom * scale, 0.5), 5);

                    const uploadedImage = document.getElementById('uploadedImage');
                    uploadedImage.style.transform = `scale(${currentZoom})`;
                    updateCursor();

                    touchStartDistance = currentDistance;
                }
            }
        }

        function handleTouchEnd(e) {
            touchStartDistance = 0;
        }

        // Download image
        function downloadImage(event) {
            if (event) event.stopPropagation();
            const uploadedImage = document.getElementById('uploadedImage');
            if (!uploadedImage.src) {
                showAlert('❌ لا توجد صورة للتحميل', 'error');
                return;
            }

            const link = document.createElement('a');
            link.href = uploadedImage.src;
            link.download = 'certificate_' + new Date().getTime() + '.jpg';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showAlert('💾 تم تحميل الصورة بنجاح', 'success');
        }









        // Clear form
        function clearForm() {
            document.getElementById('citizenForm').reset();
            document.getElementById('registrationDate').value = new Date().toISOString().split('T')[0];
            editingId = null;

            // Reset to automatic mode
            document.querySelector('input[name="actNumberMode"][value="auto"]').checked = true;
            toggleActNumberMode();

            // Clear certificate image
            currentCertificateImage = null;

            // Reset image display
            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');
            const uploadArea = document.getElementById('uploadArea');

            if (imageDisplay.style.display !== 'none') {
                uploadContent.style.display = 'block';
                imageDisplay.style.display = 'none';
                imageControls.style.display = 'none';
                uploadArea.style.border = '2px dashed #dee2e6';
                uploadArea.style.background = '#f8f9fa';
            }

            // Reset form title and button to default
            const formTitle = document.querySelector('.form-section h2');
            formTitle.textContent = '👥 إدخال بيانات المواطن';
            formTitle.style.color = '#2c3e50';

            const submitButton = document.querySelector('button[type="submit"]');
            submitButton.textContent = '💾 حفظ البيانات';
            submitButton.style.background = 'linear-gradient(135deg, #27ae60, #229954)';
        }

        // Update statistics with IndexedDB support
        async function updateStatistics() {
            try {
                // Try to get fresh data from IndexedDB if available
                if (typeof citizensDB !== 'undefined' && citizensDB.getAllCitizens) {
                    citizens = await citizensDB.getAllCitizens();
                    console.log(`تم تحديث البيانات من IndexedDB: ${citizens.length} مواطن`);
                } else {
                    // Fallback to localStorage
                    citizens = JSON.parse(localStorage.getItem('citizens')) || [];
                    console.log(`تم تحديث البيانات من localStorage: ${citizens.length} مواطن`);
                }
            } catch (error) {
                console.error('خطأ في تحديث البيانات:', error);
                // Use current citizens array as fallback
            }

            const total = citizens.length;
            const males = citizens.filter(c => c.gender === 'ذكر').length;
            const females = citizens.filter(c => c.gender === 'أنثى').length;

            const today = new Date().toISOString().split('T')[0];
            const todayRegistrations = citizens.filter(c => c.registrationDate === today).length;

            document.getElementById('totalCitizens').textContent = total.toLocaleString();
            document.getElementById('maleCount').textContent = males.toLocaleString();
            document.getElementById('femaleCount').textContent = females.toLocaleString();
            document.getElementById('todayRegistrations').textContent = todayRegistrations.toLocaleString();

            console.log('تم تحديث الإحصائيات:', {
                total,
                males,
                females,
                todayRegistrations
            });
        }

        // Show alert
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            // Format message with line breaks for better display
            const formattedMessage = message.replace(/\n/g, '<br>');

            alertContainer.innerHTML = `
                <div class="alert ${alertClass}" style="white-space: pre-line;">
                    ${formattedMessage}
                </div>
            `;

            // Auto hide after 5 seconds for error messages (more time to read)
            const hideTime = type === 'error' ? 5000 : 3000;
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, hideTime);

            // Scroll to alert
            alertContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // Export data
        function exportData() {
            const dataStr = JSON.stringify(citizens, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'citizens-data.json';
            link.click();
        }

        // Import data
        function importData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const fileContent = JSON.parse(e.target.result);
                    let importedData;
                    let backupInfo = '';

                    // Check if it's new backup format or old format
                    if (fileContent.data && Array.isArray(fileContent.data)) {
                        // New backup format
                        importedData = fileContent.data;
                        backupInfo = `\nتاريخ النسخة الاحتياطية: ${new Date(fileContent.exportDate).toLocaleDateString('ar-EG')}\nالإصدار: ${fileContent.version || '1.0'}`;
                    } else if (Array.isArray(fileContent)) {
                        // Old format (direct array)
                        importedData = fileContent;
                    } else {
                        showAlert('❌ تنسيق الملف غير صحيح\nيرجى اختيار ملف نسخة احتياطية صالح', 'error');
                        return;
                    }

                    // Show preview before importing
                    const withCertificates = importedData.filter(c => c.certificateImage).length;
                    const confirmMessage = `هل تريد استيراد هذه البيانات؟\n\nعدد المواطنين: ${importedData.length}\nلديهم شهادات كاملة: ${withCertificates}${backupInfo}\n\n⚠️ تحذير: سيتم استبدال جميع البيانات الحالية!`;

                    if (confirm(confirmMessage)) {
                        citizens = importedData;
                        localStorage.setItem('citizens', JSON.stringify(citizens));
                        updateStatistics();
                        showAlert(`✅ تم استيراد البيانات بنجاح\nعدد المواطنين المستوردين: ${importedData.length}\nلديهم شهادات كاملة: ${withCertificates}`, 'success');
                    }
                } catch (error) {
                    showAlert('❌ خطأ في قراءة الملف\nتأكد من أن الملف غير تالف ومن تنسيق JSON صحيح', 'error');
                }
            };
            reader.readAsText(file);

            // Reset file input
            event.target.value = '';
        }

        // Export all data with backup
        function exportAllData() {
            if (citizens.length === 0) {
                showAlert('⚠️ لا توجد بيانات للتصدير\nيرجى إضافة مواطنين أولاً', 'error');
                return;
            }

            const currentDate = new Date();
            const dateStr = currentDate.toISOString().split('T')[0];
            const timeStr = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

            const backupData = {
                exportDate: currentDate.toISOString(),
                totalCitizens: citizens.length,
                withCertificates: citizens.filter(c => c.certificateImage).length,
                version: '1.0',
                data: citizens
            };

            const dataStr = JSON.stringify(backupData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `citizens_backup_${dateStr}_${timeStr}.json`;
            link.click();
            URL.revokeObjectURL(url);

            showAlert(`✅ تم تصدير النسخة الاحتياطية بنجاح\nعدد المواطنين: ${citizens.length}\nالتاريخ: ${dateStr}`, 'success');
        }

        // Preview backup data without importing
        function previewBackupData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const fileContent = JSON.parse(e.target.result);
                    let previewData;
                    let backupInfo = '';

                    // Check if it's new backup format or old format
                    if (fileContent.data && Array.isArray(fileContent.data)) {
                        // New backup format
                        previewData = fileContent.data;
                        backupInfo = `تاريخ النسخة الاحتياطية: ${new Date(fileContent.exportDate).toLocaleDateString('ar-EG')}\nالوقت: ${new Date(fileContent.exportDate).toLocaleTimeString('ar-EG')}\nالإصدار: ${fileContent.version || '1.0'}\n\n`;
                    } else if (Array.isArray(fileContent)) {
                        // Old format (direct array)
                        previewData = fileContent;
                        backupInfo = 'نسخة احتياطية قديمة (بدون معلومات إضافية)\n\n';
                    } else {
                        showAlert('❌ تنسيق الملف غير صحيح\nيرجى اختيار ملف نسخة احتياطية صالح', 'error');
                        return;
                    }

                    // Calculate statistics
                    const withCertificates = previewData.filter(c => c.certificateImage).length;
                    const withoutCertificates = previewData.length - withCertificates;

                    // Get sample names (first 5)
                    const sampleNames = previewData.slice(0, 5).map(c =>
                        `• ${c.firstNameAr || 'غير محدد'} ${c.familyNameAr || 'غير محدد'} (${c.actNumber || 'بدون رقم'})`
                    ).join('\n');

                    const previewMessage = `📋 معاينة محتويات النسخة الاحتياطية:\n\n${backupInfo}📊 الإحصائيات:\n• إجمالي المواطنين: ${previewData.length}\n• لديهم شهادات كاملة: ${withCertificates}\n• بدون شهادات: ${withoutCertificates}\n\n👥 عينة من الأسماء:\n${sampleNames}${previewData.length > 5 ? '\n... والمزيد' : ''}`;

                    alert(previewMessage);

                } catch (error) {
                    showAlert('❌ خطأ في قراءة الملف\nتأكد من أن الملف غير تالف ومن تنسيق JSON صحيح', 'error');
                }
            };
            reader.readAsText(file);

            // Reset file input
            event.target.value = '';
        }

        // Save current certificate image separately
        function saveCurrentCertificateImage() {
            if (!currentCertificateImage) {
                showAlert('⚠️ لا توجد شهادة محملة حالياً\nيرجى تحميل شهادة أولاً', 'error');
                return;
            }

            // Get current form data for filename
            const firstNameAr = document.getElementById('firstNameAr').value || 'غير_محدد';
            const familyNameAr = document.getElementById('familyNameAr').value || 'غير_محدد';
            const actNumber = document.getElementById('actNumber').value || 'غير_محدد';

            const currentDate = new Date();
            const dateStr = currentDate.toISOString().split('T')[0];

            // Create filename
            const filename = `certificate_${firstNameAr}_${familyNameAr}_${actNumber}_${dateStr}.jpg`;

            // Create download link
            const link = document.createElement('a');
            link.href = currentCertificateImage.data;
            link.download = filename.replace(/[^a-zA-Z0-9\u0600-\u06FF._-]/g, '_'); // Clean filename
            link.click();

            showAlert(`✅ تم حفظ الشهادة بنجاح\nاسم الملف: ${filename}`, 'success');
        }

        // Auto backup function
        function autoBackup() {
            if (citizens.length === 0) {
                showAlert('⚠️ لا توجد بيانات للنسخ الاحتياطي', 'error');
                return;
            }

            // Check last backup date
            const lastBackup = localStorage.getItem('lastBackupDate');
            const today = new Date().toDateString();

            if (lastBackup === today) {
                const confirmBackup = confirm('تم عمل نسخة احتياطية اليوم بالفعل.\nهل تريد عمل نسخة احتياطية أخرى؟');
                if (!confirmBackup) return;
            }

            // Export data
            exportAllData();

            // Save backup date
            localStorage.setItem('lastBackupDate', today);

            // Schedule next backup reminder (24 hours)
            setTimeout(() => {
                if (citizens.length > 0) {
                    const reminder = confirm('🔔 تذكير: حان وقت النسخة الاحتياطية اليومية\nهل تريد عمل نسخة احتياطية الآن؟');
                    if (reminder) {
                        autoBackup();
                    }
                }
            }, 24 * 60 * 60 * 1000); // 24 hours
        }

        // Check for backup reminder on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Initial statistics update
            updateStatistics();

            const lastBackup = localStorage.getItem('lastBackupDate');
            const today = new Date().toDateString();

            if (lastBackup && lastBackup !== today && citizens.length > 0) {
                setTimeout(() => {
                    const reminder = confirm('🔔 لم يتم عمل نسخة احتياطية اليوم\nهل تريد عمل نسخة احتياطية الآن؟');
                    if (reminder) {
                        autoBackup();
                    }
                }, 3000); // Show after 3 seconds
            }
        });

        // Auto-refresh statistics every 30 seconds
        setInterval(updateStatistics, 30000);

        // Refresh when page becomes visible
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                updateStatistics();
            }
        });
    </script>
</body>
</html>
