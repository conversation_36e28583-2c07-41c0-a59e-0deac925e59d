<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخة موجزة من رسم الوفاة - مكتب الحالة المدنية أيير</title>
    <style>
        /* ===== GENERAL STYLES ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Cairo', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        /* ===== CONTAINER & LAYOUT ===== */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* ===== HEADER STYLES ===== */
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a 0%, #e74c3c 50%, #c41e3a 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 25px 0;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            border: 3px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .header-text h1 {
            font-size: 2em;
            margin: 0 0 8px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 5px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.95em;
            opacity: 0.85;
            font-style: italic;
        }

        .header-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
            font-size: 0.9em;
        }

        .current-time {
            background: rgba(255,255,255,0.15);
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .user-info {
            background: rgba(255,255,255,0.1);
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: 500;
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            background: rgba(255,255,255,0.15);
            border-radius: 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
        }

        /* ===== MAIN CONTENT LAYOUT ===== */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .right-panel {
            display: flex;
            flex-direction: column;
        }

        /* ===== FORM STYLES ===== */
        .form-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
            margin-bottom: 15px;
        }

        .form-section h2 {
            color: #2c3e50;
            margin-bottom: 18px;
            font-size: 1.2em;
            font-weight: 700;
            text-align: center;
            border-bottom: 2px solid #c41e3a;
            padding-bottom: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 0.9em;
        }

        .required {
            color: #e74c3c;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.9em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 0 3px rgba(196, 30, 58, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* ===== BUTTON STYLES ===== */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 3px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
            background: linear-gradient(135deg, #c0392b 0%, #e74c3c 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
            background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .form-actions {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 18px;
            gap: 8px;
        }

        /* ===== ALERT STYLES ===== */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* ===== CERTIFICATE PREVIEW STYLES ===== */
        .preview-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 18px;
            border: 1px solid #e9ecef;
            height: fit-content;
        }

        .certificate-preview {
            min-height: 500px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: white;
            margin-bottom: 15px;
            overflow: auto;
            position: relative;
            padding: 12px;
        }

        .preview-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 30px;
        }

        /* ===== CERTIFICATE CONTENT STYLES ===== */
        .certificate-content {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 20px; /* زيادة الحشو */
            font-family: 'Times New Roman', serif;
            line-height: 1.7; /* زيادة المسافة بين الأسطر */
            background: white;
            border: 2px solid #000;
            min-height: 450px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            font-size: 14px; /* تكبير الخط الأساسي */
        }

        .certificate-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 5px;
            padding-bottom: 5px;
        }

        .header-left {
            text-align: left;
            font-size: 13px; /* تكبير من 11px إلى 13px */
            line-height: 1.6;
            font-weight: 600; /* زيادة السمك */
        }

        .header-right {
            text-align: right;
            font-size: 13px; /* تكبير من 11px إلى 13px */
            line-height: 1.6;
            font-weight: 600; /* زيادة السمك */
        }

        .certificate-title {
            text-align: center;
            font-size: 20px; /* تكبير من 18px إلى 20px */
            font-weight: bold;
            margin: 8px 0; /* زيادة المسافة */
            color: #000;
        }

        .certificate-body {
            font-size: 15px; /* تكبير من 13px إلى 15px */
            line-height: 1.8; /* زيادة المسافة بين الأسطر */
            text-align: right;
            margin: 8px 0; /* زيادة المسافة */
        }

        .field-line {
            margin-bottom: 10px; /* زيادة المسافة بين الحقول */
            display: flex;
            align-items: baseline;
            width: 100%;
        }

        .field-label {
            font-weight: 600;
            margin-left: 10px; /* زيادة المسافة */
            white-space: nowrap;
            min-width: 120px; /* زيادة العرض */
            font-size: 15px; /* تكبير من 13px إلى 15px */
            color: #333; /* لون أفتح للتسميات */
        }

        .field-value {
            flex: 1;
            min-height: 24px; /* زيادة الارتفاع */
            padding: 2px 6px; /* زيادة الحشو */
            color: #000; /* لون أسود قوي للقيم */
            font-size: 15px; /* تكبير من 13px إلى 15px */
            font-weight: 700; /* تسميك القيم المهمة */
        }



        .certificate-footer {
            margin-top: 40px; /* زيادة المسافة العلوية */
            display: flex;
            justify-content: space-between;
            align-items: end;
            font-size: 12px; /* تكبير من 10px إلى 12px */
            padding-top: 20px; /* زيادة الحشو */
        }

        .footer-left {
            text-align: left;
            line-height: 1.5; /* زيادة المسافة بين الأسطر */
            font-weight: 600; /* زيادة السمك */
        }

        .footer-right {
            text-align: right;
            line-height: 1.5; /* زيادة المسافة بين الأسطر */
            font-weight: 600; /* زيادة السمك */
        }

        .certification-text {
            text-align: center;
            margin: 30px 0; /* زيادة المسافة */
            font-size: 14px; /* تكبير من 12px إلى 14px */
            line-height: 1.8; /* زيادة المسافة بين الأسطر */
            font-weight: 600; /* زيادة السمك */
            color: #333;
            padding: 15px; /* زيادة الحشو */
            background: #f9f9f9;
            border-radius: 6px; /* زيادة الانحناء */
            border: 1px solid #e0e0e0;
        }

        /* ===== CERTIFICATE IMAGE STYLES ===== */
        .certificate-image {
            max-width: 100%;
            max-height: 600px;
            width: auto;
            height: auto;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            cursor: zoom-in;
            transition: transform 0.3s ease;
        }

        .certificate-image:hover {
            transform: scale(1.02);
        }

        .image-container {
            text-align: center;
            position: relative;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .image-info {
            margin-top: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
            color: #6c757d;
        }

        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }

        .zoom-btn {
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .zoom-btn:hover {
            background: rgba(0,0,0,0.9);
        }



        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .nav-links {
                flex-direction: column;
                align-items: center;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">نسخة موجزة من رسم الوفاة</div>
                            <div class="department">مكتب الحالة المدنية - أيير</div>
                        </div>
                    </div>
                    <div class="header-left">
                        <div class="current-time" id="currentTime">جاري التحميل...</div>
                        <div class="user-info">👤 مستخدم النظام</div>
                    </div>
                </div>

                <div class="nav-links">
                    <a href="main-dashboard.html" class="nav-link">🏠 الصفحة الرئيسية</a>
                    <a href="citizens-database-indexeddb.html" class="nav-link">🗃️ إدارة البيانات</a>
                    <a href="search-citizens.html" class="nav-link">🔍 البحث في السجلات</a>
                    <a href="dual-birth-certificate.html" class="nav-link">📜 عقود الازدياد</a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Panel - Forms -->
            <div class="left-panel">
                <div id="alertContainer"></div>

                <!-- معلومات الوفاة الأساسية -->
                <div class="form-section">
                    <h2>⚱️ معلومات الوفاة الأساسية</h2>

                    <!-- Hidden Fields for Edit Mode -->
                    <input type="hidden" id="isDeceased" value="true">
                    <input type="hidden" id="originalActNumber" value="">
                    <input type="hidden" id="editMode" value="false">
                    <input type="hidden" id="citizenId" value="">

                    <form id="deathForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="deathPlace">توفي(ت) بـ <span class="required">*</span>:</label>
                                <input type="text" id="deathPlace" name="deathPlace" required placeholder="مكان الوفاة">
                            </div>

                            <div class="form-group">
                                <label for="deathDate">في <span class="required">*</span>:</label>
                                <input type="date" id="deathDate" name="deathDate" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="deathTime">الساعة:</label>
                                <input type="time" id="deathTime" name="deathTime" placeholder="ساعة الوفاة">
                            </div>

                            <div class="form-group">
                                <label for="deathCause">سبب الوفاة:</label>
                                <input type="text" id="deathCause" name="deathCause" placeholder="سبب الوفاة">
                            </div>
                        </div>
                    </form>
                </div>

            <!-- البيانات الشخصية للمتوفى -->
            <div class="form-section">
                <h2>👤 البيانات الشخصية للمتوفى</h2>

                <div class="form-row">
                    <div class="form-group">
                        <label for="personalName">الاسم الشخصي <span class="required">*</span>:</label>
                        <input type="text" id="personalName" name="personalName" required placeholder="الاسم الشخصي للمتوفى">
                    </div>

                    <div class="form-group">
                        <label for="familyName">الاسم العائلي <span class="required">*</span>:</label>
                        <input type="text" id="familyName" name="familyName" required placeholder="الاسم العائلي للمتوفى">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="birthDate">تاريخ الازدياد:</label>
                        <input type="date" id="birthDate" name="birthDate">
                    </div>

                    <div class="form-group">
                        <label for="birthPlace">مكان الازدياد:</label>
                        <input type="text" id="birthPlace" name="birthPlace" placeholder="مكان الازدياد">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="age">العمر عند الوفاة:</label>
                        <div style="position: relative;">
                            <input type="text" id="age" name="age" placeholder="سيتم حسابه تلقائياً" readonly style="background: #f8f9fa; border: 2px solid #e9ecef;">
                            <button type="button" onclick="enableAgeEdit()" style="position: absolute; left: 5px; top: 50%; transform: translateY(-50%); background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;" title="تعديل يدوي">✏️</button>
                        </div>
                        <small style="color: #6c757d; font-size: 0.8em; display: block; margin-top: 4px;">📊 يتم حساب العمر تلقائياً من تاريخ الازدياد وتاريخ الوفاة</small>
                    </div>

                    <div class="form-group">
                        <label for="gender">الجنس:</label>
                        <select id="gender" name="gender">
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="profession">مهنته (ها):</label>
                        <input type="text" id="profession" name="profession" placeholder="مهنة المتوفى">
                    </div>

                    <div class="form-group">
                        <label for="residence">الساكن (ة) بـ:</label>
                        <input type="text" id="residence" name="residence" placeholder="عنوان السكن">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="maritalStatus">الحالة المدنية:</label>
                        <select id="maritalStatus" name="maritalStatus">
                            <option value="">اختر الحالة المدنية</option>
                            <option value="أعزب">أعزب</option>
                            <option value="عزباء">عزباء</option>
                            <option value="متزوج">متزوج</option>
                            <option value="متزوجة">متزوجة</option>
                            <option value="مطلق">مطلق</option>
                            <option value="مطلقة">مطلقة</option>
                            <option value="أرمل">أرمل</option>
                            <option value="أرملة">أرملة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="nationality">الجنسية:</label>
                        <input type="text" id="nationality" name="nationality" placeholder="الجنسية" value="مغربية">
                    </div>
                </div>
            </div>

            <!-- بيانات الوالدين -->
            <div class="form-section">
                <h2>👨‍👩‍👧‍👦 بيانات الوالدين</h2>

                <div class="form-row">
                    <div class="form-group">
                        <label for="fatherName">والده:</label>
                        <input type="text" id="fatherName" name="fatherName" placeholder="اسم الوالد">
                    </div>

                    <div class="form-group">
                        <label for="motherName">والدته:</label>
                        <input type="text" id="motherName" name="motherName" placeholder="اسم الوالدة">
                    </div>
                </div>
            </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="extractDataFromImage()" id="extractBtn" disabled>📤 نقل المعلومات من الصورة</button>
                    <button type="button" class="btn btn-success" onclick="saveDeathRecord()">💾 حفظ البيانات</button>
                    <button type="button" class="btn btn-warning" onclick="printCertificate()" disabled id="printBtn">🖨️ طباعة الشهادة</button>
                    <button type="button" class="btn btn-info" onclick="openDeathCertificate()" disabled id="deathCertBtn">📋 نسخة موجزة من رسم الوفاة</button>
                    <button type="button" class="btn btn-primary" onclick="clearForm()">🗑️ مسح النموذج</button>
                </div>
            </div>

            <!-- Right Panel - Certificate Image Upload -->
            <div class="right-panel">
                <div class="preview-section">
                    <h2 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #c41e3a; padding-bottom: 8px; text-align: center; font-size: 1.2em;">📄 تحميل شهادة الوفاة</h2>

                    <!-- Upload Section -->
                    <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
                        <div style="text-align: center; margin-bottom: 15px;">
                            <h5 style="color: #495057; margin-bottom: 10px;">📤 رفع صورة شهادة الوفاة</h5>
                            <p style="color: #6c757d; font-size: 14px; margin-bottom: 15px;">اختر صورة شهادة الوفاة لعرضها ونقل المعلومات منها</p>

                            <input type="file" id="certificateImageInput" accept="image/*,.pdf" style="display: none;" onchange="handleCertificateUpload(event)">
                            <button type="button" onclick="document.getElementById('certificateImageInput').click()"
                                    style="background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px; margin: 5px;">
                                📁 اختيار ملف
                            </button>
                            <button type="button" onclick="clearCertificateImage()"
                                    style="background: #dc3545; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px; margin: 5px;">
                                🗑️ مسح الصورة
                            </button>
                        </div>

                        <div style="text-align: center; font-size: 12px; color: #6c757d;">
                            <strong>الصيغ المدعومة:</strong> JPG, PNG, PDF | <strong>الحد الأقصى:</strong> 10MB
                        </div>
                    </div>

                    <!-- Certificate Display Area -->
                    <div class="certificate-preview" id="certificatePreview">
                        <!-- Default placeholder -->
                        <div id="certificateImageDisplay" style="text-align: center; padding: 40px; color: #6c757d; border: 2px dashed #dee2e6; border-radius: 8px; background: #f8f9fa;">
                            <div style="font-size: 48px; margin-bottom: 15px;">📄</div>
                            <h4 style="color: #495057; margin-bottom: 10px;">لم يتم تحميل شهادة وفاة بعد</h4>
                            <p style="margin-bottom: 0;">اضغط على "اختيار ملف" لتحميل صورة شهادة الوفاة</p>
                        </div>

                        <!-- Hidden certificate content for reference -->
                        <div class="certificate-content" style="display: none;">
                            <!-- Header -->
                            <div class="certificate-header">
                                <div class="header-left">
                                    المملكة المغربية<br>
                                    وزارة الداخلية<br>
                                    (إقليم أسفي)<br>
                                    جماعة أيير<br>
                                    مكتب الحالة المدنية :<br>
                                    ............./............. : عقد رقم
                                </div>
                                <div class="header-right">
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                </div>
                            </div>

                            <!-- Title -->
                            <div class="certificate-title">
                                نسخة موجزة من رسم الوفاة
                            </div>

                            <!-- Body -->
                            <div class="certificate-body">
                                <div class="field-line">
                                    <span class="field-label">توفي(ت) بـ :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">في :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الاسم الشخصي :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الاسم العائلي :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">تاريخ الازدياد :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">مكان الازدياد :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">مهنته (ها) :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الساكن (ة) بـ :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">والده :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">والدته :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">نشهد بصفتنا ضابطا للحالة المدنية نحن :</span>
                                    <span class="field-value">.............................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">بمطابقة هذه النسخة لما هو مضمن في سجلات الحالة المدنية بالمكتب المذكور</span>
                                    <span class="field-value"></span>
                                </div>
                            </div>

                            <!-- Footer -->
                            <div class="certificate-footer">
                                <div class="footer-right">
                                    أيير : في ................................................................<br>
                                    ضابط الحالة المدنية
                                </div>
                                <div class="footer-left">
                                    طابع مكتب الحالة المدنية<br>
                                    ضابط الحالة المدنية
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Actions -->
                    <div style="text-align: center; margin-top: 12px;">
                        <button type="button" class="btn btn-success" disabled style="font-size: 0.85em; padding: 6px 14px;">🖨️ طباعة الشهادة</button>
                        <button type="button" class="btn btn-primary" disabled style="font-size: 0.85em; padding: 6px 14px;">📄 تصدير PDF</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include IndexedDB Manager -->
    <script src="indexeddb-manager.js"></script>

    <script>
        /* ===== JAVASCRIPT FUNCTIONS ===== */

        // متغيرات قاعدة البيانات - استخدام المدير الموحد
        let db;
        const STORE_NAME = 'citizens';

        // متغيرات وضع التعديل
        let editingId = null;
        let isEditMode = false;

        // تهيئة قاعدة البيانات باستخدام المدير الموحد
        async function initDB() {
            try {
                await citizensDB.init();
                db = citizensDB.db; // تعيين مرجع قاعدة البيانات
                console.log('✅ تم تهيئة قاعدة البيانات بنجاح - الإصدار:', citizensDB.version);
                return db;
            } catch (error) {
                console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
                throw error;
            }
        }

        // تعيين تاريخ اليوم كافتراضي
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تهيئة صفحة شهادة الوفاة...');

            const today = new Date().toISOString().split('T')[0];
            document.getElementById('deathDate').value = today;

            // تهيئة متغيرات الصورة
            currentCertificateImage = null;
            currentZoom = 1;

            // تهيئة عرض الصورة
            displayCertificateImage();
            updateButtonStates();

            // تهيئة قاعدة البيانات
            initDB().then(() => {
                // فحص وضع التعديل
                checkEditMode();
                console.log('✅ تم تهيئة الصفحة بنجاح');
            }).catch(error => {
                console.error('خطأ في تهيئة قاعدة البيانات:', error);
                showAlert('خطأ في تهيئة قاعدة البيانات', 'error');
            });

            // إضافة مستمعي الأحداث لتحديث حالة الأزرار
            const personalNameField = document.getElementById('personalName');
            const familyNameField = document.getElementById('familyName');

            if (personalNameField && familyNameField) {
                personalNameField.addEventListener('input', updateButtonStates);
                familyNameField.addEventListener('input', updateButtonStates);
            }
        });

        // فحص وضع التعديل وتحميل البيانات
        function checkEditMode() {
            const urlParams = new URLSearchParams(window.location.search);
            const editParam = urlParams.get('edit');
            const citizenId = urlParams.get('id');

            if (editParam === 'true' && citizenId) {
                console.log('🔄 وضع التعديل مفعل للمواطن:', citizenId);
                editingId = citizenId;
                isEditMode = true;
                loadCitizenData(urlParams);
            }
        }

        // تحميل بيانات المواطن للتعديل
        function loadCitizenData(urlParams) {
            try {
                console.log('📋 تحميل بيانات المواطن للتعديل...');

                // تحميل البيانات الشخصية
                document.getElementById('personalName').value = urlParams.get('personalName') || '';
                document.getElementById('familyName').value = urlParams.get('familyName') || '';
                document.getElementById('birthDate').value = urlParams.get('birthDate') || '';
                document.getElementById('birthPlace').value = urlParams.get('birthPlace') || '';
                document.getElementById('gender').value = urlParams.get('gender') || '';
                document.getElementById('profession').value = urlParams.get('profession') || '';
                document.getElementById('residence').value = urlParams.get('residence') || '';
                document.getElementById('maritalStatus').value = urlParams.get('maritalStatus') || '';
                document.getElementById('nationality').value = urlParams.get('nationality') || 'مغربية';
                document.getElementById('fatherName').value = urlParams.get('fatherName') || '';
                document.getElementById('motherName').value = urlParams.get('motherName') || '';
                document.getElementById('age').value = urlParams.get('age') || '';

                // تحميل بيانات الوفاة
                document.getElementById('deathPlace').value = urlParams.get('deathPlace') || '';
                document.getElementById('deathDate').value = urlParams.get('deathDate') || '';
                document.getElementById('deathTime').value = urlParams.get('deathTime') || '';
                document.getElementById('deathCause').value = urlParams.get('deathCause') || '';

                // ملء الحقول المخفية لوضع التعديل
                document.getElementById('editMode').value = 'true';
                document.getElementById('citizenId').value = urlParams.get('id') || '';
                document.getElementById('originalActNumber').value = urlParams.get('actNumber') || '';
                document.getElementById('isDeceased').value = 'true';

                console.log('🔧 تم تعيين الحقول المخفية:', {
                    editMode: 'true',
                    citizenId: urlParams.get('id'),
                    originalActNumber: urlParams.get('actNumber'),
                    isDeceased: 'true'
                });

                // تحديث واجهة المستخدم لوضع التعديل
                updateUIForEditMode();

                // تحميل صورة شهادة الوفاة من قاعدة البيانات
                loadDeathCertificateImage(urlParams.get('id'));

                console.log('✅ تم تحميل بيانات المواطن بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن:', error);
                showAlert('خطأ في تحميل بيانات المواطن', 'error');
            }
        }

        // تحديث واجهة المستخدم لوضع التعديل
        function updateUIForEditMode() {
            // تحديث عنوان الصفحة
            const headerTitle = document.querySelector('.header-main h1');
            if (headerTitle) {
                headerTitle.innerHTML = '✏️ تعديل شهادة الوفاة';
                headerTitle.style.color = '#f39c12';
            }

            // تحديث عنوان القسم
            const sectionTitle = document.querySelector('.form-section h2');
            if (sectionTitle) {
                sectionTitle.innerHTML = '✏️ تعديل معلومات الوفاة';
                sectionTitle.style.color = '#f39c12';
            }

            // تحديث زر الحفظ
            const saveButton = document.querySelector('button[onclick="saveDeathRecord()"]');
            if (saveButton) {
                saveButton.innerHTML = '💾 تحديث البيانات';
                saveButton.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
            }

            // إظهار رسالة التعديل
            showAlert('📝 وضع التعديل مفعل - يمكنك الآن تعديل بيانات الوفاة', 'success');
        }

        // وظيفة تحديث المعاينة
        function updatePreview() {
            // الحصول على القيم من النموذج
            const deathPlace = document.getElementById('deathPlace').value || '';
            const deathDate = document.getElementById('deathDate').value || '';
            const personalName = document.getElementById('personalName').value || '';
            const familyName = document.getElementById('familyName').value || '';
            const birthDate = document.getElementById('birthDate').value || '';
            const birthPlace = document.getElementById('birthPlace').value || '';
            const profession = document.getElementById('profession').value || '';
            const residence = document.getElementById('residence').value || '';
            const fatherName = document.getElementById('fatherName').value || '';
            const motherName = document.getElementById('motherName').value || '';

            // تحديث الحقول في المعاينة
            const fieldValues = document.querySelectorAll('.certificate-content .field-value');

            if (fieldValues[0]) fieldValues[0].textContent = deathPlace;
            if (fieldValues[1]) fieldValues[1].textContent = deathDate;
            if (fieldValues[2]) fieldValues[2].textContent = personalName;
            if (fieldValues[3]) fieldValues[3].textContent = familyName;
            if (fieldValues[4]) fieldValues[4].textContent = birthDate;
            if (fieldValues[5]) fieldValues[5].textContent = birthPlace;
            if (fieldValues[6]) fieldValues[6].textContent = profession;
            if (fieldValues[7]) fieldValues[7].textContent = residence;
            if (fieldValues[8]) fieldValues[8].textContent = fatherName;
            if (fieldValues[9]) fieldValues[9].textContent = motherName;

            // إظهار رسالة نجاح
            showAlert('تم تحديث المعاينة بنجاح!', 'success');
        }

        // وظيفة حفظ بيانات الوفاة
        async function saveDeathRecord() {
            try {
                // التحقق من صحة البيانات المطلوبة
                const deathPlace = document.getElementById('deathPlace').value.trim();
                const deathDate = document.getElementById('deathDate').value;
                const personalName = document.getElementById('personalName').value.trim();
                const familyName = document.getElementById('familyName').value.trim();

                if (!deathPlace || !deathDate || !personalName || !familyName) {
                    showAlert('يرجى ملء جميع الحقول المطلوبة (مكان الوفاة، تاريخ الوفاة، الاسم الشخصي، الاسم العائلي)', 'error');
                    return;
                }

                // قراءة الحقول المخفية
                const editMode = document.getElementById('editMode').value === 'true';
                const citizenId = document.getElementById('citizenId').value;
                const originalActNumber = document.getElementById('originalActNumber').value;
                const isDeceasedValue = document.getElementById('isDeceased').value === 'true';

                console.log('🔧 حالة الحفظ:', {
                    editMode: editMode,
                    citizenId: citizenId,
                    originalActNumber: originalActNumber,
                    isDeceased: isDeceasedValue
                });

                // جمع جميع البيانات بالتنسيق المتوافق مع النظام الأصلي
                const deathRecord = {
                    // البيانات الأساسية (متوافقة مع النظام الأصلي)
                    id: editMode ? citizenId : Date.now().toString(), // استخدام المعرف الموجود في وضع التعديل
                    firstNameAr: personalName,
                    familyNameAr: familyName,
                    personalName: personalName, // للتوافق مع النظام القديم
                    familyName: familyName, // للتوافق مع النظام القديم
                    birthDate: document.getElementById('birthDate').value || null,
                    birthPlaceAr: document.getElementById('birthPlace').value.trim() || null,
                    birthPlace: document.getElementById('birthPlace').value.trim() || null, // للتوافق
                    gender: document.getElementById('gender').value || null,
                    profession: document.getElementById('profession').value.trim() || null,
                    residence: document.getElementById('residence').value.trim() || null,
                    maritalStatus: document.getElementById('maritalStatus').value || null,
                    nationality: document.getElementById('nationality').value.trim() || 'مغربية',
                    fatherNameAr: document.getElementById('fatherName').value.trim() || null,
                    motherNameAr: document.getElementById('motherName').value.trim() || null,
                    fatherName: document.getElementById('fatherName').value.trim() || null, // للتوافق
                    motherName: document.getElementById('motherName').value.trim() || null, // للتوافق
                    age: document.getElementById('age').value || null,

                    // الحفاظ على معرف العقد الأصلي في وضع التعديل
                    actNumber: editMode ? originalActNumber : generateActNumber(),

                    // بيانات الوفاة - الحفاظ على حالة الوفاة
                    isDeceased: true, // دائماً true لشهادات الوفاة
                    deathInfo: {
                        deathPlace: deathPlace,
                        deathDate: deathDate,
                        deathTime: document.getElementById('deathTime').value || null,
                        deathCause: document.getElementById('deathCause').value.trim() || null,
                        registrationDate: new Date().toISOString().split('T')[0],
                        registrationTime: new Date().toLocaleTimeString('ar-MA'),
                        certificationOfficer: 'ضابط الحالة المدنية - أيير'
                    },

                    // إضافة صورة الشهادة إذا كانت موجودة (الكائن كاملاً)
                    deathCertificateImage: currentCertificateImage || null,

                    // معلومات إضافية
                    recordType: 'death',
                    createdAt: editMode ? undefined : new Date().toISOString(), // لا نغير تاريخ الإنشاء في التعديل
                    lastModified: new Date().toISOString()
                };

                console.log('💾 سجل الوفاة المُعد للحفظ:', {
                    id: deathRecord.id,
                    actNumber: deathRecord.actNumber,
                    isDeceased: deathRecord.isDeceased,
                    editMode: editMode
                });

                // حفظ أو تحديث في قاعدة البيانات
                if (editMode) {
                    await updateInDatabase(deathRecord);
                    showAlert('✅ تم تحديث بيانات الوفاة بنجاح!', 'success');
                } else {
                    await saveToDatabase(deathRecord);
                    showAlert('✅ تم حفظ بيانات الوفاة بنجاح!', 'success');
                }

            } catch (error) {
                console.error('❌ خطأ في حفظ البيانات:', error);
                showAlert('حدث خطأ أثناء حفظ البيانات: ' + error.message, 'error');
            }
        }

        // تحميل صورة شهادة الوفاة من قاعدة البيانات باستخدام المدير الموحد
        async function loadDeathCertificateImage(citizenId) {
            try {
                console.log('🖼️ تحميل صورة شهادة الوفاة للمواطن:', citizenId);

                const citizen = await citizensDB.getCitizen(parseInt(citizenId));

                if (citizen && citizen.deathCertificateImage) {
                    console.log('✅ تم العثور على صورة شهادة الوفاة');

                    // استخدام البيانات مباشرة (بنية موحدة)
                    currentCertificateImage = {
                        data: citizen.deathCertificateImage.data || citizen.deathCertificateImage,
                        fileName: citizen.deathCertificateImage.fileName || 'شهادة_وفاة.jpg',
                        type: citizen.deathCertificateImage.type || 'image/jpeg',
                        size: citizen.deathCertificateImage.size || 0,
                        hasImage: true,
                        uploadDate: citizen.deathCertificateImage.uploadDate || new Date().toISOString()
                    };

                    // عرض الصورة
                    displayCertificateImage();
                    updateButtonStates();

                    console.log('✅ تم تحميل وعرض صورة شهادة الوفاة بنجاح');
                } else {
                    console.log('📄 لا توجد صورة شهادة وفاة محفوظة');
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل صورة شهادة الوفاة:', error);
            }
        }

        // وظيفة توليد رقم العقد
        function generateActNumber() {
            const currentYear = new Date().getFullYear();
            const randomNumber = Math.floor(Math.random() * 1000) + 1;
            return `${randomNumber}/${currentYear}`;
        }

        // وظيفة حفظ البيانات في قاعدة البيانات باستخدام المدير الموحد
        async function saveToDatabase(record) {
            try {
                console.log('💾 حفظ سجل جديد باستخدام citizensDB...');
                const result = await citizensDB.addCitizen(record);
                console.log('✅ تم حفظ السجل بنجاح، ID:', result);
                return result;
            } catch (error) {
                console.error('❌ فشل في حفظ البيانات:', error);
                throw new Error('فشل في حفظ البيانات: ' + error.message);
            }
        }

        // وظيفة تحديث البيانات في قاعدة البيانات باستخدام المدير الموحد
        async function updateInDatabase(record) {
            try {
                console.log('🔄 تحديث سجل باستخدام citizensDB...', {
                    id: record.id,
                    actNumber: record.actNumber,
                    isDeceased: record.isDeceased
                });

                // تحويل المعرف للنوع الصحيح
                if (typeof record.id === 'string' && !isNaN(record.id)) {
                    record.id = parseInt(record.id);
                }

                const result = await citizensDB.updateCitizen(record.id, record);
                console.log('✅ تم تحديث السجل بنجاح، ID:', record.id);
                return result;
            } catch (error) {
                console.error('❌ فشل في تحديث البيانات:', error);
                throw new Error('فشل في تحديث البيانات: ' + error.message);
            }
        }



        // وظيفة مسح النموذج
        function clearForm() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المدخلة؟')) {
                document.getElementById('deathForm').reset();

                // إعادة تعيين التاريخ الافتراضي
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('deathDate').value = today;
                document.getElementById('nationality').value = 'مغربية';

                // مسح المعاينة
                const fieldValues = document.querySelectorAll('.certificate-content .field-value');
                fieldValues.forEach(field => {
                    field.textContent = '';
                });

                showAlert('تم مسح النموذج بنجاح', 'success');
            }
        }

        // وظيفة فتح النسخة الموجزة من رسم الوفاة
        function openDeathCertificate() {
            // التحقق من وجود بيانات أساسية
            const personalName = document.getElementById('personalName').value.trim();
            const familyName = document.getElementById('familyName').value.trim();
            const deathDate = document.getElementById('deathDate').value;
            const deathPlace = document.getElementById('deathPlace').value.trim();

            if (!personalName || !familyName) {
                showAlert('❌ يرجى إدخال الاسم الشخصي والعائلي على الأقل', 'error');
                return;
            }

            try {
                // جمع البيانات من النموذج
                const formData = {
                    personalName: personalName,
                    familyName: familyName,
                    birthDate: document.getElementById('birthDate').value,
                    birthPlace: document.getElementById('birthPlace').value.trim(),
                    gender: document.getElementById('gender').value,
                    fatherName: document.getElementById('fatherName').value.trim(),
                    motherName: document.getElementById('motherName').value.trim(),
                    profession: document.getElementById('profession').value.trim(),
                    maritalStatus: document.getElementById('maritalStatus').value,
                    deathDate: deathDate,
                    deathPlace: deathPlace,
                    deathTime: document.getElementById('deathTime').value,
                    deathCause: document.getElementById('deathCause').value.trim(),
                    age: document.getElementById('age').value
                };

                // إنشاء URL مع البيانات
                const params = new URLSearchParams();
                Object.keys(formData).forEach(key => {
                    if (formData[key]) {
                        params.append(key, formData[key]);
                    }
                });

                const url = `death-certificate.html?${params.toString()}`;
                console.log('📋 فتح النسخة الموجزة من رسم الوفاة:', url);

                // فتح النموذج في نافذة جديدة
                window.open(url, '_blank');

                showAlert('✅ تم فتح النسخة الموجزة من رسم الوفاة في نافذة جديدة', 'success');

            } catch (error) {
                console.error('❌ خطأ في فتح النسخة الموجزة من رسم الوفاة:', error);
                showAlert('حدث خطأ أثناء فتح النسخة الموجزة من رسم الوفاة', 'error');
            }
        }

        // وظيفة إظهار الرسائل
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // متغيرات الصورة
        let currentCertificateImage = null;
        let currentZoom = 1;

        // وظيفة تحميل صورة الشهادة
        function handleCertificateUpload(event) {
            console.log('📤 بدء تحميل الصورة...');

            const file = event.target.files[0];
            if (!file) {
                console.log('❌ لم يتم اختيار ملف');
                return;
            }

            console.log('📁 تفاصيل الملف:', {
                name: file.name,
                type: file.type,
                size: (file.size / 1024 / 1024).toFixed(2) + ' MB'
            });

            // التحقق من نوع الملف
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
            if (!allowedTypes.includes(file.type)) {
                console.log('❌ نوع الملف غير مدعوم:', file.type);
                showAlert('❌ نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG) أو ملف PDF', 'error');
                return;
            }

            // التحقق من حجم الملف (10MB)
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                console.log('❌ حجم الملف كبير:', (file.size / 1024 / 1024).toFixed(2) + ' MB');
                showAlert('❌ حجم الملف كبير جداً. الحد الأقصى 10MB', 'error');
                return;
            }

            console.log('✅ الملف صالح، بدء القراءة...');

            // قراءة الملف
            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('✅ تم قراءة الملف بنجاح');

                currentCertificateImage = {
                    data: e.target.result,
                    fileName: file.name,
                    size: file.size,
                    type: file.type,
                    hasImage: true,
                    uploadDate: new Date().toISOString()
                };

                console.log('💾 تم حفظ بيانات الصورة:', {
                    fileName: currentCertificateImage.fileName,
                    type: currentCertificateImage.type,
                    hasImage: currentCertificateImage.hasImage,
                    dataLength: currentCertificateImage.data.length
                });

                displayCertificateImage();
                updateButtonStates();
                showAlert('✅ تم تحميل صورة الشهادة بنجاح', 'success');
            };

            reader.onerror = function(error) {
                console.error('❌ خطأ في قراءة الملف:', error);
                showAlert('❌ خطأ في قراءة الملف', 'error');
            };

            reader.readAsDataURL(file);
        }

        // وظيفة عرض صورة الشهادة
        function displayCertificateImage() {
            console.log('🖼️ بدء عرض الصورة...');

            const displayArea = document.getElementById('certificateImageDisplay');

            if (!displayArea) {
                console.error('❌ لم يتم العثور على منطقة العرض');
                return;
            }

            if (!currentCertificateImage) {
                console.log('📄 عرض الحالة الافتراضية (لا توجد صورة)');
                displayArea.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #6c757d; border: 2px dashed #dee2e6; border-radius: 8px; background: #f8f9fa;">
                        <div style="font-size: 48px; margin-bottom: 15px;">📄</div>
                        <h4 style="color: #495057; margin-bottom: 10px;">لم يتم تحميل شهادة وفاة بعد</h4>
                        <p style="margin-bottom: 0;">اضغط على "اختيار ملف" لتحميل صورة شهادة الوفاة</p>
                    </div>
                `;
                return;
            }

            console.log('🔍 تحليل نوع الملف:', currentCertificateImage.type);

            const isImage = currentCertificateImage.type.startsWith('image/');
            const isPDF = currentCertificateImage.type === 'application/pdf';

            if (isImage) {
                console.log('🖼️ عرض صورة...');
                displayArea.innerHTML = `
                    <div class="image-container">
                        <div class="zoom-controls">
                            <button class="zoom-btn" onclick="zoomImage(1.2)" title="تكبير">🔍+</button>
                            <button class="zoom-btn" onclick="zoomImage(0.8)" title="تصغير">🔍-</button>
                            <button class="zoom-btn" onclick="resetZoom()" title="إعادة تعيين">↻</button>
                            <button class="zoom-btn" onclick="openImageInNewTab()" title="فتح في نافذة جديدة">🔗</button>
                        </div>
                        <img src="${currentCertificateImage.data}"
                             alt="شهادة الوفاة"
                             class="certificate-image"
                             id="certificateImg"
                             onclick="toggleZoom()"
                             style="transform: scale(${currentZoom});"
                             onload="console.log('✅ تم تحميل الصورة في DOM')"
                             onerror="console.error('❌ خطأ في تحميل الصورة في DOM')">
                        <div class="image-info">
                            <strong>📁 ${currentCertificateImage.fileName}</strong> |
                            📏 ${(currentCertificateImage.size / 1024 / 1024).toFixed(2)} MB |
                            🔍 ${Math.round(currentZoom * 100)}%
                        </div>
                    </div>
                `;
                console.log('✅ تم إنشاء HTML للصورة');
            } else if (isPDF) {
                console.log('📄 عرض PDF...');
                displayArea.innerHTML = `
                    <div class="image-container">
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 48px; margin-bottom: 15px; color: #dc3545;">📄</div>
                            <h4 style="color: #495057; margin-bottom: 10px;">ملف PDF</h4>
                            <p style="margin-bottom: 15px; color: #6c757d;">تم تحميل ملف PDF بنجاح</p>
                            <button class="btn btn-primary" onclick="openPDFInNewTab()">📖 فتح PDF</button>
                        </div>
                        <div class="image-info">
                            <strong>📁 ${currentCertificateImage.fileName}</strong> |
                            📏 ${(currentCertificateImage.size / 1024 / 1024).toFixed(2)} MB
                        </div>
                    </div>
                `;
                console.log('✅ تم إنشاء HTML للـ PDF');
            } else {
                console.error('❌ نوع ملف غير معروف:', currentCertificateImage.type);
            }
        }

        // وظيفة مسح صورة الشهادة
        function clearCertificateImage() {
            if (currentCertificateImage) {
                if (confirm('هل أنت متأكد من حذف صورة الشهادة؟')) {
                    currentCertificateImage = null;
                    currentZoom = 1;
                    document.getElementById('certificateImageInput').value = '';
                    displayCertificateImage();
                    updateButtonStates();
                    showAlert('تم حذف صورة الشهادة', 'success');
                }
            } else {
                showAlert('لا توجد صورة لحذفها', 'error');
            }
        }

        // وظائف التكبير والتصغير
        function zoomImage(factor) {
            currentZoom *= factor;
            currentZoom = Math.max(0.5, Math.min(currentZoom, 3)); // حد أدنى 50% وحد أقصى 300%

            const img = document.getElementById('certificateImg');
            if (img) {
                img.style.transform = `scale(${currentZoom})`;
                updateImageInfo();
            }
        }

        function resetZoom() {
            currentZoom = 1;
            const img = document.getElementById('certificateImg');
            if (img) {
                img.style.transform = `scale(${currentZoom})`;
                updateImageInfo();
            }
        }

        function toggleZoom() {
            if (currentZoom === 1) {
                zoomImage(1.5);
            } else {
                resetZoom();
            }
        }

        function updateImageInfo() {
            const infoDiv = document.querySelector('.image-info');
            if (infoDiv && currentCertificateImage) {
                infoDiv.innerHTML = `
                    <strong>📁 ${currentCertificateImage.fileName}</strong> |
                    📏 ${(currentCertificateImage.size / 1024 / 1024).toFixed(2)} MB |
                    🔍 ${Math.round(currentZoom * 100)}%
                `;
            }
        }

        function openImageInNewTab() {
            if (currentCertificateImage) {
                const newWindow = window.open();
                newWindow.document.write(`
                    <html>
                        <head><title>شهادة الوفاة - ${currentCertificateImage.fileName}</title></head>
                        <body style="margin: 0; padding: 20px; background: #f5f5f5; text-align: center;">
                            <img src="${currentCertificateImage.data}" style="max-width: 100%; height: auto; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                        </body>
                    </html>
                `);
            }
        }

        function openPDFInNewTab() {
            if (currentCertificateImage && currentCertificateImage.type === 'application/pdf') {
                const newWindow = window.open();
                newWindow.location.href = currentCertificateImage.data;
            }
        }

        // تحديث حالة الأزرار
        function updateButtonStates() {
            const extractBtn = document.getElementById('extractBtn');
            const printBtn = document.getElementById('printBtn');
            const deathCertBtn = document.getElementById('deathCertBtn');

            // أزرار الصورة
            if (currentCertificateImage) {
                extractBtn.disabled = false;
                printBtn.disabled = false;
                extractBtn.style.opacity = '1';
                printBtn.style.opacity = '1';
            } else {
                extractBtn.disabled = true;
                printBtn.disabled = true;
                extractBtn.style.opacity = '0.6';
                printBtn.style.opacity = '0.6';
            }

            // زر النسخة الموجزة من رسم الوفاة - يتم تفعيله عند وجود بيانات أساسية
            const personalName = document.getElementById('personalName').value.trim();
            const familyName = document.getElementById('familyName').value.trim();

            if (personalName && familyName) {
                deathCertBtn.disabled = false;
                deathCertBtn.style.opacity = '1';
            } else {
                deathCertBtn.disabled = true;
                deathCertBtn.style.opacity = '0.6';
            }
        }

        // وظيفة نقل المعلومات من الصورة (محاكاة)
        function extractDataFromImage() {
            if (!currentCertificateImage) {
                showAlert('❌ يرجى تحميل صورة الشهادة أولاً', 'error');
                return;
            }

            // محاكاة عملية استخراج البيانات
            showAlert('🔄 جاري استخراج المعلومات من الصورة...', 'success');

            // محاكاة تأخير المعالجة
            setTimeout(() => {
                // بيانات تجريبية للمحاكاة
                const extractedData = {
                    personalName: 'محمد',
                    familyName: 'الأحمدي',
                    birthDate: '1950-05-15',
                    birthPlace: 'أيير',
                    deathPlace: 'أيير',
                    deathDate: '2024-01-15',
                    deathTime: '14:30',
                    deathCause: 'أسباب طبيعية',
                    gender: 'ذكر',
                    profession: 'فلاح',
                    residence: 'أيير',
                    fatherName: 'أحمد الأحمدي',
                    motherName: 'فاطمة بنت علي',
                    age: '74'
                };

                // ملء النموذج بالبيانات المستخرجة
                fillFormWithExtractedData(extractedData);

                showAlert('✅ تم استخراج المعلومات ونقلها إلى النموذج بنجاح!', 'success');
            }, 2000);
        }

        // ملء النموذج بالبيانات المستخرجة
        function fillFormWithExtractedData(data) {
            // ملء البيانات الشخصية
            if (data.personalName) document.getElementById('personalName').value = data.personalName;
            if (data.familyName) document.getElementById('familyName').value = data.familyName;
            if (data.birthDate) document.getElementById('birthDate').value = data.birthDate;
            if (data.birthPlace) document.getElementById('birthPlace').value = data.birthPlace;
            if (data.gender) document.getElementById('gender').value = data.gender;
            if (data.profession) document.getElementById('profession').value = data.profession;
            if (data.residence) document.getElementById('residence').value = data.residence;
            if (data.fatherName) document.getElementById('fatherName').value = data.fatherName;
            if (data.motherName) document.getElementById('motherName').value = data.motherName;
            if (data.age) document.getElementById('age').value = data.age;

            // ملء بيانات الوفاة
            if (data.deathPlace) document.getElementById('deathPlace').value = data.deathPlace;
            if (data.deathDate) document.getElementById('deathDate').value = data.deathDate;
            if (data.deathTime) document.getElementById('deathTime').value = data.deathTime;
            if (data.deathCause) document.getElementById('deathCause').value = data.deathCause;

            // تأثير بصري لإظهار الحقول المملوءة
            const filledFields = document.querySelectorAll('input[type="text"], input[type="date"], input[type="time"], select');
            filledFields.forEach(field => {
                if (field.value) {
                    field.style.background = '#e8f5e8';
                    field.style.borderColor = '#28a745';

                    // إزالة التأثير بعد 3 ثوان
                    setTimeout(() => {
                        field.style.background = '';
                        field.style.borderColor = '';
                    }, 3000);
                }
            });
        }

        // وظيفة طباعة الشهادة
        function printCertificate() {
            if (!currentCertificateImage) {
                showAlert('❌ يرجى تحميل صورة الشهادة أولاً', 'error');
                return;
            }

            // فتح نافذة طباعة للصورة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>طباعة شهادة الوفاة</title>
                    <style>
                        @media print {
                            @page {
                                size: A4;
                                margin: 15mm;
                            }
                            body {
                                margin: 0;
                                padding: 0;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                min-height: 100vh;
                            }
                            img {
                                max-width: 100%;
                                max-height: 100%;
                                object-fit: contain;
                            }
                        }
                        body {
                            margin: 0;
                            padding: 20px;
                            background: white;
                            text-align: center;
                        }
                        img {
                            max-width: 100%;
                            height: auto;
                            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                        }
                    </style>
                </head>
                <body>
                    <img src="${currentCertificateImage.data}" alt="شهادة الوفاة">
                    <script>
                        window.onload = function() {
                            window.print();
                            window.onafterprint = function() {
                                window.close();
                            }
                        }
                    <\/script>
                </body>
                </html>
            `);
            printWindow.document.close();
        }
        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-MA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // حساب العمر بدقة من تاريخ الازدياد وتاريخ الوفاة
        function calculateAge(birthDate, deathDate) {
            if (!birthDate || !deathDate) {
                return null;
            }

            try {
                const birth = new Date(birthDate);
                const death = new Date(deathDate);

                // التحقق من صحة التواريخ
                if (isNaN(birth.getTime()) || isNaN(death.getTime())) {
                    console.warn('⚠️ تواريخ غير صحيحة');
                    return null;
                }

                // التحقق من أن تاريخ الوفاة بعد تاريخ الازدياد
                if (death < birth) {
                    console.warn('⚠️ تاريخ الوفاة قبل تاريخ الازدياد');
                    return null;
                }

                // حساب العمر بالسنوات فقط
                let years = death.getFullYear() - birth.getFullYear();
                const birthThisYear = new Date(death.getFullYear(), birth.getMonth(), birth.getDate());

                // إذا لم يحن موعد عيد الميلاد بعد في سنة الوفاة، نقص سنة
                if (death < birthThisYear) {
                    years--;
                }

                // تنسيق العمر (السنوات فقط)
                let ageString = '';
                if (years > 0) {
                    ageString = `${years} سنة`;
                } else {
                    ageString = 'أقل من سنة';
                }

                console.log(`📅 حساب العمر: من ${birthDate} إلى ${deathDate} = ${ageString}`);
                return ageString;

            } catch (error) {
                console.error('❌ خطأ في حساب العمر:', error);
                return null;
            }
        }

        // تحديث حقل العمر تلقائياً
        function updateAgeField() {
            const birthDate = document.getElementById('birthDate').value;
            const deathDate = document.getElementById('deathDate').value;
            const ageField = document.getElementById('age');

            if (birthDate && deathDate) {
                const calculatedAge = calculateAge(birthDate, deathDate);
                if (calculatedAge) {
                    ageField.value = calculatedAge;
                    ageField.style.background = '#e8f5e8';
                    ageField.style.borderColor = '#28a745';
                    console.log('✅ تم حساب العمر تلقائياً:', calculatedAge);
                } else {
                    ageField.value = 'غير محدد';
                    ageField.style.background = '#fff3cd';
                    ageField.style.borderColor = '#ffc107';
                }
            } else {
                ageField.value = '';
                ageField.style.background = '#f8f9fa';
                ageField.style.borderColor = '#e9ecef';
            }
        }

        // تفعيل التعديل اليدوي للعمر
        function enableAgeEdit() {
            const ageField = document.getElementById('age');
            const currentValue = ageField.value;

            // تغيير الحقل إلى قابل للتعديل
            ageField.removeAttribute('readonly');
            ageField.style.background = '#fff';
            ageField.style.borderColor = '#007bff';
            ageField.placeholder = 'أدخل العمر يدوياً';
            ageField.focus();
            ageField.select();

            // تغيير زر التعديل
            const editButton = ageField.nextElementSibling;
            editButton.innerHTML = '🔄';
            editButton.title = 'إعادة حساب تلقائي';
            editButton.onclick = function() {
                resetAgeField();
            };

            console.log('📝 تم تفعيل التعديل اليدوي للعمر');
        }

        // إعادة تعيين حقل العمر للحساب التلقائي
        function resetAgeField() {
            const ageField = document.getElementById('age');

            // إعادة الحقل إلى الوضع التلقائي
            ageField.setAttribute('readonly', 'readonly');
            ageField.style.background = '#f8f9fa';
            ageField.style.borderColor = '#e9ecef';
            ageField.placeholder = 'سيتم حسابه تلقائياً';

            // إعادة زر التعديل
            const editButton = ageField.nextElementSibling;
            editButton.innerHTML = '✏️';
            editButton.title = 'تعديل يدوي';
            editButton.onclick = function() {
                enableAgeEdit();
            };

            // إعادة حساب العمر
            updateAgeField();

            console.log('🔄 تم إعادة تعيين حقل العمر للحساب التلقائي');
        }

        // تحديث الوقت عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 60000);

            // إضافة مستمعي الأحداث لحساب العمر التلقائي
            const birthDateField = document.getElementById('birthDate');
            const deathDateField = document.getElementById('deathDate');

            if (birthDateField && deathDateField) {
                birthDateField.addEventListener('change', updateAgeField);
                deathDateField.addEventListener('change', updateAgeField);
                console.log('✅ تم إعداد مستمعي الأحداث لحساب العمر التلقائي');
            }
        });
    </script>
</body>
</html>
