<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قاعدة بيانات المواطنين - IndexedDB - مكتب الحالة المدنية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
            color: white;
            padding: 20px 30px;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #006233 0%, #c41e3a 50%, #006233 100%);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .header h1 {
            font-size: 2.2em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            text-align: center;
            flex: 1;
        }

        .nav-btn {
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.15);
            padding: 10px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
        }

        .header p {
            text-align: center;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .form-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #c41e3a;
            padding-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 10px rgba(196, 30, 58, 0.3);
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 3px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #c41e3a, #8b0000);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            padding: 15px 20px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 3px solid #c41e3a;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .stat-number {
            font-size: 1.6em;
            font-weight: bold;
            color: #c41e3a;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(196, 30, 58, 0.2);
        }

        .stat-label {
            color: #6c757d;
            font-weight: 600;
            font-size: 12px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <a href="main-dashboard.html" class="nav-btn">
                    🏠 الصفحة الرئيسية
                </a>
                <h1>🗃️ قاعدة بيانات المواطنين</h1>
                <a href="search-citizens.html" class="nav-btn">
                    🔍 البحث في السجلات
                </a>
            </div>
            <p>مكتب الحالة المدنية - أيير، إقليم أسفي • نظام IndexedDB المتطور</p>
        </div>

        <!-- Statistics -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalCitizens">0</div>
                <div class="stat-label">إجمالي المواطنين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="maleCount">0</div>
                <div class="stat-label">ذكور</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="femaleCount">0</div>
                <div class="stat-label">إناث</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayRegistrations">0</div>
                <div class="stat-label">تسجيلات اليوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="withCertificates">0</div>
                <div class="stat-label">مع شهادات كاملة</div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Form Section -->
            <div class="form-section">
                <h2>📝 تسجيل مواطن جديد</h2>

                <div id="alertContainer"></div>
                <div id="loadingIndicator" class="loading" style="display: none;">
                    ⏳ جاري التحميل...
                </div>

                <form id="citizenForm">
                    <div class="form-group">
                        <label for="firstNameAr">الاسم الشخصي (عربي):</label>
                        <input type="text" id="firstNameAr" name="firstNameAr" required placeholder="أحمد">
                    </div>

                    <div class="form-group">
                        <label for="firstNameFr">الاسم الشخصي (فرنسي):</label>
                        <input type="text" id="firstNameFr" name="firstNameFr" required placeholder="Ahmed">
                    </div>

                    <div class="form-group">
                        <label for="familyNameAr">الاسم العائلي (عربي):</label>
                        <input type="text" id="familyNameAr" name="familyNameAr" required placeholder="محمد علي">
                    </div>

                    <div class="form-group">
                        <label for="familyNameFr">الاسم العائلي (فرنسي):</label>
                        <input type="text" id="familyNameFr" name="familyNameFr" required placeholder="Mohamed Ali">
                    </div>

                    <div class="form-group">
                        <label for="birthPlaceAr">مكان الازدياد (عربي):</label>
                        <input type="text" id="birthPlaceAr" name="birthPlaceAr" required placeholder="الرباط">
                    </div>

                    <div class="form-group">
                        <label for="birthPlaceFr">مكان الازدياد (فرنسي):</label>
                        <input type="text" id="birthPlaceFr" name="birthPlaceFr" required placeholder="Rabat">
                    </div>

                    <div class="form-group">
                        <label for="birthDate">تاريخ الازدياد (ميلادي):</label>
                        <input type="date" id="birthDate" name="birthDate" required>
                    </div>

                    <div class="form-group">
                        <label for="hijriDate">التاريخ الهجري:</label>
                        <input type="text" id="hijriDate" name="hijriDate" placeholder="مثال: 5 رجب 1445">
                    </div>

                    <div class="form-group">
                        <label for="gender">الجنس:</label>
                        <select id="gender" name="gender" required>
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="fatherNameAr">اسم الوالد (عربي):</label>
                        <input type="text" id="fatherNameAr" name="fatherNameAr" required placeholder="محمد علي حسن">
                    </div>

                    <div class="form-group">
                        <label for="fatherNameFr">اسم الوالد (فرنسي):</label>
                        <input type="text" id="fatherNameFr" name="fatherNameFr" required placeholder="Mohamed Ali Hassan">
                    </div>

                    <div class="form-group">
                        <label for="motherNameAr">اسم الوالدة (عربي):</label>
                        <input type="text" id="motherNameAr" name="motherNameAr" required placeholder="فاطمة أحمد محمد">
                    </div>

                    <div class="form-group">
                        <label for="motherNameFr">اسم الوالدة (فرنسي):</label>
                        <input type="text" id="motherNameFr" name="motherNameFr" required placeholder="Fatima Ahmed Mohamed">
                    </div>

                    <div class="form-group">
                        <label for="actNumber">رقم القيد:</label>
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="actNumberMode" value="auto" checked onchange="toggleActNumberMode()"> تلقائي
                            </label>
                            <label style="margin: 0; font-weight: normal;">
                                <input type="radio" name="actNumberMode" value="manual" onchange="toggleActNumberMode()"> يدوي
                            </label>
                        </div>
                        <input type="text" id="actNumber" name="actNumber" required readonly style="background-color: #f8f9fa;">
                        <small id="actNumberHelp" style="color: #6c757d; font-size: 12px;">سيتم توليد الرقم تلقائياً بصيغة: رقم/سنة</small>
                    </div>

                    <div class="form-group">
                        <label for="registrationDate">تاريخ التسجيل:</label>
                        <input type="date" id="registrationDate" name="registrationDate" required>
                    </div>

                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 8px; flex-wrap: wrap; justify-content: center; margin-top: 15px;">
                        <button type="button" class="btn btn-success" onclick="saveAndNew()" style="font-size: 14px; padding: 10px 20px;" title="اختصار: Ctrl+S">💾➕ حفظ وإضافة جديد</button>
                        <button type="button" class="btn btn-warning" onclick="clearForm()" style="font-size: 14px; padding: 10px 20px;" title="اختصار: Ctrl+R">🗑️ مسح النموذج</button>
                        <a href="search-citizens.html" class="btn btn-secondary" style="font-size: 14px; padding: 10px 20px;">🔍 البحث في السجلات</a>
                    </div>

                    <!-- Keyboard Shortcuts Info -->
                    <div style="text-align: center; margin-top: 8px; font-size: 11px; color: #6c757d;">
                        💡 اختصارات لوحة المفاتيح: Ctrl+S (حفظ وجديد) • Ctrl+R (مسح)
                    </div>
                </form>
            </div>

            <!-- Certificate Section -->
            <div class="form-section">
                <h2>📄 إدارة الشهادة الكاملة</h2>

                <!-- Upload Button -->
                <div style="text-align: center; margin-bottom: 15px;">
                    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()" style="font-size: 14px; padding: 10px 20px;">
                        📷 تحميل الشهادة الكاملة
                    </button>
                </div>

                <!-- Display Area -->
                <div class="upload-area" id="uploadArea" style="background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; padding: 20px; text-align: center; min-height: 200px; transition: all 0.3s ease;">
                    <div id="uploadContent">
                        <div style="color: #6c757d; font-size: 32px; margin-bottom: 12px;">📄</div>
                        <h4 style="color: #6c757d; margin-bottom: 8px; font-size: 16px;">منطقة عرض الشهادة</h4>
                        <p style="color: #6c757d; margin-bottom: 8px; font-size: 13px;">استخدم الزر أعلاه لتحميل الشهادة</p>
                        <p style="color: #95a5a6; font-size: 12px;">يدعم: JPG, PNG, PDF</p>
                    </div>

                    <!-- Uploaded Image Display -->
                    <div id="imageDisplay" style="display: none;">
                        <div id="imageContainer" style="margin-bottom: 10px; position: relative; overflow: hidden; border-radius: 6px; border: 1px solid #ddd; cursor: zoom-in;">
                            <img id="uploadedImage" style="max-width: 100%; max-height: 350px; border-radius: 6px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); transition: transform 0.3s ease; transform-origin: center;">
                        </div>
                    </div>
                </div>

                <!-- Image Controls -->
                <div id="imageControls" style="display: none; margin-top: 10px; text-align: center;">
                    <div style="margin-bottom: 6px; color: #6c757d; font-size: 12px;">
                        💡 اضغط على الصورة للتكبير • مرر عجلة الفأرة للتكبير والتصغير
                    </div>
                    <div style="margin-bottom: 8px;">
                        <button class="btn btn-primary" onclick="zoomIn(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔍+</button>
                        <button class="btn btn-primary" onclick="zoomOut(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔍-</button>
                        <button class="btn btn-secondary" onclick="resetZoom(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🔄 إعادة تعيين</button>
                    </div>
                    <button class="btn btn-secondary" onclick="removeImage(event)" style="margin: 2px; font-size: 12px; padding: 6px 10px;">🗑️ إزالة الصورة</button>
                </div>

                <!-- Hidden File Input -->
                <input type="file" id="fileInput" accept="image/*,.pdf" style="display: none;" onchange="handleFileUpload(event)">

                <!-- Instructions -->
                <div style="margin-top: 12px; padding: 10px; background: #e8f5e8; border: 1px solid #c3e6cb; border-radius: 6px;">
                    <h5 style="color: #155724; margin-bottom: 6px; font-size: 14px;">📋 تعليمات الاستخدام:</h5>
                    <ul style="color: #155724; text-align: right; margin: 0; padding-right: 15px; font-size: 12px;">
                        <li>قم بتحميل صورة الشهادة الكاملة للمواطن</li>
                        <li>اقرأ المعلومات من الشهادة المعروضة</li>
                        <li>أدخل البيانات يدوياً في النموذج على اليسار</li>
                        <li>تأكد من صحة جميع المعلومات قبل الحفظ</li>
                        <li>يمكنك تكبير الصورة لرؤية التفاصيل بوضوح</li>
                    </ul>
                </div>

                <!-- Backup Section -->
                <div style="margin-top: 12px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
                    <h5 style="color: #856404; margin-bottom: 8px; font-size: 14px;">💾 النسخ الاحتياطي والأرشفة</h5>
                    <div style="display: flex; gap: 6px; flex-wrap: wrap; justify-content: center;">
                        <button type="button" class="btn btn-primary" onclick="exportAllData()" style="font-size: 12px; padding: 6px 12px;">📤 تصدير جميع البيانات</button>
                        <button type="button" class="btn btn-success" onclick="autoBackup()" style="font-size: 12px; padding: 6px 12px;">🔄 نسخ احتياطي تلقائي</button>
                    </div>
                    <div style="margin-top: 6px; font-size: 11px; color: #856404; text-align: center;">
                        💡 يُنصح بعمل نسخة احتياطية يومياً لضمان عدم فقدان البيانات
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include IndexedDB Manager -->
    <script src="indexeddb-manager.js"></script>
    <script>
        // Global variables
        let editingId = null;
        let currentCertificateImage = null;

        // Initialize page
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // Initialize IndexedDB
                await citizensDB.init();
                console.log('تم تهيئة IndexedDB بنجاح');

                // Update statistics
                await updateStatistics();

                // Set today's date as default
                document.getElementById('registrationDate').value = new Date().toISOString().split('T')[0];

                // Generate automatic act number
                generateActNumber();

                // Check for edit parameters
                checkForEditParameters();

                // Add keyboard shortcuts
                setupKeyboardShortcuts();

            } catch (error) {
                console.error('خطأ في تهيئة الصفحة:', error);
                showAlert('❌ خطأ في تهيئة قاعدة البيانات', 'error');
            }
        });

        // Setup keyboard shortcuts
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl+S or Cmd+S for save and new (الوظيفة الوحيدة المتبقية)
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    saveAndNew();
                }

                // Ctrl+R or Cmd+R for clear form (prevent default browser refresh)
                if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                    e.preventDefault();
                    clearForm();
                }
            });
        }

        // Update statistics with IndexedDB support
        async function updateStatistics() {
            try {
                const dbInfo = await citizensDB.getDatabaseInfo();

                if (dbInfo) {
                    document.getElementById('totalCitizens').textContent = dbInfo.totalCitizens.toLocaleString();
                    document.getElementById('withCertificates').textContent = dbInfo.withCertificates.toLocaleString();

                    // Get all citizens for detailed stats
                    const citizens = await citizensDB.getAllCitizens();
                    const males = citizens.filter(c => c.gender === 'ذكر').length;
                    const females = citizens.filter(c => c.gender === 'أنثى').length;

                    const today = new Date().toISOString().split('T')[0];
                    const todayRegistrations = citizens.filter(c => c.registrationDate === today).length;

                    document.getElementById('maleCount').textContent = males.toLocaleString();
                    document.getElementById('femaleCount').textContent = females.toLocaleString();
                    document.getElementById('todayRegistrations').textContent = todayRegistrations.toLocaleString();
                }
            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // Generate automatic act number
        async function generateActNumber() {
            try {
                const citizens = await citizensDB.getAllCitizens();
                const currentYear = new Date().getFullYear();
                const thisYearCitizens = citizens.filter(c => {
                    const regDate = new Date(c.registrationDate);
                    return regDate.getFullYear() === currentYear;
                });

                const nextNumber = thisYearCitizens.length + 1;
                const actNumber = `${nextNumber}/${currentYear}`;
                document.getElementById('actNumber').value = actNumber;
            } catch (error) {
                console.error('خطأ في توليد رقم القيد:', error);
                const currentYear = new Date().getFullYear();
                document.getElementById('actNumber').value = `1/${currentYear}`;
            }
        }

        // Toggle act number mode
        function toggleActNumberMode() {
            const mode = document.querySelector('input[name="actNumberMode"]:checked').value;
            const actNumberInput = document.getElementById('actNumber');
            const helpText = document.getElementById('actNumberHelp');

            if (mode === 'auto') {
                actNumberInput.readOnly = true;
                actNumberInput.style.backgroundColor = '#f8f9fa';
                helpText.textContent = 'سيتم توليد الرقم تلقائياً بصيغة: رقم/سنة';
                generateActNumber();
            } else {
                actNumberInput.readOnly = false;
                actNumberInput.style.backgroundColor = 'white';
                helpText.textContent = 'أدخل رقم القيد يدوياً بصيغة: رقم/سنة';
                actNumberInput.focus();
            }
        }

        // Handle form submission
        document.getElementById('citizenForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const loadingIndicator = document.getElementById('loadingIndicator');
            loadingIndicator.style.display = 'block';

            try {
                const formData = new FormData(e.target);

                // Generate unique ID for new citizens
                const citizenId = editingId || `citizen_${Date.now()}_${Math.floor(Math.random() * 100000)}_${performance.now().toString().replace('.', '')}`;

                const citizenData = {
                    id: citizenId, // إضافة ID مطلوب لقاعدة البيانات
                    firstNameAr: formData.get('firstNameAr'),
                    firstNameFr: formData.get('firstNameFr'),
                    familyNameAr: formData.get('familyNameAr'),
                    familyNameFr: formData.get('familyNameFr'),
                    birthPlaceAr: formData.get('birthPlaceAr'),
                    birthPlaceFr: formData.get('birthPlaceFr'),
                    birthDate: formData.get('birthDate'),
                    hijriDate: formData.get('hijriDate'),
                    gender: formData.get('gender'),
                    fatherNameAr: formData.get('fatherNameAr'),
                    fatherNameFr: formData.get('fatherNameFr'),
                    motherNameAr: formData.get('motherNameAr'),
                    motherNameFr: formData.get('motherNameFr'),
                    actNumber: formData.get('actNumber'),
                    registrationDate: formData.get('registrationDate'),
                    createdAt: new Date().toISOString(),
                    timestamp: Date.now() // إضافة timestamp للتوافق
                };

                // Add certificate image if available
                if (currentCertificateImage) {
                    citizenData.certificateImage = currentCertificateImage;
                }

                // الآن دائماً "حفظ وإضافة جديد" بما أن هذا هو الزر الوحيد
                const isSaveAndNew = e.target.dataset.saveAndNew === 'true' || !editingId; // دائماً true للسجلات الجديدة

                if (editingId) {
                    // Update existing citizen - ID already set above
                    await citizensDB.updateCitizen(citizenData);
                    showAlert('✅ تم تحديث بيانات المواطن بنجاح', 'success');
                    editingId = null;
                } else {
                    // Add new citizen - دائماً مع إعداد للتسجيل التالي
                    console.log('إضافة مواطن جديد بـ ID:', citizenData.id);
                    console.log('بيانات المواطن:', citizenData);

                    // التحقق من وجود ID
                    if (!citizenData.id) {
                        throw new Error('لم يتم توليد ID للمواطن');
                    }

                    await citizensDB.addCitizen(citizenData);
                    showAlert('✅ تم حفظ البيانات بنجاح - جاهز لإدخال مواطن جديد', 'success');
                }

                // Update statistics
                await updateStatistics();

                // دائماً مسح النموذج وإعداده للتسجيل التالي
                clearForm();

                // Remove the flag if exists
                if (e.target.dataset.saveAndNew) {
                    delete e.target.dataset.saveAndNew;
                }

                // Generate new act number for next entry
                if (document.querySelector('input[name="actNumberMode"]:checked').value === 'auto') {
                    generateActNumber();
                }

            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showAlert('❌ خطأ في حفظ البيانات: ' + error.message, 'error');
            } finally {
                loadingIndicator.style.display = 'none';
            }
        });

        // Save and add new function (الوظيفة الافتراضية الآن)
        async function saveAndNew() {
            // Trigger form submission with save and new flag
            const form = document.getElementById('citizenForm');
            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });

            // Add flag to indicate this is save and new
            form.dataset.saveAndNew = 'true';
            form.dispatchEvent(submitEvent);
        }

        // Clear form
        function clearForm() {
            document.getElementById('citizenForm').reset();
            document.getElementById('registrationDate').value = new Date().toISOString().split('T')[0];
            removeImage();
            editingId = null;

            // Reset act number mode to auto
            document.querySelector('input[name="actNumberMode"][value="auto"]').checked = true;
            toggleActNumberMode();

            // Focus on first field
            document.getElementById('firstNameAr').focus();
        }

        // Show alert
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            alertContainer.innerHTML = `<div class="alert ${alertClass}">${message}</div>`;

            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // Check for edit parameters
        function checkForEditParameters() {
            const urlParams = new URLSearchParams(window.location.search);

            if (urlParams.get('edit') === 'true') {
                const citizenId = parseInt(urlParams.get('id'));

                // Fill form with citizen data
                document.getElementById('firstNameAr').value = urlParams.get('firstNameAr') || '';
                document.getElementById('firstNameFr').value = urlParams.get('firstNameFr') || '';
                document.getElementById('familyNameAr').value = urlParams.get('familyNameAr') || '';
                document.getElementById('familyNameFr').value = urlParams.get('familyNameFr') || '';
                document.getElementById('birthPlaceAr').value = urlParams.get('birthPlaceAr') || '';
                document.getElementById('birthPlaceFr').value = urlParams.get('birthPlaceFr') || '';
                document.getElementById('birthDate').value = urlParams.get('birthDate') || '';
                document.getElementById('hijriDate').value = urlParams.get('hijriDate') || '';
                document.getElementById('gender').value = urlParams.get('gender') || '';
                document.getElementById('fatherNameAr').value = urlParams.get('fatherNameAr') || '';
                document.getElementById('fatherNameFr').value = urlParams.get('fatherNameFr') || '';
                document.getElementById('motherNameAr').value = urlParams.get('motherNameAr') || '';
                document.getElementById('motherNameFr').value = urlParams.get('motherNameFr') || '';
                document.getElementById('actNumber').value = urlParams.get('actNumber') || '';
                document.getElementById('registrationDate').value = urlParams.get('registrationDate') || '';

                // Set editing mode
                editingId = citizenId;

                // Update form title
                document.querySelector('.form-section h2').innerHTML = '✏️ تعديل بيانات المواطن';

                // Clear URL parameters
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }

        // Image handling functions
        let currentZoom = 1;
        let isDragging = false;
        let startX, startY, scrollLeft, scrollTop;

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const imageData = e.target.result;

                // Store image data
                currentCertificateImage = {
                    data: imageData,
                    fileName: file.name,
                    uploadDate: new Date().toISOString(),
                    hasImage: true
                };

                // Display image
                displayImage(imageData);
            };
            reader.readAsDataURL(file);
        }

        function displayImage(imageSrc) {
            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');
            const uploadedImage = document.getElementById('uploadedImage');

            uploadContent.style.display = 'none';
            imageDisplay.style.display = 'block';
            imageControls.style.display = 'block';
            uploadedImage.src = imageSrc;

            // Reset zoom
            currentZoom = 1;
            uploadedImage.style.transform = 'scale(1)';

            // Add click to zoom
            uploadedImage.onclick = function() {
                if (currentZoom === 1) {
                    zoomIn();
                } else {
                    resetZoom();
                }
            };

            // Add wheel zoom
            uploadedImage.addEventListener('wheel', function(e) {
                e.preventDefault();
                if (e.deltaY < 0) {
                    zoomIn();
                } else {
                    zoomOut();
                }
            });
        }

        function zoomIn(event) {
            if (event) event.preventDefault();
            currentZoom = Math.min(currentZoom * 1.2, 5);
            document.getElementById('uploadedImage').style.transform = `scale(${currentZoom})`;
        }

        function zoomOut(event) {
            if (event) event.preventDefault();
            currentZoom = Math.max(currentZoom / 1.2, 0.5);
            document.getElementById('uploadedImage').style.transform = `scale(${currentZoom})`;
        }

        function resetZoom(event) {
            if (event) event.preventDefault();
            currentZoom = 1;
            document.getElementById('uploadedImage').style.transform = 'scale(1)';
        }

        function removeImage(event) {
            if (event) event.preventDefault();

            const uploadContent = document.getElementById('uploadContent');
            const imageDisplay = document.getElementById('imageDisplay');
            const imageControls = document.getElementById('imageControls');

            uploadContent.style.display = 'block';
            imageDisplay.style.display = 'none';
            imageControls.style.display = 'none';

            currentCertificateImage = null;
            document.getElementById('fileInput').value = '';
        }

        // Export all data
        async function exportAllData() {
            try {
                const citizens = await citizensDB.getAllCitizens();

                if (citizens.length === 0) {
                    alert('⚠️ لا توجد بيانات للتصدير');
                    return;
                }

                const currentDate = new Date();
                const dateStr = currentDate.toISOString().split('T')[0];
                const timeStr = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                const dbInfo = await citizensDB.getDatabaseInfo();

                const backupData = {
                    exportDate: currentDate.toISOString(),
                    totalCitizens: citizens.length,
                    withCertificates: dbInfo ? dbInfo.withCertificates : 0,
                    version: '3.0-IndexedDB',
                    storageInfo: dbInfo,
                    data: citizens
                };

                const dataStr = JSON.stringify(backupData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `citizens_backup_indexeddb_${dateStr}_${timeStr}.json`;
                link.click();
                URL.revokeObjectURL(url);

                showAlert(`✅ تم تصدير النسخة الاحتياطية بنجاح\nعدد المواطنين: ${citizens.length.toLocaleString()}`, 'success');

            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                showAlert('❌ خطأ في تصدير البيانات', 'error');
            }
        }

        // Auto backup
        async function autoBackup() {
            try {
                const citizens = await citizensDB.getAllCitizens();

                if (citizens.length === 0) {
                    showAlert('⚠️ لا توجد بيانات للنسخ الاحتياطي', 'error');
                    return;
                }

                const lastBackup = localStorage.getItem('lastBackupDate');
                const today = new Date().toDateString();

                if (lastBackup === today) {
                    const confirmBackup = confirm('تم عمل نسخة احتياطية اليوم بالفعل.\nهل تريد عمل نسخة احتياطية أخرى؟');
                    if (!confirmBackup) return;
                }

                await exportAllData();
                localStorage.setItem('lastBackupDate', today);

            } catch (error) {
                console.error('خطأ في النسخ الاحتياطي:', error);
                showAlert('❌ خطأ في النسخ الاحتياطي', 'error');
            }
        }
    </script>
</body>
</html>
