<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج إدخال بيانات البطاقة الشخصية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a 0%, #e74c3c 50%, #c41e3a 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 25px 0;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .morocco-emblem {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            border: 3px solid rgba(255,255,255,0.2);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .header-text h1 {
            font-size: 2em;
            margin: 0 0 8px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-text .subtitle {
            font-size: 1.1em;
            margin: 0 0 5px 0;
            opacity: 0.95;
            font-weight: 500;
        }

        .header-text .department {
            font-size: 0.9em;
            opacity: 0.8;
            font-weight: 400;
        }

        .header-left {
            text-align: left;
            font-size: 0.9em;
        }

        .current-time {
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 20px;
            margin-bottom: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .user-info {
            opacity: 0.8;
        }

        /* قسم أزرار الانتقال */
        .header-navigation-section {
            background: rgba(0,0,0,0.15);
            border-top: 1px solid rgba(255,255,255,0.1);
            padding: 12px 0;
            position: relative;
        }

        .header-navigation-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        .header-navigation {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            flex-wrap: nowrap;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .header-navigation::-webkit-scrollbar {
            display: none;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 12px 18px;
            border: none;
            border-radius: 30px;
            text-decoration: none;
            font-size: 0.9em;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            min-width: 140px;
            justify-content: center;
            text-align: center;
            flex-shrink: 0;
            white-space: nowrap;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.35);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.25);
            border-color: rgba(255,255,255,0.5);
        }

        .nav-btn:active {
            transform: translateY(-1px);
        }

        .nav-btn.active {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-color: rgba(231, 76, 60, 1);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
            transform: translateY(-2px);
        }

        .nav-btn.active:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.5);
        }

        .nav-btn .icon {
            font-size: 1.1em;
            filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.3));
        }

        .nav-btn span:last-child {
            font-weight: 600;
            letter-spacing: 0.3px;
        }

        /* تحسين الأزرار النشطة */
        .nav-btn.active .icon {
            animation: pulse-icon 2s ease-in-out infinite;
        }

        @keyframes pulse-icon {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .form-container {
            padding: 40px;
        }

        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }

        .section-title {
            font-size: 1.4em;
            color: #c41e3a;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #c41e3a;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
        }

        .form-group.full-width {
            flex: 100%;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        input[type="text"],
        input[type="date"],
        textarea,
        select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: white;
        }

        input[type="text"]:focus,
        input[type="date"]:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 10px rgba(196, 30, 58, 0.2);
            transform: translateY(-2px);
        }

        textarea {
            resize: vertical;
            min-height: 80px;
        }

        .date-group {
            display: flex;
            gap: 15px;
            align-items: end;
        }

        .date-input {
            flex: 1;
        }

        .hijri-note {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
            font-style: italic;
        }

        .buttons-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            min-width: 150px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #c41e3a 0%, #a01729 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(196, 30, 58, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #228b22 0%, #1e7e1e 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(34, 139, 34, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #666;
            border: 2px solid #ddd;
        }

        .btn-outline:hover {
            background: #f8f9fa;
            border-color: #c41e3a;
            color: #c41e3a;
        }

        .required {
            color: #c41e3a;
        }

        .form-note {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c5460;
        }

        .form-note strong {
            color: #0a4c57;
        }

        /* تحسينات للشاشات الكبيرة */
        @media (min-width: 1400px) {
            .header-navigation {
                gap: 20px;
            }

            .nav-btn {
                min-width: 160px;
                padding: 14px 20px;
                font-size: 1em;
            }
        }

        @media (max-width: 992px) {
            .header-navigation {
                gap: 12px;
            }

            .nav-btn {
                min-width: 130px;
                padding: 10px 16px;
                font-size: 0.85em;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .header-text h1 {
                font-size: 1.6em;
            }

            .header-navigation {
                gap: 8px;
                padding: 0 10px;
                justify-content: flex-start;
            }

            .nav-btn {
                padding: 8px 12px;
                font-size: 0.8em;
                min-width: 110px;
                gap: 6px;
            }

            .morocco-emblem {
                width: 60px;
                height: 60px;
                font-size: 1.8em;
            }

            .form-row {
                flex-direction: column;
            }

            .form-group {
                min-width: 100%;
            }

            .date-group {
                flex-direction: column;
                gap: 10px;
            }

            .buttons-container {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        /* تحسينات للطباعة */
        @media print {
            body {
                background: white;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
            }

            .buttons-container {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <!-- شريط علوي -->
            <div class="header-top">
                🇲🇦 المملكة المغربية - وزارة الداخلية - مديرية الحالة المدنية
            </div>

            <!-- الهيدر الرئيسي -->
            <div class="header-main">
                <div class="header-content">
                    <div class="header-right">
                        <div class="morocco-emblem">🇲🇦</div>
                        <div class="header-text">
                            <h1>نظام إدارة الحالة المدنية</h1>
                            <div class="subtitle">🆔 نموذج إدخال بيانات البطاقة الشخصية</div>
                            <div class="department">مكتب الحالة المدنية - أيير، إقليم أسفي</div>
                        </div>
                    </div>
                    <div class="header-left">
                        <div class="current-time" id="currentTime">
                            🕰️ جاري تحميل الوقت...
                        </div>
                        <div class="user-info">
                            👤 موظف الحالة المدنية
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم أزرار الانتقال -->
            <div class="header-navigation-section">
                <div class="header-content">
                    <div class="header-navigation">
                        <a href="main-dashboard.html" class="nav-btn">
                            <span class="icon">🏠</span>
                            <span>الرئيسية</span>
                        </a>
                        <a href="citizens-database-indexeddb.html" class="nav-btn">
                            <span class="icon">🗃️</span>
                            <span>إدارة البيانات</span>
                        </a>
                        <a href="search-citizens.html" class="nav-btn">
                            <span class="icon">🔍</span>
                            <span>البحث</span>
                        </a>
                        <a href="death-data-entry.html" class="nav-btn">
                            <span class="icon">⚱️</span>
                            <span>بيانات الوفاة</span>
                        </a>
                        <a href="personal-id-form.html" class="nav-btn active">
                            <span class="icon">🆔</span>
                            <span>البطاقة الشخصية</span>
                        </a>
                        <a href="login.html" class="nav-btn">
                            <span class="icon">🔐</span>
                            <span>تسجيل الدخول</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-container">
            <div class="form-note">
                <strong>ملاحظة:</strong> جميع الحقول المطلوبة مميزة بعلامة <span class="required">*</span>.
                يرجى التأكد من صحة البيانات قبل إنشاء البطاقة.
            </div>

            <form id="personalIdForm">
                <!-- قسم المعلومات الشخصية -->
                <div class="form-section">
                    <h2 class="section-title">
                        👤 المعلومات الشخصية
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstNameAr">الاسم الشخصي (عربي) <span class="required">*</span></label>
                            <input type="text" id="firstNameAr" name="firstNameAr" required placeholder="مثال: محمد">
                        </div>
                        <div class="form-group">
                            <label for="firstNameFr">الاسم الشخصي (فرنسي) <span class="required">*</span></label>
                            <input type="text" id="firstNameFr" name="firstNameFr" required placeholder="Exemple: Mohamed">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="familyNameAr">الاسم العائلي (عربي) <span class="required">*</span></label>
                            <input type="text" id="familyNameAr" name="familyNameAr" required placeholder="مثال: العلوي">
                        </div>
                        <div class="form-group">
                            <label for="familyNameFr">الاسم العائلي (فرنسي) <span class="required">*</span></label>
                            <input type="text" id="familyNameFr" name="familyNameFr" required placeholder="Exemple: Alaoui">
                        </div>
                    </div>
                </div>

                <!-- قسم تاريخ ومكان الولادة -->
                <div class="form-section">
                    <h2 class="section-title">
                        📅 تاريخ ومكان الولادة
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="birthDateHijri">تاريخ الولادة <span class="required">*</span></label>
                            <input type="text" id="birthDateHijri" name="birthDateHijri" required placeholder="مثال: 25/06/1411">
                            <div class="hijri-note">التاريخ الهجري بالصيغة: يوم/شهر/سنة</div>
                        </div>
                        <div class="form-group">
                            <label for="birthDateGregorian">موافق <span class="required">*</span></label>
                            <input type="date" id="birthDateGregorian" name="birthDateGregorian" required>
                            <div class="hijri-note">التاريخ الميلادي الموافق</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="birthPlaceAr">مكان الولادة (عربي) <span class="required">*</span></label>
                            <input type="text" id="birthPlaceAr" name="birthPlaceAr" required placeholder="مثال: الرباط">
                        </div>
                        <div class="form-group">
                            <label for="birthPlaceFr">مكان الولادة (فرنسي) <span class="required">*</span></label>
                            <input type="text" id="birthPlaceFr" name="birthPlaceFr" required placeholder="Exemple: Rabat">
                        </div>
                    </div>
                </div>

                <!-- قسم معلومات الوالدين -->
                <div class="form-section">
                    <h2 class="section-title">
                        👨‍👩‍👧‍👦 معلومات الوالدين
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="fatherName">اسم الوالد <span class="required">*</span></label>
                            <input type="text" id="fatherName" name="fatherName" required placeholder="مثال: عبد الله العلوي">
                        </div>
                        <div class="form-group">
                            <label for="motherName">اسم الوالدة <span class="required">*</span></label>
                            <input type="text" id="motherName" name="motherName" required placeholder="مثال: فاطمة الزهراني">
                        </div>
                    </div>
                </div>

                <!-- قسم العنوان الحالي -->
                <div class="form-section">
                    <h2 class="section-title">
                        🏠 العنوان الحالي
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="currentAddressAr">العنوان الحالي (عربي) <span class="required">*</span></label>
                            <input type="text" id="currentAddressAr" name="currentAddressAr" required placeholder="مثال: حي السلام">
                        </div>
                        <div class="form-group">
                            <label for="currentAddressFr">العنوان الحالي (فرنسي)</label>
                            <input type="text" id="currentAddressFr" name="currentAddressFr" placeholder="Exemple: Hay Salam">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="commune">الجماعة</label>
                            <input type="text" id="commune" name="commune" value="جماعة أيير" placeholder="جماعة أيير">
                        </div>
                        <div class="form-group">
                            <label for="province">الإقليم</label>
                            <input type="text" id="province" name="province" value="إقليم آسفي" placeholder="إقليم آسفي">
                        </div>
                    </div>
                </div>

                <!-- قسم البيانات الهامشية -->
                <div class="form-section">
                    <h2 class="section-title">
                        📝 البيانات الهامشية
                    </h2>

                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="marginalData">البيانات الهامشية</label>
                            <textarea id="marginalData" name="marginalData" placeholder="أدخل أي بيانات إضافية أو اتركه فارغ"></textarea>
                        </div>
                    </div>
                </div>

                <!-- قسم بيانات العقد والإصدار -->
                <div class="form-section">
                    <h2 class="section-title">
                        📄 بيانات العقد والإصدار
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="actNumber">رقم العقد</label>
                            <input type="text" id="actNumber" name="actNumber" placeholder="مثال: 15/2024">
                        </div>
                        <div class="form-group">
                            <label for="actYear">سنة العقد</label>
                            <input type="text" id="actYear" name="actYear" placeholder="مثال: 2024">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="issuingCommune">جماعة الإصدار</label>
                            <input type="text" id="issuingCommune" name="issuingCommune" value="أيير" placeholder="أيير">
                        </div>
                        <div class="form-group">
                            <label for="issueDate">تاريخ الإصدار</label>
                            <input type="date" id="issueDate" name="issueDate">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="certificationDate">تاريخ التصديق</label>
                            <input type="date" id="certificationDate" name="certificationDate">
                        </div>
                    </div>
                </div>

                <!-- قسم بيانات الشاهد -->
                <div class="form-section">
                    <h2 class="section-title">
                        👤 بيانات الشاهد
                    </h2>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="witnessName">أنا الموقع أسفله</label>
                            <input type="text" id="witnessName" name="witnessName" placeholder="مثال: عبد الرحمن العلوي">
                        </div>
                        <div class="form-group">
                            <label for="witnessAddress">الساكن حاليا ب</label>
                            <input type="text" id="witnessAddress" name="witnessAddress" placeholder="مثال: حي النهضة أيير">
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="buttons-container">
                    <button type="submit" class="btn btn-primary">
                        🆔 إنشاء البطاقة الشخصية
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="fillSampleData()">
                        📝 ملء بيانات تجريبية
                    </button>
                    <button type="reset" class="btn btn-outline">
                        🔄 مسح البيانات
                    </button>
                    <a href="index.html" class="btn btn-outline">
                        🏠 العودة للرئيسية
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script>
        // ملء بيانات تجريبية
        function fillSampleData() {
            document.getElementById('firstNameAr').value = 'محمد';
            document.getElementById('firstNameFr').value = 'Mohamed';
            document.getElementById('familyNameAr').value = 'العلوي';
            document.getElementById('familyNameFr').value = 'Alaoui';
            document.getElementById('birthDateHijri').value = '25/06/1411';
            document.getElementById('birthDateGregorian').value = '1990-03-15';
            document.getElementById('birthPlaceAr').value = 'أيير';
            document.getElementById('birthPlaceFr').value = 'Ayir';
            document.getElementById('fatherName').value = 'عبد الله العلوي';
            document.getElementById('motherName').value = 'فاطمة الزهراني';
            document.getElementById('currentAddressAr').value = 'حي السلام';
            document.getElementById('currentAddressFr').value = 'Hay Salam';
            document.getElementById('marginalData').value = '';

            // بيانات العقد والإصدار
            document.getElementById('actNumber').value = '15/2024';
            document.getElementById('actYear').value = '2024';
            document.getElementById('issuingCommune').value = 'أيير';
            document.getElementById('issueDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('certificationDate').value = new Date().toISOString().split('T')[0];

            // بيانات الشاهد
            document.getElementById('witnessName').value = 'عبد الرحمن العلوي';
            document.getElementById('witnessAddress').value = 'حي النهضة أيير';

            // إظهار رسالة تأكيد
            alert('✅ تم ملء البيانات التجريبية بنجاح!');
        }

        // معالجة إرسال النموذج
        document.getElementById('personalIdForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // جمع البيانات
            const formData = {
                firstNameAr: document.getElementById('firstNameAr').value,
                firstNameFr: document.getElementById('firstNameFr').value,
                familyNameAr: document.getElementById('familyNameAr').value,
                familyNameFr: document.getElementById('familyNameFr').value,
                birthDateHijri: document.getElementById('birthDateHijri').value,
                birthDateGregorian: document.getElementById('birthDateGregorian').value,
                birthPlaceAr: document.getElementById('birthPlaceAr').value,
                birthPlaceFr: document.getElementById('birthPlaceFr').value,
                fatherName: document.getElementById('fatherName').value,
                motherName: document.getElementById('motherName').value,
                currentAddressAr: document.getElementById('currentAddressAr').value,
                currentAddressFr: document.getElementById('currentAddressFr').value,
                commune: document.getElementById('commune').value,
                province: document.getElementById('province').value,
                marginalData: document.getElementById('marginalData').value,

                // بيانات العقد والإصدار
                actNumber: document.getElementById('actNumber').value,
                actYear: document.getElementById('actYear').value,
                issuingCommune: document.getElementById('issuingCommune').value,
                issueDate: document.getElementById('issueDate').value,
                certificationDate: document.getElementById('certificationDate').value,

                // بيانات الشاهد
                witnessName: document.getElementById('witnessName').value,
                witnessAddress: document.getElementById('witnessAddress').value
            };

            // التحقق من البيانات المطلوبة
            const requiredFields = ['firstNameAr', 'firstNameFr', 'familyNameAr', 'familyNameFr',
                                  'birthDateHijri', 'birthDateGregorian', 'birthPlaceAr', 'birthPlaceFr',
                                  'fatherName', 'motherName', 'currentAddressAr'];

            let isValid = true;
            for (let field of requiredFields) {
                if (!formData[field] || formData[field].trim() === '') {
                    isValid = false;
                    document.getElementById(field).style.borderColor = '#dc3545';
                } else {
                    document.getElementById(field).style.borderColor = '#ddd';
                }
            }

            if (!isValid) {
                alert('⚠️ يرجى ملء جميع الحقول المطلوبة!');
                return;
            }

            // تحويل البيانات إلى URL parameters
            const params = new URLSearchParams(formData);

            // فتح صفحة البطاقة الشخصية مع البيانات
            window.open(`personal-id-card.html?${params.toString()}`, '_blank');
        });

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للحقول
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // تحديث التاريخ الميلادي عند تغيير التاريخ الهجري (اختياري)
            document.getElementById('birthDateHijri').addEventListener('change', function() {
                // يمكن إضافة تحويل تلقائي من هجري إلى ميلادي هنا
            });

            // تحديث الوقت الحالي
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            // تهيئة أزرار التنقل
            initializeNavigation();

            // تركيز على أول حقل
            document.getElementById('firstNameAr').focus();

            console.log('✅ تم تهيئة صفحة البطاقة الشخصية بنجاح');
        });

        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Africa/Casablanca'
            };

            const timeString = now.toLocaleDateString('ar-MA', options);
            document.getElementById('currentTime').innerHTML = `🕰️ ${timeString}`;
        }

        // تفعيل أزرار التنقل
        function initializeNavigation() {
            // إضافة تفاعل للأزرار
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // إزالة الفئة النشطة من جميع الأزرار
                    document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));

                    // إضافة الفئة النشطة للزر المنقور
                    this.classList.add('active');

                    // تأثير بصري
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        }

        // معالجة الضغط على Enter
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                // الانتقال للحقل التالي
                const inputs = Array.from(document.querySelectorAll('input, select, textarea'));
                const currentIndex = inputs.indexOf(e.target);
                if (currentIndex < inputs.length - 1) {
                    inputs[currentIndex + 1].focus();
                }
            }
        });
    </script>
</body>
</html>
