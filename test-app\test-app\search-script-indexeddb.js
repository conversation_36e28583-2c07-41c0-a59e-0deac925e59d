// Search script using IndexedDB
let citizens = [];
let currentPage = 1;
let itemsPerPage = 20;
let currentResults = [];
let citizenToDelete = null;

// Initialize page
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Initialize IndexedDB
        await citizensDB.init();
        console.log('تم تهيئة قاعدة البيانات بنجاح');

        // Load citizens from IndexedDB
        await loadCitizens();

        // Update statistics
        await updateStatistics();

        // Load preferences
        loadPreferences();

        // Add Enter key support for search fields
        const searchFields = ['searchName', 'searchActNumber', 'searchBirthDate', 'searchParent'];
        searchFields.forEach(fieldId => {
            document.getElementById(fieldId).addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchCitizens();
                }
            });
        });

    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        alert('❌ خطأ في تهيئة قاعدة البيانات\nسيتم استخدام التخزين المحلي كبديل');
        // Fallback to localStorage if IndexedDB fails
        loadFromLocalStorage();
    }
});

// Load citizens from IndexedDB
async function loadCitizens() {
    try {
        citizens = await citizensDB.getAllCitizens();
        console.log(`تم تحميل ${citizens.length} مواطن من قاعدة البيانات`);
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        citizens = [];
    }
}

// Fallback to localStorage
function loadFromLocalStorage() {
    citizens = JSON.parse(localStorage.getItem('citizens')) || [];
    console.log(`تم تحميل ${citizens.length} مواطن من التخزين المحلي`);
}

// Update statistics with database info
async function updateStatistics() {
    try {
        // Load fresh data from IndexedDB
        await loadCitizens();

        const dbInfo = await citizensDB.getDatabaseInfo();

        if (dbInfo) {
            const totalCitizens = dbInfo.totalCitizens;
            const withCertificates = dbInfo.withCertificates;
            const withoutCertificates = totalCitizens - withCertificates;

            // Update main statistics
            document.getElementById('totalCitizens').textContent = totalCitizens.toLocaleString();
            document.getElementById('withCertificates').textContent = withCertificates.toLocaleString();
            document.getElementById('withoutCertificates').textContent = withoutCertificates.toLocaleString();

            console.log(`إحصائيات محدثة: إجمالي=${totalCitizens}, مع شهادات=${withCertificates}, بدون شهادات=${withoutCertificates}`);

            // Storage info removed as requested
        }
    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);

        // Fallback to manual calculation from loaded citizens
        await loadCitizens();
        const total = citizens.length;

        // Count certificates manually from loaded data
        const withCertificates = citizens.filter(c =>
            c.certificateImage &&
            (c.certificateImage.hasImage === true || c.certificateImage.data)
        ).length;

        const withoutCertificates = total - withCertificates;

        document.getElementById('totalCitizens').textContent = total.toLocaleString();
        document.getElementById('withCertificates').textContent = withCertificates.toLocaleString();
        document.getElementById('withoutCertificates').textContent = withoutCertificates.toLocaleString();

        console.log(`إحصائيات احتياطية: إجمالي=${total}, مع شهادات=${withCertificates}, بدون شهادات=${withoutCertificates}`);
    }
}

// Search citizens using IndexedDB
async function searchCitizens() {
    const searchName = document.getElementById('searchName').value.trim();
    const searchActNumber = document.getElementById('searchActNumber').value.trim();
    const searchBirthDate = document.getElementById('searchBirthDate').value;
    const searchParent = document.getElementById('searchParent').value.trim();

    // Check if at least one search criteria is provided
    if (!searchName && !searchActNumber && !searchBirthDate && !searchParent) {
        alert('⚠️ يرجى إدخال معيار بحث واحد على الأقل');
        return;
    }

    try {
        // Use IndexedDB search
        const searchCriteria = {
            name: searchName,
            actNumber: searchActNumber,
            birthDate: searchBirthDate,
            parent: searchParent
        };

        const filtered = await citizensDB.searchCitizens(searchCriteria);

        // Store results and reset pagination
        currentResults = filtered;
        currentPage = 1;

        displayResults();

    } catch (error) {
        console.error('خطأ في البحث:', error);
        alert('❌ خطأ في البحث\nيرجى المحاولة مرة أخرى');
    }
}

// Display search results with pagination
function displayResults() {
    const container = document.getElementById('searchResults');

    if (currentResults.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #7f8c8d; font-style: italic;">لم يتم العثور على نتائج مطابقة</p>';
        return;
    }

    // Calculate pagination
    const totalPages = Math.ceil(currentResults.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const citizensToShow = currentResults.slice(startIndex, endIndex);

    // Build results HTML
    let resultsHTML = '';

    // Add pagination info
    resultsHTML += '<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center;">';
    resultsHTML += '<div style="margin-bottom: 10px;"><strong>📊 النتائج:</strong> ' + currentResults.length.toLocaleString() + ' مواطن | ';
    resultsHTML += '<strong>📄 الصفحة:</strong> ' + currentPage + ' من ' + totalPages + ' | ';
    resultsHTML += '<strong>📋 عرض:</strong> ' + (startIndex + 1) + '-' + Math.min(endIndex, currentResults.length) + '</div>';

    // Add pagination controls
    if (totalPages > 1) {
        resultsHTML += '<div>';
        if (currentPage > 1) {
            resultsHTML += '<button class="btn btn-secondary" onclick="changePage(' + (currentPage - 1) + ')" style="margin: 2px;">⬅️ السابق</button>';
        }

        // Page numbers
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            const activeClass = i === currentPage ? 'btn-primary' : 'btn-secondary';
            resultsHTML += '<button class="btn ' + activeClass + '" onclick="changePage(' + i + ')" style="margin: 2px;">' + i + '</button>';
        }

        if (currentPage < totalPages) {
            resultsHTML += '<button class="btn btn-secondary" onclick="changePage(' + (currentPage + 1) + ')" style="margin: 2px;">التالي ➡️</button>';
        }
        resultsHTML += '</div>';
    }
    resultsHTML += '</div>';

    // Add citizen cards
    citizensToShow.forEach(citizen => {
        const hasCertificate = citizen.certificateImage && citizen.certificateImage.hasImage;
        const certificateBadge = hasCertificate ?
            '<span class="certificate-badge">📄 يوجد شهادة كاملة</span>' :
            '<span class="certificate-badge no-certificate">❌ لا توجد شهادة</span>';

        resultsHTML += '<div class="citizen-card">';
        resultsHTML += '<div class="citizen-info">';
        resultsHTML += '<div><strong>الاسم:</strong> ' + (citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '') + '</div>';
        resultsHTML += '<div><strong>الاسم (فرنسي):</strong> ' + (citizen.firstNameFr || '') + ' ' + (citizen.familyNameFr || '') + '</div>';
        resultsHTML += '<div><strong>رقم القيد:</strong> ' + citizen.actNumber + '</div>';
        resultsHTML += '<div><strong>تاريخ الازدياد:</strong> ' + citizen.birthDate + '</div>';
        resultsHTML += '<div><strong>الجنس:</strong> ' + citizen.gender + '</div>';
        resultsHTML += '<div><strong>مكان الازدياد:</strong> ' + (citizen.birthPlaceAr || '') + '</div>';
        resultsHTML += '<div><strong>الوالد:</strong> ' + (citizen.fatherNameAr || '') + '</div>';
        resultsHTML += '<div><strong>الوالدة:</strong> ' + (citizen.motherNameAr || '') + '</div>';
        resultsHTML += '</div>';
        resultsHTML += '<div style="margin-bottom: 15px;">' + certificateBadge + '</div>';
        resultsHTML += '<div class="citizen-actions">';
        resultsHTML += '<button class="btn btn-success" onclick="printCertificate(' + citizen.id + ')">🖨️ طباعة عقد الازدياد</button>';
        if (hasCertificate) {
            resultsHTML += '<button class="btn btn-primary" onclick="printFullCertificate(' + citizen.id + ')">🖨️ طباعة الشهادة الكاملة</button>';
        }

        resultsHTML += '<button class="btn btn-secondary" onclick="editCitizen(' + citizen.id + ')">✏️ تعديل البيانات</button>';
        resultsHTML += '<button class="btn" style="background: #e74c3c; color: white;" onclick="deleteCitizen(' + citizen.id + ')">🗑️ حذف</button>';
        resultsHTML += '</div>';
        resultsHTML += '</div>';
    });

    container.innerHTML = resultsHTML;
}

// Change page function
function changePage(page) {
    currentPage = page;
    displayResults();

    // Scroll to top of results
    document.getElementById('searchResults').scrollIntoView({ behavior: 'smooth' });
}

// Show all citizens
async function showAll() {
    try {
        await loadCitizens();
        currentResults = [...citizens];
        currentPage = 1;
        displayResults();
    } catch (error) {
        console.error('خطأ في عرض جميع المواطنين:', error);
        alert('❌ خطأ في تحميل البيانات');
    }
}

// Clear search
function clearSearch() {
    document.getElementById('searchName').value = '';
    document.getElementById('searchActNumber').value = '';
    document.getElementById('searchBirthDate').value = '';
    document.getElementById('searchParent').value = '';

    // Clear results and show initial message
    currentResults = [];
    currentPage = 1;
    const container = document.getElementById('searchResults');
    container.innerHTML = '<p style="text-align: center; color: #7f8c8d; font-style: italic;">استخدم البحث أعلاه للعثور على المواطنين</p>';
}

// Print certificate
function printCertificate(id) {
    const citizen = currentResults.find(c => c.id === id);
    if (!citizen) return;

    const params = new URLSearchParams({
        firstNameAr: citizen.firstNameAr || '',
        firstNameFr: citizen.firstNameFr || '',
        familyNameAr: citizen.familyNameAr || '',
        familyNameFr: citizen.familyNameFr || '',
        birthPlaceAr: citizen.birthPlaceAr || '',
        birthPlaceFr: citizen.birthPlaceFr || '',
        birthDate: citizen.birthDate,
        hijriDate: citizen.hijriDate,
        gender: citizen.gender,
        fatherNameAr: citizen.fatherNameAr || '',
        fatherNameFr: citizen.fatherNameFr || '',
        motherNameAr: citizen.motherNameAr || '',
        motherNameFr: citizen.motherNameFr || '',
        actNumber: citizen.actNumber,
        registrationDate: citizen.registrationDate
    });

    window.open('dual-birth-certificate.html?' + params.toString(), '_blank');
}

// Print full certificate image
async function printFullCertificate(id) {
    try {
        // Get citizen with image from IndexedDB
        const citizen = await citizensDB.getCitizen(id, true);
        if (!citizen || !citizen.certificateImage || !citizen.certificateImage.data) {
            alert('❌ لم يتم العثور على الشهادة الكاملة');
            return;
        }

        // Create a new window for printing
        const printWindow = window.open('', '_blank');

        const htmlContent = '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>الشهادة الكاملة</title><style>body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; min-height: 100vh; background: white; } .certificate-image { max-width: 100%; max-height: 100vh; object-fit: contain; } @media print { @page { size: A4 portrait !important; margin: 0; } body { margin: 0; padding: 0; } .certificate-image { max-width: 100%; max-height: 100%; object-fit: contain; transform: rotate(0deg); } }</style></head><body><img src="' + citizen.certificateImage.data + '" alt="الشهادة الكاملة" class="certificate-image"><script>window.onload = function() { window.print(); window.onafterprint = function() { window.close(); } }</script></body></html>';

        printWindow.document.write(htmlContent);
        printWindow.document.close();

    } catch (error) {
        console.error('خطأ في طباعة الشهادة:', error);
        alert('❌ خطأ في طباعة الشهادة الكاملة');
    }
}

// Edit citizen
function editCitizen(id) {
    const citizen = currentResults.find(c => c.id === id);
    if (!citizen) return;

    // Create URL parameters for the citizen data
    const params = new URLSearchParams({
        edit: 'true',
        id: citizen.id,
        firstNameAr: citizen.firstNameAr || '',
        firstNameFr: citizen.firstNameFr || '',
        familyNameAr: citizen.familyNameAr || '',
        familyNameFr: citizen.familyNameFr || '',
        birthPlaceAr: citizen.birthPlaceAr || '',
        birthPlaceFr: citizen.birthPlaceFr || '',
        birthDate: citizen.birthDate || '',
        hijriDate: citizen.hijriDate || '',
        gender: citizen.gender || '',
        fatherNameAr: citizen.fatherNameAr || '',
        fatherNameFr: citizen.fatherNameFr || '',
        motherNameAr: citizen.motherNameAr || '',
        motherNameFr: citizen.motherNameFr || '',
        actNumber: citizen.actNumber || '',
        registrationDate: citizen.registrationDate || ''
    });

    // Redirect to citizens database page with edit parameters
    window.location.href = 'citizens-database-indexeddb.html?' + params.toString();
}

// Delete citizen - show confirmation modal
function deleteCitizen(id) {
    const citizen = currentResults.find(c => c.id === id);
    if (!citizen) return;

    // Store citizen ID for deletion
    citizenToDelete = id;

    // Fill modal with citizen details
    const citizenName = ((citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '')).trim();
    document.getElementById('deleteCitizenName').textContent = citizenName;
    document.getElementById('deleteCitizenActNumber').textContent = citizen.actNumber;
    document.getElementById('deleteCitizenBirthDate').textContent = citizen.birthDate;

    // Show modal
    document.getElementById('deleteModal').style.display = 'block';
}

// Confirm deletion
async function confirmDelete() {
    if (!citizenToDelete) return;

    const citizen = currentResults.find(c => c.id === citizenToDelete);
    if (!citizen) return;

    const citizenName = ((citizen.firstNameAr || '') + ' ' + (citizen.familyNameAr || '')).trim();

    try {
        // Delete from IndexedDB
        await citizensDB.deleteCitizen(citizenToDelete);

        // Update local arrays
        citizens = citizens.filter(c => c.id !== citizenToDelete);
        currentResults = currentResults.filter(c => c.id !== citizenToDelete);

        // Adjust page if needed
        const totalPages = Math.ceil(currentResults.length / itemsPerPage);
        if (currentPage > totalPages && totalPages > 0) {
            currentPage = totalPages;
        }

        // Update statistics
        await updateStatistics();

        // Refresh display
        displayResults();

        // Hide modal
        document.getElementById('deleteModal').style.display = 'none';

        // Show success message
        alert('✅ تم حذف بيانات المواطن بنجاح\nالاسم: ' + citizenName);

    } catch (error) {
        console.error('خطأ في حذف المواطن:', error);
        alert('❌ خطأ في حذف بيانات المواطن\nيرجى المحاولة مرة أخرى');
    }

    // Reset citizen to delete
    citizenToDelete = null;
}

// Cancel deletion
function cancelDelete() {
    // Hide modal
    document.getElementById('deleteModal').style.display = 'none';

    // Reset citizen to delete
    citizenToDelete = null;
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('deleteModal');
    if (event.target === modal) {
        cancelDelete();
    }
}

// Load preferences
function loadPreferences() {
    const savedItemsPerPage = localStorage.getItem('itemsPerPage');
    if (savedItemsPerPage) {
        itemsPerPage = parseInt(savedItemsPerPage);
        const select = document.getElementById('itemsPerPageSelect');
        if (select) {
            select.value = itemsPerPage;
        }
    }
}

// Change items per page
function changeItemsPerPage() {
    const select = document.getElementById('itemsPerPageSelect');
    itemsPerPage = parseInt(select.value);
    currentPage = 1; // Reset to first page

    // Save preference
    localStorage.setItem('itemsPerPage', itemsPerPage);

    // Refresh display if there are current results
    if (currentResults.length > 0) {
        displayResults();
    }

    alert('✅ تم تغيير عدد النتائج إلى ' + itemsPerPage + ' في الصفحة');
}

// Export all data with backup
async function exportAllData() {
    try {
        await loadCitizens();

        if (citizens.length === 0) {
            alert('⚠️ لا توجد بيانات للتصدير\nيرجى إضافة مواطنين أولاً');
            return;
        }

        const currentDate = new Date();
        const dateStr = currentDate.toISOString().split('T')[0];
        const timeStr = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

        // Get database info
        const dbInfo = await citizensDB.getDatabaseInfo();

        const backupData = {
            exportDate: currentDate.toISOString(),
            totalCitizens: citizens.length,
            withCertificates: dbInfo ? dbInfo.withCertificates : 0,
            version: '2.0-IndexedDB',
            storageInfo: dbInfo,
            data: citizens
        };

        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'citizens_backup_indexeddb_' + dateStr + '_' + timeStr + '.json';
        link.click();
        URL.revokeObjectURL(url);

        alert('✅ تم تصدير النسخة الاحتياطية بنجاح\nعدد المواطنين: ' + citizens.length.toLocaleString() + '\nالتاريخ: ' + dateStr);

    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        alert('❌ خطأ في تصدير البيانات');
    }
}

// Auto backup function
async function autoBackup() {
    try {
        await loadCitizens();

        if (citizens.length === 0) {
            alert('⚠️ لا توجد بيانات للنسخ الاحتياطي');
            return;
        }

        // Check last backup date
        const lastBackup = localStorage.getItem('lastBackupDate');
        const today = new Date().toDateString();

        if (lastBackup === today) {
            const confirmBackup = confirm('تم عمل نسخة احتياطية اليوم بالفعل.\nهل تريد عمل نسخة احتياطية أخرى؟');
            if (!confirmBackup) return;
        }

        // Export data
        await exportAllData();

        // Save backup date
        localStorage.setItem('lastBackupDate', today);

        alert('✅ تم حفظ تاريخ النسخة الاحتياطية\nسيتم تذكيرك غداً لعمل نسخة احتياطية جديدة');

    } catch (error) {
        console.error('خطأ في النسخ الاحتياطي:', error);
        alert('❌ خطأ في النسخ الاحتياطي');
    }
}
