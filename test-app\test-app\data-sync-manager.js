// Data Synchronization Manager
// يدير مزامنة البيانات ونقلها بين الأجهزة المختلفة

class DataSyncManager {
    constructor() {
        this.syncInProgress = false;
        this.lastSyncTime = null;
        this.syncHistory = [];
    }

    // تهيئة مدير المزامنة
    async init() {
        try {
            // تحميل تاريخ آخر مزامنة
            this.lastSyncTime = localStorage.getItem('lastSyncTime');
            
            // تحميل تاريخ المزامنة
            const history = localStorage.getItem('syncHistory');
            this.syncHistory = history ? JSON.parse(history) : [];

            console.log('✅ تم تهيئة مدير المزامنة');
            return true;
        } catch (error) {
            console.error('خطأ في تهيئة مدير المزامنة:', error);
            return false;
        }
    }

    // إنشاء حزمة بيانات للنقل
    async createDataPackage(includeImages = false) {
        try {
            console.log('📦 إنشاء حزمة البيانات...');

            // جلب جميع البيانات
            const citizens = await citizensDB.getAllCitizens();
            
            // إنشاء معلومات الحزمة
            const packageInfo = {
                version: '2.0',
                created: new Date().toISOString(),
                platform: this._getPlatformInfo(),
                totalRecords: citizens.length,
                includesImages: includeImages,
                checksum: this._generateChecksum(citizens)
            };

            // إضافة الصور إذا طُلب ذلك
            let citizensWithImages = citizens;
            if (includeImages) {
                console.log('🖼️ إضافة الصور إلى الحزمة...');
                citizensWithImages = [];
                for (const citizen of citizens) {
                    if (citizen.certificateImage && citizen.certificateImage.hasImage) {
                        const fullCitizen = await citizensDB.getCitizen(citizen.id, true);
                        citizensWithImages.push(fullCitizen);
                    } else {
                        citizensWithImages.push(citizen);
                    }
                }
            }

            const dataPackage = {
                packageInfo,
                citizens: citizensWithImages,
                metadata: {
                    exportedBy: 'Civil Registry System',
                    exportMethod: 'DataSyncManager',
                    compatibility: 'Universal'
                }
            };

            console.log(`✅ تم إنشاء حزمة البيانات: ${citizens.length} سجل`);
            return dataPackage;

        } catch (error) {
            console.error('خطأ في إنشاء حزمة البيانات:', error);
            throw error;
        }
    }

    // تصدير البيانات للنقل
    async exportForTransfer(includeImages = false) {
        try {
            const dataPackage = await this.createDataPackage(includeImages);
            
            // تحويل إلى JSON
            const jsonData = JSON.stringify(dataPackage, null, 2);
            
            // إنشاء ملف للتحميل
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            // إنشاء رابط التحميل
            const link = document.createElement('a');
            link.href = url;
            link.download = `civil-registry-data-${new Date().toISOString().split('T')[0]}.json`;
            
            // تحميل الملف
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            // تسجيل عملية التصدير
            this._recordSyncOperation('export', {
                recordCount: dataPackage.citizens.length,
                includeImages: includeImages,
                timestamp: new Date().toISOString()
            });

            console.log('✅ تم تصدير البيانات للنقل');
            return true;

        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            throw error;
        }
    }

    // استيراد البيانات من جهاز آخر
    async importFromTransfer(file, options = {}) {
        try {
            if (this.syncInProgress) {
                throw new Error('عملية مزامنة أخرى قيد التنفيذ');
            }

            this.syncInProgress = true;
            console.log('📥 بدء استيراد البيانات...');

            // قراءة الملف
            const fileContent = await this._readFile(file);
            const importData = JSON.parse(fileContent);

            // التحقق من صحة البيانات
            const validation = this._validateDataPackage(importData);
            if (!validation.isValid) {
                throw new Error(`بيانات غير صحيحة: ${validation.errors.join(', ')}`);
            }

            // إعداد خيارات الاستيراد
            const importOptions = {
                clearExisting: options.clearExisting || false,
                skipDuplicates: options.skipDuplicates !== false,
                createBackup: options.createBackup !== false,
                ...options
            };

            // إنشاء نسخة احتياطية قبل الاستيراد
            if (importOptions.createBackup) {
                await this._createPreImportBackup();
            }

            // مسح البيانات الموجودة إذا طُلب ذلك
            if (importOptions.clearExisting) {
                console.log('🗑️ مسح البيانات الموجودة...');
                await citizensDB.clearAllData();
            }

            // استيراد البيانات
            let imported = 0;
            let skipped = 0;
            let errors = 0;

            for (const citizen of importData.citizens) {
                try {
                    // التحقق من وجود السجل
                    if (importOptions.skipDuplicates) {
                        const existing = await citizensDB.getCitizen(citizen.id);
                        if (existing) {
                            skipped++;
                            continue;
                        }
                    }

                    // إضافة السجل
                    await citizensDB.addCitizen(citizen);
                    imported++;

                } catch (error) {
                    console.warn(`فشل في استيراد السجل ${citizen.id}:`, error);
                    errors++;
                }
            }

            // تسجيل عملية الاستيراد
            const importResult = {
                totalRecords: importData.citizens.length,
                imported,
                skipped,
                errors,
                timestamp: new Date().toISOString(),
                sourceInfo: importData.packageInfo
            };

            this._recordSyncOperation('import', importResult);

            console.log(`✅ تم الاستيراد: ${imported} سجل، تم تخطي: ${skipped}، أخطاء: ${errors}`);
            
            this.syncInProgress = false;
            return importResult;

        } catch (error) {
            this.syncInProgress = false;
            console.error('خطأ في استيراد البيانات:', error);
            throw error;
        }
    }

    // قراءة ملف
    _readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('خطأ في قراءة الملف'));
            reader.readAsText(file);
        });
    }

    // التحقق من صحة حزمة البيانات
    _validateDataPackage(data) {
        const errors = [];

        // التحقق من الهيكل الأساسي
        if (!data.packageInfo) {
            errors.push('معلومات الحزمة مفقودة');
        }

        if (!data.citizens || !Array.isArray(data.citizens)) {
            errors.push('بيانات المواطنين مفقودة أو غير صحيحة');
        }

        // التحقق من الإصدار
        if (data.packageInfo && data.packageInfo.version) {
            const version = parseFloat(data.packageInfo.version);
            if (version < 1.0 || version > 3.0) {
                errors.push('إصدار غير مدعوم');
            }
        }

        // التحقق من البيانات الأساسية
        if (data.citizens) {
            const invalidRecords = data.citizens.filter(citizen => 
                !citizen.id || !citizen.firstNameAr || !citizen.familyNameAr
            );
            
            if (invalidRecords.length > 0) {
                errors.push(`${invalidRecords.length} سجل يحتوي على بيانات ناقصة`);
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // إنشاء نسخة احتياطية قبل الاستيراد
    async _createPreImportBackup() {
        try {
            console.log('💾 إنشاء نسخة احتياطية قبل الاستيراد...');
            
            const citizens = await citizensDB.getAllCitizens();
            const backupData = {
                timestamp: new Date().toISOString(),
                type: 'pre-import-backup',
                recordCount: citizens.length,
                citizens
            };

            const backupKey = `pre_import_backup_${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify(backupData));
            
            console.log('✅ تم إنشاء نسخة احتياطية قبل الاستيراد');
            return backupKey;

        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            throw error;
        }
    }

    // تسجيل عملية المزامنة
    _recordSyncOperation(type, details) {
        const operation = {
            type,
            timestamp: new Date().toISOString(),
            details
        };

        this.syncHistory.push(operation);
        
        // الاحتفاظ بآخر 50 عملية فقط
        if (this.syncHistory.length > 50) {
            this.syncHistory = this.syncHistory.slice(-50);
        }

        // حفظ في localStorage
        localStorage.setItem('syncHistory', JSON.stringify(this.syncHistory));
        localStorage.setItem('lastSyncTime', operation.timestamp);
        
        this.lastSyncTime = operation.timestamp;
    }

    // توليد checksum للبيانات
    _generateChecksum(data) {
        const str = JSON.stringify(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString(16);
    }

    // الحصول على معلومات المنصة
    _getPlatformInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            timestamp: new Date().toISOString()
        };
    }

    // الحصول على تاريخ المزامنة
    getSyncHistory() {
        return this.syncHistory;
    }

    // الحصول على آخر وقت مزامنة
    getLastSyncTime() {
        return this.lastSyncTime;
    }
}

// إنشاء مثيل عام
const dataSyncManager = new DataSyncManager();
