═══════════════════════════════════════════════════════════════════════════════
                    طريقة إخفاء معلومات المتصفح عند الطباعة
═══════════════════════════════════════════════════════════════════════════════

📋 المشكلة:
عند طباعة الوثائق من المتصفح، قد تظهر معلومات إضافية مثل:
- رابط الصفحة (URL)
- تاريخ ووقت الطباعة
- رقم الصفحة (1/1)
- اسم الموقع

═══════════════════════════════════════════════════════════════════════════════

🌐 Google Chrome:
═══════════════════════════════════════════════════════════════════════════════

الطريقة الأولى - من إعدادات الطباعة:
1. اضغط Ctrl+P أو اذهب إلى القائمة → Print
2. في نافذة الطباعة، ابحث عن "More settings"
3. قم بإلغاء تحديد "Headers and footers"
4. اطبع الوثيقة

الطريقة الثانية - من إعدادات Chrome:
1. اذه<PERSON> إلى Settings (الإعدادات)
2. اضغط على "Advanced" (متقدم)
3. <PERSON><PERSON><PERSON><PERSON> إلى "Printing" (الطباعة)
4. قم بإلغاء تحديد "Print headers and footers"

═══════════════════════════════════════════════════════════════════════════════

🦊 Mozilla Firefox:
═══════════════════════════════════════════════════════════════════════════════

الطريقة الأولى - من إعدادات الطباعة:
1. اضغط Ctrl+P
2. في نافذة الطباعة، اذهب إلى "Page Setup"
3. في تبويب "Margins & Header/Footer"
4. اجعل جميع الحقول فارغة:
   - Header Left: --blank--
   - Header Center: --blank--
   - Header Right: --blank--
   - Footer Left: --blank--
   - Footer Center: --blank--
   - Footer Right: --blank--

الطريقة الثانية - من about:config:
1. اكتب في شريط العنوان: about:config
2. ابحث عن: print.print_headerleft
3. اجعل القيمة فارغة
4. كرر نفس الشيء مع:
   - print.print_headerright
   - print.print_headercenter
   - print.print_footerleft
   - print.print_footerright
   - print.print_footercenter

═══════════════════════════════════════════════════════════════════════════════

🌊 Microsoft Edge:
═══════════════════════════════════════════════════════════════════════════════

1. اضغط Ctrl+P
2. في نافذة الطباعة، اضغط على "More settings"
3. قم بإلغاء تحديد "Headers and footers"
4. اطبع الوثيقة

أو:
1. اذهب إلى Settings (النقاط الثلاث → Settings)
2. ابحث عن "Printing"
3. قم بإلغاء تحديد "Print headers and footers"

═══════════════════════════════════════════════════════════════════════════════

🍎 Safari (Mac):
═══════════════════════════════════════════════════════════════════════════════

1. اضغط Cmd+P
2. في نافذة الطباعة، اضغط على "Show Details"
3. اذهب إلى قائمة "Safari"
4. قم بإلغاء تحديد:
   - "Print headers and footers"
   - "Print backgrounds"

═══════════════════════════════════════════════════════════════════════════════

🐧 Linux (مختلف المتصفحات):
═══════════════════════════════════════════════════════════════════════════════

Chrome/Chromium:
- نفس خطوات Chrome على Windows

Firefox:
- نفس خطوات Firefox على Windows

═══════════════════════════════════════════════════════════════════════════════

📱 المتصفحات على الهاتف:
═══════════════════════════════════════════════════════════════════════════════

Chrome Mobile:
1. اضغط على القائمة (النقاط الثلاث)
2. اختر "Print"
3. في إعدادات الطباعة، ابحث عن "Headers and footers"
4. قم بإلغاء التحديد

Firefox Mobile:
1. اضغط على القائمة
2. اختر "Print"
3. في الإعدادات، قم بإلغاء تحديد "Headers and footers"

═══════════════════════════════════════════════════════════════════════════════

💡 نصائح إضافية:
═══════════════════════════════════════════════════════════════════════════════

1. استخدم "Print to PDF":
   - اطبع كـ PDF أولاً
   - ثم اطبع ملف PDF (تحكم أكبر)

2. استخدم وضع "Kiosk Mode":
   - في Chrome: اضغط F11 قبل الطباعة
   - يقلل من ظهور معلومات إضافية

3. تحقق من إعدادات الطابعة:
   - بعض الطابعات تضيف معلومات تلقائياً
   - تحقق من إعدادات برنامج الطابعة

4. استخدم CSS في الكود:
   - تم إضافة @page في الكود لإخفاء المعلومات
   - يعمل مع معظم المتصفحات الحديثة

═══════════════════════════════════════════════════════════════════════════════

🔧 حل المشاكل الشائعة:
═══════════════════════════════════════════════════════════════════════════════

المشكلة: مازالت المعلومات تظهر
الحل: 
- تأكد من حفظ الإعدادات
- أعد تشغيل المتصفح
- جرب متصفح آخر

المشكلة: الإعدادات تعود للوضع الافتراضي
الحل:
- تأكد من أن المتصفح محدث
- تحقق من إعدادات المؤسسة (في بيئة العمل)

المشكلة: لا توجد خيارات Headers and footers
الحل:
- حدث المتصفح لآخر إصدار
- جرب الطباعة كـ PDF

═══════════════════════════════════════════════════════════════════════════════

📞 للدعم الفني:
═══════════════════════════════════════════════════════════════════════════════

إذا واجهت مشاكل:
1. تأكد من إصدار المتصفح
2. جرب متصفح مختلف
3. استخدم الطباعة كـ PDF كحل بديل
4. تحقق من إعدادات نظام التشغيل

═══════════════════════════════════════════════════════════════════════════════

تم إنشاء هذا الملف لمساعدتك في الحصول على طباعة نظيفة بدون معلومات إضافية.
آخر تحديث: 2024

═══════════════════════════════════════════════════════════════════════════════
