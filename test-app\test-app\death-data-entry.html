<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخة موجزة من رسم الوفاة - مكتب الحالة المدنية أيير</title>
    <style>
        /* ===== GENERAL STYLES ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Amiri', 'Times New Roman', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
            color: #2c3e50;
            line-height: 1.6;
        }

        /* ===== CONTAINER & LAYOUT ===== */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* ===== HEADER STYLES ===== */
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #c41e3a 0%, #006233 50%, #c41e3a 100%);
        }

        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 0.85em;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .header-main {
            padding: 20px 0;
            text-align: center;
        }

        .header-main h1 {
            font-size: 2.2em;
            margin: 0 0 10px 0;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-main p {
            font-size: 1.1em;
            opacity: 0.95;
            margin-bottom: 20px;
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            background: rgba(255,255,255,0.15);
            border-radius: 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.25);
            transform: translateY(-2px);
        }

        /* ===== MAIN CONTENT LAYOUT ===== */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }

        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .right-panel {
            display: flex;
            flex-direction: column;
        }

        /* ===== FORM STYLES ===== */
        .form-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
            margin-bottom: 15px;
        }

        .form-section h2 {
            color: #2c3e50;
            margin-bottom: 18px;
            font-size: 1.2em;
            font-weight: 700;
            text-align: center;
            border-bottom: 2px solid #c41e3a;
            padding-bottom: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 0.9em;
        }

        .required {
            color: #e74c3c;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 0.9em;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #c41e3a;
            box-shadow: 0 0 0 3px rgba(196, 30, 58, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* ===== BUTTON STYLES ===== */
        .btn {
            padding: 8px 18px;
            border: none;
            border-radius: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 3px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .form-actions {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 18px;
            gap: 8px;
        }

        /* ===== ALERT STYLES ===== */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* ===== CERTIFICATE PREVIEW STYLES ===== */
        .preview-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 18px;
            border: 1px solid #e9ecef;
            height: fit-content;
        }

        .certificate-preview {
            min-height: 500px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            background: white;
            margin-bottom: 15px;
            overflow: auto;
            position: relative;
            padding: 12px;
        }

        .preview-placeholder {
            text-align: center;
            color: #6c757d;
            padding: 30px;
        }

        /* ===== CERTIFICATE CONTENT STYLES ===== */
        .certificate-content {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 15px;
            font-family: 'Times New Roman', serif;
            line-height: 1.5;
            background: white;
            border: 2px solid #000;
            min-height: 450px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        }

        .certificate-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 5px;
            padding-bottom: 5px;
        }

        .header-left {
            text-align: left;
            font-size: 11px;
            line-height: 1.5;
            font-weight: 500;
        }

        .header-right {
            text-align: right;
            font-size: 11px;
            line-height: 1.5;
            font-weight: 500;
        }

        .certificate-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 4px 0 4px 0;
            color: #000;
        }

        .certificate-body {
            font-size: 13px;
            line-height: 1.6;
            text-align: right;
            margin: 4px 0;
        }

        .field-line {
            margin-bottom: 6px;
            display: flex;
            align-items: baseline;
            width: 100%;
        }

        .field-label {
            font-weight: 600;
            margin-left: 8px;
            white-space: nowrap;
            min-width: 100px;
            font-size: 13px;
        }

        .field-value {
            flex: 1;
            min-height: 20px;
            padding: 1px 4px;
            color: #2c3e50;
            font-size: 13px;
        }



        .certificate-footer {
            margin-top: 35px;
            display: flex;
            justify-content: space-between;
            align-items: end;
            font-size: 10px;
            padding-top: 15px;
        }

        .footer-left {
            text-align: left;
            line-height: 1.3;
        }

        .footer-right {
            text-align: right;
            line-height: 1.3;
        }

        .certification-text {
            text-align: center;
            margin: 25px 0;
            font-size: 12px;
            line-height: 1.6;
            font-weight: 500;
            color: #333;
            padding: 12px;
            background: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .nav-links {
                flex-direction: column;
                align-items: center;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                المملكة المغربية - وزارة الداخلية - إقليم أسفي
            </div>
            <div class="header-main">
                <h1>⚱️ نسخة موجزة من رسم الوفاة</h1>
                <p>مكتب الحالة المدنية - أيير • إدخال بيانات المتوفى</p>

                <div class="nav-links">
                    <a href="main-dashboard.html" class="nav-link">🏠 الصفحة الرئيسية</a>
                    <a href="citizens-database-indexeddb.html" class="nav-link">🗃️ إدارة البيانات</a>
                    <a href="search-citizens.html" class="nav-link">🔍 البحث في السجلات</a>
                    <a href="dual-birth-certificate.html" class="nav-link">📜 عقود الازدياد</a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Panel - Forms -->
            <div class="left-panel">
                <div id="alertContainer"></div>

                <!-- معلومات الوفاة الأساسية -->
                <div class="form-section">
                    <h2>⚱️ معلومات الوفاة الأساسية</h2>

                    <form id="deathForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="deathPlace">توفي(ت) بـ <span class="required">*</span>:</label>
                                <input type="text" id="deathPlace" name="deathPlace" required placeholder="مكان الوفاة">
                            </div>

                            <div class="form-group">
                                <label for="deathDate">في <span class="required">*</span>:</label>
                                <input type="date" id="deathDate" name="deathDate" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="deathTime">الساعة:</label>
                                <input type="time" id="deathTime" name="deathTime" placeholder="ساعة الوفاة">
                            </div>

                            <div class="form-group">
                                <label for="deathCause">سبب الوفاة:</label>
                                <input type="text" id="deathCause" name="deathCause" placeholder="سبب الوفاة">
                            </div>
                        </div>
                    </form>
                </div>

            <!-- البيانات الشخصية للمتوفى -->
            <div class="form-section">
                <h2>👤 البيانات الشخصية للمتوفى</h2>

                <div class="form-row">
                    <div class="form-group">
                        <label for="personalName">الاسم الشخصي <span class="required">*</span>:</label>
                        <input type="text" id="personalName" name="personalName" required placeholder="الاسم الشخصي للمتوفى">
                    </div>

                    <div class="form-group">
                        <label for="familyName">الاسم العائلي <span class="required">*</span>:</label>
                        <input type="text" id="familyName" name="familyName" required placeholder="الاسم العائلي للمتوفى">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="birthDate">تاريخ الازدياد:</label>
                        <input type="date" id="birthDate" name="birthDate">
                    </div>

                    <div class="form-group">
                        <label for="birthPlace">مكان الازدياد:</label>
                        <input type="text" id="birthPlace" name="birthPlace" placeholder="مكان الازدياد">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="age">العمر عند الوفاة:</label>
                        <input type="number" id="age" name="age" placeholder="العمر بالسنوات" min="0">
                    </div>

                    <div class="form-group">
                        <label for="gender">الجنس:</label>
                        <select id="gender" name="gender">
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="profession">مهنته (ها):</label>
                        <input type="text" id="profession" name="profession" placeholder="مهنة المتوفى">
                    </div>

                    <div class="form-group">
                        <label for="residence">الساكن (ة) بـ:</label>
                        <input type="text" id="residence" name="residence" placeholder="عنوان السكن">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="maritalStatus">الحالة المدنية:</label>
                        <select id="maritalStatus" name="maritalStatus">
                            <option value="">اختر الحالة المدنية</option>
                            <option value="أعزب">أعزب</option>
                            <option value="عزباء">عزباء</option>
                            <option value="متزوج">متزوج</option>
                            <option value="متزوجة">متزوجة</option>
                            <option value="مطلق">مطلق</option>
                            <option value="مطلقة">مطلقة</option>
                            <option value="أرمل">أرمل</option>
                            <option value="أرملة">أرملة</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="nationality">الجنسية:</label>
                        <input type="text" id="nationality" name="nationality" placeholder="الجنسية" value="مغربية">
                    </div>
                </div>
            </div>

            <!-- بيانات الوالدين -->
            <div class="form-section">
                <h2>👨‍👩‍👧‍👦 بيانات الوالدين</h2>

                <div class="form-row">
                    <div class="form-group">
                        <label for="fatherName">والده:</label>
                        <input type="text" id="fatherName" name="fatherName" placeholder="اسم الوالد">
                    </div>

                    <div class="form-group">
                        <label for="motherName">والدته:</label>
                        <input type="text" id="motherName" name="motherName" placeholder="اسم الوالدة">
                    </div>
                </div>
            </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="button" class="btn btn-primary" onclick="updatePreview()">🔄 تحديث المعاينة</button>
                    <button type="button" class="btn btn-success" onclick="saveDeathRecord()">💾 حفظ البيانات</button>
                    <button type="button" class="btn btn-warning">🖨️ طباعة الشهادة</button>
                    <button type="button" class="btn btn-primary" onclick="clearForm()">🗑️ مسح النموذج</button>
                </div>
            </div>

            <!-- Right Panel - Certificate Preview -->
            <div class="right-panel">
                <div class="preview-section">
                    <h2 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #c41e3a; padding-bottom: 8px; text-align: center; font-size: 1.2em;">👁️ معاينة الشهادة</h2>

                    <div class="certificate-preview" id="certificatePreview">
                        <!-- Certificate Content -->
                        <div class="certificate-content">
                            <!-- Header -->
                            <div class="certificate-header">
                                <div class="header-left">
                                    المملكة المغربية<br>
                                    وزارة الداخلية<br>
                                    (إقليم أسفي)<br>
                                    جماعة أيير<br>
                                    مكتب الحالة المدنية :<br>
                                    ............./............. : عقد رقم
                                </div>
                                <div class="header-right">
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                    <br>
                                </div>
                            </div>

                            <!-- Title -->
                            <div class="certificate-title">
                                نسخة موجزة من رسم الوفاة
                            </div>

                            <!-- Body -->
                            <div class="certificate-body">
                                <div class="field-line">
                                    <span class="field-label">توفي(ت) بـ :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">في :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الاسم الشخصي :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الاسم العائلي :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">تاريخ الازدياد :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">مكان الازدياد :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">مهنته (ها) :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">الساكن (ة) بـ :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">والده :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">والدته :</span>
                                    <span class="field-value">...............................................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">نشهد بصفتنا ضابطا للحالة المدنية نحن :</span>
                                    <span class="field-value">.............................................................................</span>
                                </div>

                                <div class="field-line">
                                    <span class="field-label">بمطابقة هذه النسخة لما هو مضمن في سجلات الحالة المدنية بالمكتب المذكور</span>
                                    <span class="field-value"></span>
                                </div>
                            </div>

                            <!-- Footer -->
                            <div class="certificate-footer">
                                <div class="footer-right">
                                    أيير : في ................................................................<br>
                                    ضابط الحالة المدنية
                                </div>
                                <div class="footer-left">
                                    طابع مكتب الحالة المدنية<br>
                                    ضابط الحالة المدنية
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Actions -->
                    <div style="text-align: center; margin-top: 12px;">
                        <button type="button" class="btn btn-success" disabled style="font-size: 0.85em; padding: 6px 14px;">🖨️ طباعة الشهادة</button>
                        <button type="button" class="btn btn-primary" disabled style="font-size: 0.85em; padding: 6px 14px;">📄 تصدير PDF</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        /* ===== JAVASCRIPT FUNCTIONS ===== */

        // متغيرات قاعدة البيانات
        let db;
        const DB_NAME = 'CitizensDatabase'; // تطابق مع النظام الأصلي
        const DB_VERSION = 2; // زيادة الإصدار لإضافة حقول الوفاة
        const STORE_NAME = 'citizens';

        // متغيرات وضع التعديل
        let editingId = null;
        let isEditMode = false;

        // تهيئة قاعدة البيانات
        function initDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, DB_VERSION);

                request.onerror = () => reject(request.error);
                request.onsuccess = () => {
                    db = request.result;
                    resolve(db);
                };

                request.onupgradeneeded = (event) => {
                    db = event.target.result;

                    // إنشاء المخزن إذا لم يكن موجوداً
                    if (!db.objectStoreNames.contains(STORE_NAME)) {
                        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id', autoIncrement: true });
                        store.createIndex('personalName', 'personalName', { unique: false });
                        store.createIndex('familyName', 'familyName', { unique: false });
                        store.createIndex('isDeceased', 'isDeceased', { unique: false });
                    }
                };
            });
        }

        // تعيين تاريخ اليوم كافتراضي
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('deathDate').value = today;

            // تهيئة قاعدة البيانات
            initDB().then(() => {
                // فحص وضع التعديل
                checkEditMode();
            }).catch(error => {
                console.error('خطأ في تهيئة قاعدة البيانات:', error);
                showAlert('خطأ في تهيئة قاعدة البيانات', 'error');
            });
        });

        // فحص وضع التعديل وتحميل البيانات
        function checkEditMode() {
            const urlParams = new URLSearchParams(window.location.search);
            const editParam = urlParams.get('edit');
            const citizenId = urlParams.get('id');

            if (editParam === 'true' && citizenId) {
                console.log('🔄 وضع التعديل مفعل للمواطن:', citizenId);
                editingId = citizenId;
                isEditMode = true;
                loadCitizenData(urlParams);
            }
        }

        // تحميل بيانات المواطن للتعديل
        function loadCitizenData(urlParams) {
            try {
                console.log('📋 تحميل بيانات المواطن للتعديل...');

                // تحميل البيانات الشخصية
                document.getElementById('personalName').value = urlParams.get('personalName') || '';
                document.getElementById('familyName').value = urlParams.get('familyName') || '';
                document.getElementById('birthDate').value = urlParams.get('birthDate') || '';
                document.getElementById('birthPlace').value = urlParams.get('birthPlace') || '';
                document.getElementById('gender').value = urlParams.get('gender') || '';
                document.getElementById('profession').value = urlParams.get('profession') || '';
                document.getElementById('residence').value = urlParams.get('residence') || '';
                document.getElementById('maritalStatus').value = urlParams.get('maritalStatus') || '';
                document.getElementById('nationality').value = urlParams.get('nationality') || 'مغربية';
                document.getElementById('fatherName').value = urlParams.get('fatherName') || '';
                document.getElementById('motherName').value = urlParams.get('motherName') || '';
                document.getElementById('age').value = urlParams.get('age') || '';

                // تحميل بيانات الوفاة
                document.getElementById('deathPlace').value = urlParams.get('deathPlace') || '';
                document.getElementById('deathDate').value = urlParams.get('deathDate') || '';
                document.getElementById('deathTime').value = urlParams.get('deathTime') || '';
                document.getElementById('deathCause').value = urlParams.get('deathCause') || '';

                // تحديث واجهة المستخدم لوضع التعديل
                updateUIForEditMode();

                // تحديث المعاينة
                updatePreview();

                console.log('✅ تم تحميل بيانات المواطن بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات المواطن:', error);
                showAlert('خطأ في تحميل بيانات المواطن', 'error');
            }
        }

        // تحديث واجهة المستخدم لوضع التعديل
        function updateUIForEditMode() {
            // تحديث عنوان الصفحة
            const headerTitle = document.querySelector('.header-main h1');
            if (headerTitle) {
                headerTitle.innerHTML = '✏️ تعديل شهادة الوفاة';
                headerTitle.style.color = '#f39c12';
            }

            // تحديث عنوان القسم
            const sectionTitle = document.querySelector('.form-section h2');
            if (sectionTitle) {
                sectionTitle.innerHTML = '✏️ تعديل معلومات الوفاة';
                sectionTitle.style.color = '#f39c12';
            }

            // تحديث زر الحفظ
            const saveButton = document.querySelector('button[onclick="saveDeathRecord()"]');
            if (saveButton) {
                saveButton.innerHTML = '💾 تحديث البيانات';
                saveButton.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
            }

            // إظهار رسالة التعديل
            showAlert('📝 وضع التعديل مفعل - يمكنك الآن تعديل بيانات الوفاة', 'success');
        }

        // وظيفة تحديث المعاينة
        function updatePreview() {
            // الحصول على القيم من النموذج
            const deathPlace = document.getElementById('deathPlace').value || '';
            const deathDate = document.getElementById('deathDate').value || '';
            const personalName = document.getElementById('personalName').value || '';
            const familyName = document.getElementById('familyName').value || '';
            const birthDate = document.getElementById('birthDate').value || '';
            const birthPlace = document.getElementById('birthPlace').value || '';
            const profession = document.getElementById('profession').value || '';
            const residence = document.getElementById('residence').value || '';
            const fatherName = document.getElementById('fatherName').value || '';
            const motherName = document.getElementById('motherName').value || '';

            // تحديث الحقول في المعاينة
            const fieldValues = document.querySelectorAll('.certificate-content .field-value');

            if (fieldValues[0]) fieldValues[0].textContent = deathPlace;
            if (fieldValues[1]) fieldValues[1].textContent = deathDate;
            if (fieldValues[2]) fieldValues[2].textContent = personalName;
            if (fieldValues[3]) fieldValues[3].textContent = familyName;
            if (fieldValues[4]) fieldValues[4].textContent = birthDate;
            if (fieldValues[5]) fieldValues[5].textContent = birthPlace;
            if (fieldValues[6]) fieldValues[6].textContent = profession;
            if (fieldValues[7]) fieldValues[7].textContent = residence;
            if (fieldValues[8]) fieldValues[8].textContent = fatherName;
            if (fieldValues[9]) fieldValues[9].textContent = motherName;

            // إظهار رسالة نجاح
            showAlert('تم تحديث المعاينة بنجاح!', 'success');
        }

        // وظيفة حفظ بيانات الوفاة
        async function saveDeathRecord() {
            try {
                // التحقق من صحة البيانات المطلوبة
                const deathPlace = document.getElementById('deathPlace').value.trim();
                const deathDate = document.getElementById('deathDate').value;
                const personalName = document.getElementById('personalName').value.trim();
                const familyName = document.getElementById('familyName').value.trim();

                if (!deathPlace || !deathDate || !personalName || !familyName) {
                    showAlert('يرجى ملء جميع الحقول المطلوبة (مكان الوفاة، تاريخ الوفاة، الاسم الشخصي، الاسم العائلي)', 'error');
                    return;
                }

                // جمع جميع البيانات بالتنسيق المتوافق مع النظام الأصلي
                const deathRecord = {
                    // البيانات الأساسية (متوافقة مع النظام الأصلي)
                    id: isEditMode ? editingId : Date.now().toString(), // استخدام المعرف الموجود في وضع التعديل
                    firstNameAr: personalName,
                    familyNameAr: familyName,
                    personalName: personalName, // للتوافق مع النظام القديم
                    familyName: familyName, // للتوافق مع النظام القديم
                    birthDate: document.getElementById('birthDate').value || null,
                    birthPlaceAr: document.getElementById('birthPlace').value.trim() || null,
                    birthPlace: document.getElementById('birthPlace').value.trim() || null, // للتوافق
                    gender: document.getElementById('gender').value || null,
                    profession: document.getElementById('profession').value.trim() || null,
                    residence: document.getElementById('residence').value.trim() || null,
                    maritalStatus: document.getElementById('maritalStatus').value || null,
                    nationality: document.getElementById('nationality').value.trim() || 'مغربية',
                    fatherNameAr: document.getElementById('fatherName').value.trim() || null,
                    motherNameAr: document.getElementById('motherName').value.trim() || null,
                    fatherName: document.getElementById('fatherName').value.trim() || null, // للتوافق
                    motherName: document.getElementById('motherName').value.trim() || null, // للتوافق
                    age: document.getElementById('age').value || null,

                    // بيانات الوفاة
                    isDeceased: true,
                    deathInfo: {
                        deathPlace: deathPlace,
                        deathDate: deathDate,
                        deathTime: document.getElementById('deathTime').value || null,
                        deathCause: document.getElementById('deathCause').value.trim() || null,
                        registrationDate: new Date().toISOString().split('T')[0],
                        registrationTime: new Date().toLocaleTimeString('ar-MA'),
                        certificationOfficer: 'ضابط الحالة المدنية - أيير'
                    },

                    // معلومات إضافية
                    recordType: 'death',
                    createdAt: isEditMode ? undefined : new Date().toISOString(), // لا نغير تاريخ الإنشاء في التعديل
                    lastModified: new Date().toISOString()
                };

                // حفظ أو تحديث في قاعدة البيانات
                if (isEditMode) {
                    await updateInDatabase(deathRecord);
                    showAlert('تم تحديث بيانات الوفاة بنجاح!', 'success');
                } else {
                    await saveToDatabase(deathRecord);
                    showAlert('تم حفظ بيانات الوفاة بنجاح!', 'success');
                }

                // تحديث المعاينة
                updatePreview();

            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showAlert('حدث خطأ أثناء حفظ البيانات: ' + error.message, 'error');
            }
        }

        // وظيفة حفظ البيانات في قاعدة البيانات
        function saveToDatabase(record) {
            return new Promise((resolve, reject) => {
                if (!db) {
                    reject(new Error('قاعدة البيانات غير متاحة'));
                    return;
                }

                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const store = transaction.objectStore(STORE_NAME);
                const request = store.add(record);

                request.onsuccess = () => {
                    console.log('تم حفظ السجل بنجاح، ID:', request.result);
                    resolve(request.result);
                };

                request.onerror = () => {
                    reject(new Error('فشل في حفظ البيانات: ' + request.error));
                };

                transaction.onerror = () => {
                    reject(new Error('خطأ في المعاملة: ' + transaction.error));
                };
            });
        }

        // وظيفة تحديث البيانات في قاعدة البيانات
        function updateInDatabase(record) {
            return new Promise((resolve, reject) => {
                if (!db) {
                    reject(new Error('قاعدة البيانات غير متاحة'));
                    return;
                }

                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const store = transaction.objectStore(STORE_NAME);

                // تحويل المعرف للنوع الصحيح إذا لزم الأمر
                let searchId = record.id;
                if (typeof record.id === 'string' && !isNaN(record.id)) {
                    searchId = parseInt(record.id);
                    record.id = searchId; // تحديث المعرف في السجل
                }

                const request = store.put(record);

                request.onsuccess = () => {
                    console.log('تم تحديث السجل بنجاح، ID:', record.id);
                    resolve(record.id);
                };

                request.onerror = () => {
                    reject(new Error('فشل في تحديث البيانات: ' + request.error));
                };

                transaction.onerror = () => {
                    reject(new Error('خطأ في المعاملة: ' + transaction.error));
                };
            });
        }

        // وظيفة مسح النموذج
        function clearForm() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المدخلة؟')) {
                document.getElementById('deathForm').reset();

                // إعادة تعيين التاريخ الافتراضي
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('deathDate').value = today;
                document.getElementById('nationality').value = 'مغربية';

                // مسح المعاينة
                const fieldValues = document.querySelectorAll('.certificate-content .field-value');
                fieldValues.forEach(field => {
                    field.textContent = '';
                });

                showAlert('تم مسح النموذج بنجاح', 'success');
            }
        }

        // وظيفة إظهار الرسائل
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }
    </script>
</body>
</html>
